# Environment Configuration Guide

This guide explains how to configure and switch between different API environments in the Agent Report UI application.

## Quick Start

1. **Copy the environment template:**
   ```bash
   cp .env.example .env
   ```

2. **Switch to your desired environment:**
   ```bash
   # For local development
   npm run env:local
   
   # For production
   npm run env:production
   
   # For staging
   npm run env:staging
   ```

3. **Restart your development server:**
   ```bash
   npm run dev
   ```

## Available Environments

| Environment | API Base URL | Node Environment | Description |
|-------------|--------------|------------------|-------------|
| **Local** | `http://localhost:8000` | `development` | Local development server |
| **Production** | `https://agentreportbackend.vercel.app` | `production` | Live production server |
| **Staging** | `https://staging-agentreportbackend.vercel.app` | `staging` | Staging/testing server |

## Environment Switching Commands

The application provides convenient npm scripts for switching between environments:

```bash
# Switch environments
npm run env:local        # Switch to local development
npm run env:production   # Switch to production
npm run env:staging      # Switch to staging

# Check current environment
npm run env:status       # Show current environment configuration

# Get help
npm run env:help         # Show detailed help information
```

## Manual Configuration

You can also manually edit the `.env` file:

```bash
# For local development
NEXT_PUBLIC_API_BASE="http://localhost:8000"
NODE_ENV="development"

# For production
NEXT_PUBLIC_API_BASE="https://agentreportbackend.vercel.app"
NODE_ENV="production"

# For staging
NEXT_PUBLIC_API_BASE="https://staging-agentreportbackend.vercel.app"
NODE_ENV="staging"
```

## How It Works

### 1. Environment Variables

The application uses these environment variables:

- **`NEXT_PUBLIC_API_BASE`**: The base URL for your backend API (without `/api` suffix)
- **`NODE_ENV`**: The Node.js environment (`development`, `production`, or `staging`)

### 2. API URL Construction

The `getApiBaseUrl()` function automatically:
- Reads the `NEXT_PUBLIC_API_BASE` environment variable
- Removes trailing slashes
- Appends `/api` to create the full API base URL
- Provides fallback defaults if not configured

### 3. Centralized Configuration

All API calls use the centralized configuration through:
- `src/lib/constants/api.ts` - API endpoints and configuration
- `src/lib/api/client.ts` - Axios client with automatic base URL
- `src/providers/ApiContext.tsx` - React context for API calls

## Development Workflow

### Starting a New Feature

1. **Switch to local environment:**
   ```bash
   npm run env:local
   npm run dev
   ```

2. **Start your local backend server** (usually on port 8000)

3. **Develop and test** with your local backend

### Testing with Production Data

1. **Switch to production environment:**
   ```bash
   npm run env:production
   npm run dev
   ```

2. **Test your frontend** against the live backend

### Deploying to Staging

1. **Switch to staging environment:**
   ```bash
   npm run env:staging
   npm run build
   ```

2. **Deploy to your staging environment**

## Troubleshooting

### Common Issues

1. **"API calls failing after environment switch"**
   - **Solution**: Restart your development server (`npm run dev`)
   - **Reason**: Environment variables are loaded at startup

2. **"Using wrong API URL"**
   - **Check current environment**: `npm run env:status`
   - **Verify .env file**: Check `NEXT_PUBLIC_API_BASE` value
   - **Clear browser cache**: Environment changes may be cached

3. **"Environment script not working"**
   - **Check Node.js version**: Ensure Node.js is installed
   - **Verify file permissions**: Make sure `scripts/switch-env.js` is readable
   - **Run directly**: `node scripts/switch-env.js help`

### Debug Information

The application provides debug information in development mode:

```javascript
// Check current API base URL in browser console
console.log('API Base URL:', getApiBaseUrl());

// Environment validation warnings appear in console
// Look for messages starting with 🔧, 🔗, ⚠️, or ❌
```

## Advanced Configuration

### Custom Environment

You can create custom environments by:

1. **Editing the switch script** (`scripts/switch-env.js`)
2. **Adding your environment** to the `ENVIRONMENTS` object
3. **Adding a new npm script** in `package.json`

Example:
```javascript
// In scripts/switch-env.js
const ENVIRONMENTS = {
  // ... existing environments
  custom: {
    NEXT_PUBLIC_API_BASE: 'https://my-custom-backend.com',
    NODE_ENV: 'development',
    description: 'Custom Backend Server'
  }
};
```

```json
// In package.json
{
  "scripts": {
    "env:custom": "node scripts/switch-env.js custom"
  }
}
```

### Environment Validation

The application automatically validates:
- Valid NODE_ENV values
- Proper URL format for NEXT_PUBLIC_API_BASE
- Environment variable presence

Validation warnings appear in the browser console during development.

## Security Notes

1. **Never commit sensitive data** to `.env` files
2. **Use environment-specific secrets** for production
3. **The `NEXT_PUBLIC_` prefix** exposes variables to the client-side
4. **Restart required** after changing environment variables

## Support

If you encounter issues with environment configuration:

1. **Check the current status**: `npm run env:status`
2. **View help information**: `npm run env:help`
3. **Verify your .env file** matches the examples above
4. **Restart your development server** after changes
