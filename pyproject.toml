[project]
name = "agentreport"
version = "0.1.0"
description = "Query databases using natural language!"
readme = "README.md"
requires-python = "==3.12.*"
dependencies = [
    "aiobotocore>=2.22.0",
    "authlib>=1.6.0",
    "bcrypt==4.0.1",
    "boto3>=1.37.3",
    "fastapi[standard]>=0.115.12",
    "matplotlib>=3.10.3",
    "pandas>=2.2.3",
    "passlib>=1.7.4",
    "pg8000>=1.31.2",
    "pytest>=8.0.0",
    "pytest-asyncio>=0.24.0",
    "pytest-mock>=3.14.0",
    "httpx>=0.27.0",
    "python-jose>=3.5.0",
    "seaborn>=0.13.2",
    "sqlmodel>=0.0.24",
    "xgboost>=3.0.2",
]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--disable-warnings"
]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow running tests",
    "database: Tests requiring database connection",
    "agent: Agent-specific tests"
]
asyncio_mode = "auto"
