{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "env:local": "node scripts/switch-env.js local", "env:production": "node scripts/switch-env.js production", "env:staging": "node scripts/switch-env.js staging", "env:help": "node scripts/switch-env.js help", "env:status": "node scripts/switch-env.js status"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.76.2", "@types/react-grid-layout": "^1.3.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.15.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "motion": "^12.15.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-grid-layout": "^1.5.1", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "recharts": "^3.0.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "tailwind-merge": "^3.3.0", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.10", "@types/lodash": "^4.17.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "postcss": "^8.5.3", "tailwindcss": "^3.4.0", "tw-animate-css": "^1.3.0", "typescript": "^5"}}