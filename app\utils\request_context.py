"""Request Context Management

This module provides request context management for correlation ID tracking
and request information extraction across the authentication system.
"""

import uuid
from contextvars import Con<PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware


# Context variables for request tracking
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
request_context_var: ContextVar[Optional[Dict[str, Any]]] = ContextVar('request_context', default=None)


class RequestContextMiddleware(BaseHTTPMiddleware):
    """Middleware to extract and store request context information."""
    
    async def dispatch(self, request: Request, call_next):
        """Process request and extract context information.
        
        Args:
            request: FastAPI request object
            call_next: Next middleware in chain
            
        Returns:
            Response with correlation ID header
        """
        # Extract or generate correlation ID
        correlation_id = self._get_correlation_id(request)
        
        # Extract request context
        context = {
            "correlation_id": correlation_id,
            "ip_address": request.client.host if request.client else None,
            "user_agent": request.headers.get("user-agent"),
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "query_params": dict(request.query_params),
            "headers": dict(request.headers)
        }
        
        # Set context variables
        correlation_id_var.set(correlation_id)
        request_context_var.set(context)
        
        # Process request
        response = await call_next(request)
        
        # Add correlation ID to response headers
        response.headers["X-Correlation-ID"] = correlation_id
        
        return response
    
    def _get_correlation_id(self, request: Request) -> str:
        """Extract or generate correlation ID from request.
        
        Args:
            request: FastAPI request object
            
        Returns:
            str: Correlation ID for the request
        """
        # Check various header names for correlation ID
        correlation_id = (
            request.headers.get("X-Correlation-ID") or
            request.headers.get("X-Request-ID") or
            request.headers.get("X-Trace-ID") or
            str(uuid.uuid4())
        )
        
        return correlation_id


def get_correlation_id(request: Request = None) -> str:
    """Get the current request's correlation ID.

    Args:
        request: Optional FastAPI request object

    Returns:
        str: Current correlation ID
    """
    if request:
        # Extract correlation ID from request headers
        correlation_id = (
            request.headers.get("X-Correlation-ID") or
            request.headers.get("X-Request-ID") or
            request.headers.get("X-Trace-ID") or
            str(uuid.uuid4())
        )
        return correlation_id
    else:
        # Get from context variable
        return correlation_id_var.get() or str(uuid.uuid4())


def get_request_context() -> Optional[Dict[str, Any]]:
    """Get the current request's context information.
    
    Returns:
        Optional[Dict[str, Any]]: Current request context or None if not set
    """
    return request_context_var.get()


def get_client_ip() -> Optional[str]:
    """Get the current request's client IP address.
    
    Returns:
        Optional[str]: Client IP address or None if not available
    """
    context = get_request_context()
    return context.get("ip_address") if context else None


def get_user_agent() -> Optional[str]:
    """Get the current request's user agent.
    
    Returns:
        Optional[str]: User agent string or None if not available
    """
    context = get_request_context()
    return context.get("user_agent") if context else None


def parse_accept_header(accept_header: str) -> Dict[str, float]:
    """Parse Accept header with quality values.

    Args:
        accept_header: The Accept header value

    Returns:
        Dict[str, float]: Media types with their quality values
    """
    if not accept_header:
        return {}

    media_types = {}

    for item in accept_header.split(','):
        item = item.strip()
        if not item:
            continue

        # Split media type and parameters
        parts = item.split(';')
        media_type = parts[0].strip().lower()

        # Default quality value
        quality = 1.0

        # Parse quality value if present
        for param in parts[1:]:
            param = param.strip()
            if param.startswith('q='):
                try:
                    quality = float(param[2:])
                except ValueError:
                    quality = 1.0
                break

        media_types[media_type] = quality

    return media_types


def is_json_request(request: Request) -> bool:
    """Check if the request expects a JSON response with enhanced detection.

    Args:
        request: FastAPI request object

    Returns:
        bool: True if JSON response is expected
    """
    # Check query parameter override first (highest priority)
    if request.query_params.get("format") == "json":
        return True

    # Check for AJAX requests (high priority)
    if request.headers.get("x-requested-with", "").lower() == "xmlhttprequest":
        return True

    # Parse Accept header with quality values
    accept_header = request.headers.get("accept", "").lower()
    if accept_header:
        media_types = parse_accept_header(accept_header)

        # Check if JSON is preferred over HTML
        json_quality = media_types.get("application/json", 0)
        html_quality = max(
            media_types.get("text/html", 0),
            media_types.get("application/xhtml+xml", 0)
        )

        # If JSON quality is higher than HTML, or JSON is present and HTML is not
        if json_quality > html_quality or (json_quality > 0 and html_quality == 0):
            return True

        # Check for any JSON-related media types
        json_types = ["application/json", "application/vnd.api+json", "application/hal+json"]
        if any(media_type in media_types for media_type in json_types):
            return True

    # Check for API client indicators in User-Agent
    user_agent = request.headers.get("user-agent", "").lower()
    api_indicators = [
        "postman", "insomnia", "curl", "httpie", "axios", "fetch",
        "node-fetch", "got", "request", "urllib", "python-requests",
        "okhttp", "retrofit", "alamofire"
    ]
    if any(indicator in user_agent for indicator in api_indicators):
        return True

    # Check for API-specific headers
    api_headers = [
        "x-api-key", "authorization", "x-auth-token",
        "x-client-id", "x-app-id", "x-api-version"
    ]
    if any(header in request.headers for header in api_headers):
        return True

    return False


def is_spa_request(request: Request) -> bool:
    """Check if the request is from a Single Page Application with enhanced detection.

    Args:
        request: FastAPI request object

    Returns:
        bool: True if request is from SPA
    """
    # Check for explicit SPA query parameter (highest priority)
    if request.query_params.get("spa") == "true":
        return True

    # Check for SPA-specific headers
    if request.headers.get("x-requested-with", "").lower() == "xmlhttprequest":
        return True

    # Check for modern SPA framework indicators in User-Agent
    user_agent = request.headers.get("user-agent", "").lower()
    spa_indicators = [
        "react", "vue", "angular", "svelte", "ember",
        "next.js", "nuxt", "gatsby", "vite", "webpack",
        "electron", "cordova", "phonegap", "ionic"
    ]
    if any(indicator in user_agent for indicator in spa_indicators):
        return True

    # Check for SPA-specific headers
    spa_headers = [
        "x-spa-version", "x-app-version", "x-client-version",
        "x-framework", "x-build-id", "x-deployment-id"
    ]
    if any(header in request.headers for header in spa_headers):
        return True

    # Check for modern browser fetch API patterns
    if "fetch" in user_agent and "mozilla" in user_agent:
        return True

    # Check for mobile app indicators
    mobile_indicators = [
        "mobile", "android", "ios", "iphone", "ipad",
        "react-native", "flutter", "xamarin", "cordova"
    ]
    if any(indicator in user_agent for indicator in mobile_indicators):
        # Mobile apps typically expect JSON responses
        return True

    # Check for JSON preference (but not if it's a traditional browser)
    if is_json_request(request):
        # Additional check: if it's a browser but prefers JSON, likely SPA
        browser_indicators = ["mozilla", "chrome", "safari", "firefox", "edge"]
        if any(indicator in user_agent for indicator in browser_indicators):
            return True

    return False


def is_mobile_request(request: Request) -> bool:
    """Check if the request is from a mobile device.

    Args:
        request: FastAPI request object

    Returns:
        bool: True if request is from mobile device
    """
    user_agent = request.headers.get("user-agent", "").lower()
    mobile_indicators = [
        "mobile", "android", "ios", "iphone", "ipad", "ipod",
        "blackberry", "windows phone", "opera mini", "opera mobi"
    ]
    return any(indicator in user_agent for indicator in mobile_indicators)


def get_client_type(request: Request) -> str:
    """Determine the type of client making the request.

    Args:
        request: FastAPI request object

    Returns:
        str: Client type ('spa', 'mobile', 'api', 'browser')
    """
    if is_mobile_request(request):
        return "mobile"
    elif is_spa_request(request):
        return "spa"
    elif is_json_request(request):
        return "api"
    else:
        return "browser"


def should_return_json(request: Request) -> bool:
    """Determine if the response should be JSON based on client type and preferences.

    Args:
        request: FastAPI request object

    Returns:
        bool: True if JSON response should be returned
    """
    client_type = get_client_type(request)
    return client_type in ["spa", "mobile", "api"]


class RequestLogger:
    """Enhanced request logging with context information."""

    @staticmethod
    def log_request_start(request: Request, correlation_id: str):
        """Log the start of a request.

        Args:
            request: FastAPI request object
            correlation_id: Request correlation ID
        """
        from app.utils.audit_logger import audit_logger, SecurityEventType

        audit_logger.log_security_event(
            SecurityEventType.LOGIN_SUCCESS,  # Generic event type
            correlation_id=correlation_id,
            ip_address=request.client.host if request.client else None,
            details={
                "event": "request_start",
                "method": request.method,
                "path": request.url.path,
                "user_agent": request.headers.get("user-agent"),
                "is_json_request": is_json_request(request),
                "is_spa_request": is_spa_request(request)
            }
        )

    @staticmethod
    def log_authentication_attempt(
        request: Request,
        user_email: Optional[str] = None,
        success: bool = False,
        error_code: Optional[str] = None
    ):
        """Log an authentication attempt.

        Args:
            request: FastAPI request object
            user_email: Email of user attempting authentication
            success: Whether authentication was successful
            error_code: Error code if authentication failed
        """
        from app.utils.audit_logger import audit_logger, SecurityEventType

        event_type = (
            SecurityEventType.LOGIN_SUCCESS if success
            else SecurityEventType.LOGIN_FAILED
        )

        correlation_id = get_correlation_id()

        details = {
            "method": request.method,
            "path": request.url.path,
            "user_agent": request.headers.get("user-agent"),
            "is_json_request": is_json_request(request),
            "is_spa_request": is_spa_request(request)
        }

        if user_email:
            details["email"] = user_email
        if error_code:
            details["error_code"] = error_code

        audit_logger.log_security_event(
            event_type,
            correlation_id=correlation_id,
            ip_address=request.client.host if request.client else None,
            details=details
        )
