// API-related type definitions

export interface Message {
  role: 'user' | 'agent';
  content: string;
}

export interface QueryRequest {
  query: string;
  output_format?: string;
  session_id?: string;
  conversation_history?: Message[];
  target_databases?: string[];
  target_tables?: Record<string, string[]>;
  target_columns?: Record<string, Record<string, string[]>>;
  enable_token_streaming?: boolean;
}

// Database connection types
export interface DatabaseConnectionRequestParams {
  name: string;
  description?: string;
  type: string; // Corresponds to DatabaseType enum on backend (e.g., "POSTGRESQL", "MONGODB")
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  database?: string;
  db_schema?: string;
  ssl_enabled?: boolean;
  connection_string?: string;
}

export interface DatabaseConnectionResponse {
  id: string;
  name: string;
  type: string;
  message: string;
  table_count?: number; 
}

export interface ConnectedDatabase {
  id: string;
  name: string;
  type: string;
  description?: string;
  host?: string;
  database?: string;
}

// Chat-related types
export interface ChatListItem {
  session_id: string;
  title: string;
  last_seen: number; // timestamp in milliseconds
  message_count: number;
}

export interface ApiChatMessage {
  role: 'user' | 'assistant';
  content: string;
}

export interface ChatHistoryRequest {
  session_id: string;
}

// Report types
export interface ReportInfo {
  key: string;
  session_id: string;
  file_name: string;
  size: number;
  last_modified: string;
  download_url: string;
  content_type: string;
  format: string;
}

// Streaming types
export interface TokenStreamEvent {
  type: 'token_stream' | 'conversation_complete' | 'error';
  agent: string;
  data: {
    token?: string;
    message?: string;
    error?: string;
  };
  timestamp: string;
}

export interface StreamingResponse {
  session_id: string;
  conversation_id?: string;
  status: 'streaming' | 'complete' | 'error';
}

export interface ListReportsRequest {
  max_reports?: number;
}

export interface ListReportsResponse {
  reports: ReportInfo[];
  total_count: number;
}

export interface DeleteChatRequest {
  session_id: string;
}

// Forward declarations for types from other modules
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
}

export interface RegisterResponse {
  message: string;
  user_id?: string;
  email?: string;
}

export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_at: string; // ISO 8601 date string
  refresh_token: string;
  user_id: string;
  is_new_user?: boolean; // Flag indicating if user needs onboarding
}

// API Context type
export interface ApiContextType {
  queryDatabases: (request: QueryRequest) => Promise<any>;
  getDatabaseSchema: (dbId: string) => Promise<any>;
  listDatabases: () => Promise<ConnectedDatabase[]>;
  connectNewDatabase: (params: DatabaseConnectionRequestParams) => Promise<DatabaseConnectionResponse>;
  disconnectExistingDatabase: (dbId: string) => Promise<{ message: string }>;
  askQuery: (
    query: string,
    outputFormat: string,
    conversationHistory: any[],
    targetDatabases?: string[],
    targetTables?: string[],
    targetColumns?: string[]
  ) => Promise<any>;
  loginUser: (credentials: LoginRequest) => Promise<TokenResponse>;
  registerUser: (credentials: RegisterRequest) => Promise<RegisterResponse>;
  refreshToken: () => Promise<TokenResponse>;
  logoutUser: () => Promise<void>;
  getUserProfile: () => Promise<any>;
  completeOnboarding: () => Promise<any>;
  updateUserProfile: (data: any) => Promise<any>;
  changePassword: (data: any) => Promise<any>;
  saveEmailPreferences: (data: any) => Promise<any>;
  savePrivacySettings: (data: any) => Promise<any>;
  exportUserData: () => Promise<any>;
  deleteAccount: () => Promise<any>;
  listUserChats: () => Promise<ChatListItem[]>;
  getChatHistory: (sessionId: string) => Promise<ApiChatMessage[]>;
  listUserReports: (request?: ListReportsRequest) => Promise<ListReportsResponse>;
  deleteChat: (sessionId: string) => Promise<{ message: string }>;
  queryChart: (request: { prompt: string; user_id?: string; dashboard_id?: string }) => Promise<any>;
  // Dashboard management methods
  listDashboards: () => Promise<any>;
  createDashboard: (request: any) => Promise<any>;
  getDashboard: (dashboardId: string) => Promise<any>;
  updateDashboard: (dashboardId: string, request: any) => Promise<any>;
  deleteDashboard: (dashboardId: string) => Promise<any>;
}
