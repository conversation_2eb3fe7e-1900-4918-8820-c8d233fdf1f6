"use client"

import * as React from "react"
import { ThemeProvider as NextThemesProvider, useTheme as useNextTheme } from "next-themes"

interface ThemeProviderProps {
  children: React.ReactNode
  [key: string]: any
}

export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>
}

// Custom hook that handles client-side rendering
export const useTheme = () => {
  const [mounted, setMounted] = React.useState(false)
  const { theme, setTheme, resolvedTheme } = useNextTheme()
  
  // After mounting, we have access to the theme
  React.useEffect(() => setMounted(true), [])
  
  return {
    theme: mounted ? theme : undefined,
    setTheme,
    resolvedTheme: mounted ? resolvedTheme : undefined,
    mounted
  }
}
