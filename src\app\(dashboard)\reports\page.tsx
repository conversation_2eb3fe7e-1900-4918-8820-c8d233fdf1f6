"use client";
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Layout from '@/components/layout/Layout';
import { useAuth } from '@/providers/AuthContext';
import Link from 'next/link';
import { useApi } from '@/providers/ApiContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { ReportInfo } from '@/types';
import { usePageTitle } from '@/hooks/usePageTitle';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Download, Eye, FileText, RefreshCw } from 'lucide-react';

function ReportsPageContent() {
  const { isAuthenticated } = useAuth();
  const { listUserReports } = useApi();
  const [reports, setReports] = useState<ReportInfo[]>([]);
  const [isLoadingReports, setIsLoadingReports] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadReports = useCallback(async () => {
    setIsLoadingReports(true);
    setError(null);
    try {
      const response = await listUserReports({ max_reports: 100 });
      setReports(response.reports);
    } catch (error) {
      console.error('Error loading reports:', error);
      setError('Failed to load reports. Please try again.');
    } finally {
      setIsLoadingReports(false);
    }
  }, [listUserReports]);

  useEffect(() => {
    if (isAuthenticated) {
      loadReports();
    } else {
      // Clear reports when user is not authenticated
      setReports([]);
      setError(null);
    }
  }, [isAuthenticated, loadReports]);

  // Add retry functionality for failed API calls
  const retryLoadReports = async () => {
    setError(null);
    await loadReports();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getFileIcon = () => {
    return <FileText className="h-4 w-4" />;
  };

  const getFormatBadgeColor = (format: string): string => {
    switch (format.toLowerCase()) {
      case 'csv':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'xlsx':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'json':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const handleViewReport = (report: ReportInfo) => {
    // For CSV files, we can view them directly in the browser
    if (report.format === 'csv') {
      window.open(report.download_url, '_blank');
    } else {
      // For Excel files, download them as they can't be viewed directly in browser
      handleDownloadReport(report);
    }
  };

  const handleDownloadReport = (report: ReportInfo) => {
    const link = document.createElement('a');
    link.href = report.download_url;
    link.download = report.file_name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
      <div className="flex flex-col h-full p-10">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-6 gap-4">
          <div className="flex-1">
            {/* Removed hardcoded header - now using centralized header system */}
          </div>
          <Button
            onClick={loadReports}
            disabled={isLoadingReports}
            variant="outline"
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 ${isLoadingReports ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {error && (
          <Card className="mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <p className="text-red-800 dark:text-red-200">{error}</p>
                <Button
                  onClick={retryLoadReports}
                  disabled={isLoadingReports}
                  variant="outline"
                  size="sm"
                  className="ml-4"
                >
                  Try Again
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-900 dark:text-white">
              <FileText className="h-5 w-5 text-gray-600 dark:text-white" />
              Your Reports ({reports.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingReports ? (
              <div className="flex items-center justify-center py-8">
                <div className="flex items-center gap-2 text-gray-600 dark:text-black">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  Loading reports...
                </div>
              </div>
            ) : reports.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-gray-600 dark:text-gray-400">No reports found</p>
                <p className="text-sm text-gray-500 dark:text-black mt-1">
                  Reports will appear here after you generate them from your chats
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>File</TableHead>
                      <TableHead>Format</TableHead>
                      <TableHead>Size</TableHead>
                      <TableHead>Session</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reports.map((report) => (
                      <TableRow key={report.key}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getFileIcon()}
                            <span className="font-medium text-sm">{report.file_name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={`${getFormatBadgeColor(report.format)} text-xs`}>
                            {report.format.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600 dark:text-white">
                          {formatFileSize(report.size)}
                        </TableCell>
                        <TableCell className="text-sm">
                          <Link
                            href={`/chat/chat_sess${report.session_id}`}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:underline"
                          >
                            {report.session_id}
                          </Link>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600 dark:text-white">
                          {formatDate(report.last_modified)}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewReport(report)}
                              className="h-8 w-8 p-0"
                              title={report.format === 'csv' ? 'View in browser' : 'Download file'}
                            >
                              {report.format === 'csv' ? (
                                <Eye className="h-4 w-4" />
                              ) : (
                                <Download className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
  );
}

function ReportsPageWithTitle() {
  // Set page title - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: 'Reports',
    icon: FileText
  }), []);

  usePageTitle(pageConfig);

  return <ReportsPageContent />;
}

export default function ReportsPage() {
  return (
    <ProtectedRoute>
      <Layout>
        <ReportsPageWithTitle />
      </Layout>
    </ProtectedRoute>
  );
}