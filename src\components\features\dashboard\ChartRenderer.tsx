"use client";

import React from 'react';
import {
  Bar,
  BarChart,
  Line,
  LineChart,
  Pie,
  PieChart,
  Area,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
  Tooltip,
} from 'recharts';
import { ChartData, DEFAULT_CHART_COLORS } from '@/types';
import { useTheme } from 'next-themes';

interface ChartRendererProps {
  chartData: ChartData;
  width?: number;
  height?: number;
  className?: string;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({
  chartData,
  width,
  height,
  className = '',
}) => {
  const { chartType, data, metadata } = chartData;
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  // Theme-aware colors
  const getThemeColors = () => ({
    grid: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',
    text: isDark ? 'hsl(215 20.2% 65.1%)' : 'hsl(215.4 16.3% 46.9%)',
    tooltipBg: isDark ? 'hsl(222.2 84% 4.9%)' : 'hsl(0 0% 100%)',
    tooltipBorder: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',
    tooltipText: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)',
    primary: isDark ? 'hsl(217.2 91.2% 59.8%)' : 'hsl(221.2 83.2% 53.3%)',
  });

  const themeColors = getThemeColors();

  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 10, right: 15, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />
              <XAxis
                dataKey="label"
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize: 11 }}
                height={30}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize: 11 }}
                width={30}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar
                dataKey="value"
                fill={metadata.colors?.[0] || themeColors.primary}
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 10, right: 15, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />
              <XAxis
                dataKey="label"
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize: 11 }}
                height={30}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize: 11 }}
                width={30}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={metadata.colors?.[0] || themeColors.primary}
                strokeWidth={2}
                dot={{ fill: metadata.colors?.[0] || themeColors.primary, strokeWidth: 0, r: 3 }}
                activeDot={{ r: 5, fill: metadata.colors?.[0] || themeColors.primary }}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data} margin={{ top: 10, right: 15, left: 10, bottom: 10 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />
              <XAxis
                dataKey="label"
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize: 11 }}
                height={30}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize: 11 }}
                width={30}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke={metadata.colors?.[0] || themeColors.primary}
                fill={metadata.colors?.[0] || themeColors.primary}
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ label, percent }) => `${label} ${percent ? (percent * 100).toFixed(0) : 0}%`}
                outerRadius="80%"
                innerRadius="40%"
                fill={themeColors.primary}
                dataKey="value"
                stroke={themeColors.grid}
                strokeWidth={1}
              >
                {data.map((_, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={metadata.colors?.[index] || DEFAULT_CHART_COLORS[index % DEFAULT_CHART_COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <p>Unsupported chart type: {chartType}</p>
          </div>
        );
    }
  };

  return (
    <div className="w-full h-full">
      {renderChart()}
    </div>
  );
};

export default ChartRenderer;
