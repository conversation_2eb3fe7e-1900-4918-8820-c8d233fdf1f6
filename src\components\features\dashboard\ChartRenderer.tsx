"use client";

import React, { useRef, useEffect, useState } from 'react';
import {
  Bar,
  BarChart,
  Line,
  LineChart,
  Pie,
  PieChart,
  Area,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Cell,
  Tooltip,
} from 'recharts';
import { ChartData, DEFAULT_CHART_COLORS } from '@/types';
import { useTheme } from 'next-themes';

interface ChartRendererProps {
  chartData: ChartData;
  width?: number;
  height?: number;
  className?: string;
}

// Responsive font size based on container dimensions
const getResponsiveFontSize = (width?: number, height?: number) => {
  if (!width || !height) return 10;
  const minDimension = Math.min(width, height);
  if (minDimension < 200) return 8;
  if (minDimension < 300) return 9;
  if (minDimension < 400) return 10;
  return 11;
};

// Responsive axis dimensions
const getAxisDimensions = (width?: number, height?: number) => {
  if (!width || !height) return { xHeight: 20, yWidth: 25 };
  const minDimension = Math.min(width, height);
  if (minDimension < 200) return { xHeight: 15, yWidth: 20 };
  if (minDimension < 300) return { xHeight: 18, yWidth: 22 };
  return { xHeight: 20, yWidth: 25 };
};

const ChartRenderer: React.FC<ChartRendererProps> = ({
  chartData,
  width,
  height,
  className = '',
}) => {
  const { chartType, data, metadata } = chartData;
  const { resolvedTheme } = useTheme();
  const isDark = resolvedTheme === 'dark';

  // Responsive sizing
  const fontSize = getResponsiveFontSize(width, height);
  const { xHeight, yWidth } = getAxisDimensions(width, height);

  // Theme-aware colors
  const getThemeColors = () => ({
    grid: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',
    text: isDark ? 'hsl(215 20.2% 65.1%)' : 'hsl(215.4 16.3% 46.9%)',
    tooltipBg: isDark ? 'hsl(222.2 84% 4.9%)' : 'hsl(0 0% 100%)',
    tooltipBorder: isDark ? 'hsl(217.2 32.6% 17.5%)' : 'hsl(214.3 31.8% 91.4%)',
    tooltipText: isDark ? 'hsl(210 40% 98%)' : 'hsl(222.2 84% 4.9%)',
    primary: isDark ? 'hsl(217.2 91.2% 59.8%)' : 'hsl(221.2 83.2% 53.3%)',
  });

  const themeColors = getThemeColors();

  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />
              <XAxis
                dataKey="label"
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize }}
                height={xHeight}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize }}
                width={yWidth}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Bar
                dataKey="value"
                fill={metadata.colors?.[0] || themeColors.primary}
                radius={[2, 2, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />
              <XAxis
                dataKey="label"
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize }}
                height={xHeight}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize }}
                width={yWidth}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={metadata.colors?.[0] || themeColors.primary}
                strokeWidth={2}
                dot={{ fill: metadata.colors?.[0] || themeColors.primary, strokeWidth: 0, r: 3 }}
                activeDot={{ r: 5, fill: metadata.colors?.[0] || themeColors.primary }}
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'area':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data} margin={{ top: 5, right: 5, left: 5, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={themeColors.grid} />
              <XAxis
                dataKey="label"
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize }}
                height={xHeight}
              />
              <YAxis
                axisLine={false}
                tickLine={false}
                tick={{ fill: themeColors.text, fontSize }}
                width={yWidth}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
              <Area
                type="monotone"
                dataKey="value"
                stroke={metadata.colors?.[0] || themeColors.primary}
                fill={metadata.colors?.[0] || themeColors.primary}
                fillOpacity={0.3}
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={width && width < 250 ? false : ({ label, percent }) => `${label} ${percent ? (percent * 100).toFixed(0) : 0}%`}
                outerRadius="90%"
                innerRadius="35%"
                fill={themeColors.primary}
                dataKey="value"
                stroke={themeColors.grid}
                strokeWidth={1}
              >
                {data.map((_, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={metadata.colors?.[index] || DEFAULT_CHART_COLORS[index % DEFAULT_CHART_COLORS.length]}
                  />
                ))}
              </Pie>
              <Tooltip
                contentStyle={{
                  backgroundColor: themeColors.tooltipBg,
                  border: `1px solid ${themeColors.tooltipBorder}`,
                  borderRadius: '8px',
                  color: themeColors.tooltipText,
                  boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            <p>Unsupported chart type: {chartType}</p>
          </div>
        );
    }
  };

  return (
    <div className="w-full h-full">
      {renderChart()}
    </div>
  );
};

// Wrapper component that measures container size for responsive behavior
const ResponsiveChartRenderer: React.FC<Omit<ChartRendererProps, 'width' | 'height'>> = (props) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null);

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        setDimensions({ width, height });
      }
    };

    updateDimensions();

    const resizeObserver = new ResizeObserver(updateDimensions);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div ref={containerRef} className={`w-full h-full ${props.className || ''}`}>
      {dimensions && (
        <ChartRenderer
          {...props}
          width={dimensions.width}
          height={dimensions.height}
          className=""
        />
      )}
    </div>
  );
};

export default ResponsiveChartRenderer;
