// Dashboard-related TypeScript interfaces

import { ChartWidget } from './chart';

export interface Dashboard {
  id: string;
  name: string;
  description: string;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export interface DashboardWithCharts extends Dashboard {
  widgets: ChartWidget[];
}

export interface CreateDashboardRequest {
  name: string;
  description: string;
}

export interface UpdateDashboardRequest {
  name?: string;
  description?: string;
}

export interface DashboardListResponse {
  success: boolean;
  data: Dashboard[];
  error?: string;
}

export interface DashboardResponse {
  success: boolean;
  data: DashboardWithCharts;
  error?: string;
}

export interface CreateDashboardResponse {
  success: boolean;
  data: Dashboard;
  error?: string;
}

export interface UpdateDashboardResponse {
  success: boolean;
  data: Dashboard;
  error?: string;
}

export interface DeleteDashboardResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Dashboard view states
export type DashboardViewState = 'list' | 'dashboard';

export interface DashboardNavigationState {
  currentView: DashboardViewState;
  selectedDashboard: Dashboard | null;
  breadcrumbs: Array<{
    label: string;
    onClick?: () => void;
  }>;
}

// Dashboard statistics
export interface DashboardStats {
  totalDashboards: number;
  totalCharts: number;
  activeCharts: number;
  recentActivity: string;
}
