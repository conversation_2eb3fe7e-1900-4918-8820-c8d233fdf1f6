"""
Business Context Agent - Phase 3: Intelligent Data Analysis & Insights
======================================================================

This agent provides domain-aware insight interpretation by understanding business context
and translating statistical findings into actionable business intelligence.

Key Features:
- Domain Knowledge Integration: Understands common business metrics and KPIs
- Contextual Interpretation: Translates statistical insights into business language
- Industry Pattern Recognition: Recognizes common business patterns and anomalies
- Actionable Recommendations: Provides specific business actions based on data insights
- Risk Assessment: Identifies potential business risks from data patterns
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from app.agents.base import Agent, AgentResponse
from app.utils.bedrock_client import BedrockClient
from app.config.llm_config import ModelPurpose

logger = logging.getLogger(__name__)


class BusinessDomain(Enum):
    """Common business domains for context interpretation."""
    SALES = "sales"
    MARKETING = "marketing"
    FINANCE = "finance"
    OPERATIONS = "operations"
    HR = "human_resources"
    CUSTOMER_SERVICE = "customer_service"
    GENERAL = "general"


@dataclass
class BusinessInsight:
    """Represents a business-contextualized insight."""
    insight_id: str
    business_domain: str
    insight_type: str
    business_impact: str  # high, medium, low
    description: str
    business_explanation: str
    recommended_actions: List[str]
    potential_risks: List[str]
    kpi_implications: List[str]
    confidence_score: float


class BusinessContextAgent(Agent):
    """
    Agent responsible for interpreting statistical insights within business context.
    
    This agent takes raw statistical insights and translates them into meaningful
    business intelligence with domain-specific interpretations and actionable recommendations.
    """
    
    def __init__(self, agent_id: Optional[str] = None):
        """Initialize the Business Context Agent."""
        self.agent_id = agent_id or "business_context_agent"
        self.bedrock_client = BedrockClient(purpose=ModelPurpose.ANALYSIS)
        self.initialized = False
        
        # Business context patterns
        self.business_patterns = self._load_business_patterns()
        self.kpi_mappings = self._load_kpi_mappings()
        
    async def initialize(self) -> None:
        """Initialize the agent."""
        self.initialized = True
        logger.info(f"✅ {self.agent_id} initialized successfully")
        
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process statistical insights and add business context.
        
        Expected message format:
        {
            "statistical_insights": [...],  # From DataInsightsAgent
            "query_context": {...},         # Original query context
            "business_domain": "sales",     # Optional domain hint
            "column_metadata": {...},       # Optional column business meanings
        }
        
        Returns:
        {
            "business_insights": [...],
            "domain_analysis": {...},
            "kpi_implications": [...],
            "risk_assessment": {...},
            "business_recommendations": [...]
        }
        """
        if not self.initialized:
            await self.initialize()
            
        try:
            # Extract input data
            statistical_insights = message.get("statistical_insights", [])
            query_context = message.get("query_context", {})
            business_domain = message.get("business_domain", "general")
            column_metadata = message.get("column_metadata", {})
            
            if not statistical_insights:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    error="No statistical insights provided for business context analysis"
                ).to_dict()
            
            # Infer business domain if not provided
            if business_domain == "general":
                business_domain = await self._infer_business_domain(query_context, column_metadata)
            
            # Generate business insights
            business_insights = await self._generate_business_insights(
                statistical_insights, business_domain, query_context, column_metadata
            )
            
            # Analyze domain-specific implications
            domain_analysis = await self._analyze_domain_implications(
                business_insights, business_domain
            )
            
            # Assess KPI implications
            kpi_implications = await self._assess_kpi_implications(
                business_insights, business_domain, column_metadata
            )
            
            # Perform risk assessment
            risk_assessment = await self._perform_risk_assessment(
                business_insights, business_domain
            )
            
            # Generate business recommendations
            business_recommendations = await self._generate_business_recommendations(
                business_insights, domain_analysis, risk_assessment
            )
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={
                    "business_insights": [insight.__dict__ for insight in business_insights],
                    "domain_analysis": domain_analysis,
                    "kpi_implications": kpi_implications,
                    "risk_assessment": risk_assessment,
                    "business_recommendations": business_recommendations,
                    "business_metadata": {
                        "inferred_domain": business_domain,
                        "analysis_timestamp": datetime.utcnow().isoformat(),
                        "insights_processed": len(statistical_insights),
                        "business_insights_generated": len(business_insights)
                    }
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error in business context analysis: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Business context analysis failed: {str(e)}"
            ).to_dict()
    
    def _load_business_patterns(self) -> Dict[str, Any]:
        """Load common business patterns and their interpretations."""
        return {
            "high_correlation": {
                "sales": "Strong relationship between metrics may indicate sales funnel efficiency",
                "marketing": "Correlation suggests effective campaign targeting or channel synergy",
                "finance": "Relationship may indicate cost drivers or revenue dependencies",
                "operations": "Correlation suggests process dependencies or bottlenecks"
            },
            "outliers": {
                "sales": "Outliers may represent exceptional deals, seasonal effects, or data quality issues",
                "marketing": "Anomalies could indicate campaign spikes, viral content, or tracking errors",
                "finance": "Outliers may represent one-time events, errors, or exceptional transactions",
                "operations": "Anomalies could indicate process failures, exceptional performance, or measurement errors"
            },
            "trends": {
                "increasing": {
                    "sales": "Positive trend indicates growth opportunity or successful strategies",
                    "marketing": "Upward trend suggests effective campaigns or growing market interest",
                    "finance": "Increasing trend may indicate revenue growth or rising costs",
                    "operations": "Upward trend could indicate improving efficiency or growing demand"
                },
                "decreasing": {
                    "sales": "Declining trend requires immediate attention and strategy adjustment",
                    "marketing": "Downward trend suggests campaign fatigue or market saturation",
                    "finance": "Decreasing trend may indicate cost savings or declining revenue",
                    "operations": "Declining trend could indicate efficiency improvements or reduced demand"
                }
            }
        }
    
    def _load_kpi_mappings(self) -> Dict[str, List[str]]:
        """Load mappings of common column names to business KPIs."""
        return {
            "sales": ["revenue", "sales", "amount", "price", "quantity", "deals", "conversion"],
            "marketing": ["clicks", "impressions", "ctr", "cpc", "leads", "engagement", "reach"],
            "finance": ["cost", "expense", "profit", "margin", "budget", "roi", "revenue"],
            "operations": ["efficiency", "throughput", "capacity", "utilization", "downtime", "quality"],
            "customer": ["satisfaction", "retention", "churn", "lifetime_value", "acquisition_cost"],
            "time": ["date", "time", "timestamp", "created", "updated", "period", "month", "year"]
        }
    
    async def _infer_business_domain(self, query_context: Dict[str, Any], column_metadata: Dict[str, Any]) -> str:
        """Infer the business domain from query context and column names."""
        try:
            # Extract column names from various sources
            column_names = []
            
            if column_metadata:
                column_names.extend(column_metadata.keys())
            
            if query_context.get("enhanced_query"):
                # Simple keyword extraction from query
                query_text = query_context["enhanced_query"].lower()
                column_names.extend(query_text.split())
            
            # Score domains based on keyword matches
            domain_scores = {}
            for domain, keywords in self.kpi_mappings.items():
                score = sum(1 for col in column_names for keyword in keywords if keyword in col.lower())
                if score > 0:
                    domain_scores[domain] = score
            
            # Return domain with highest score, or general if no clear match
            if domain_scores:
                return max(domain_scores, key=domain_scores.get)
            
            return "general"
            
        except Exception as e:
            logger.error(f"Error inferring business domain: {str(e)}")
            return "general"
    
    async def _generate_business_insights(
        self,
        statistical_insights: List[Dict[str, Any]],
        business_domain: str,
        query_context: Dict[str, Any],
        column_metadata: Dict[str, Any]
    ) -> List[BusinessInsight]:
        """Generate business-contextualized insights from statistical insights."""
        business_insights = []
        
        try:
            for i, stat_insight in enumerate(statistical_insights):
                # Extract statistical insight details
                insight_type = stat_insight.get("insight_type", "unknown")
                description = stat_insight.get("description", "")
                significance = stat_insight.get("significance", 0.0)
                
                # Generate business context using LLM
                business_context = await self._generate_llm_business_context(
                    stat_insight, business_domain, query_context
                )
                
                # Create business insight
                business_insight = BusinessInsight(
                    insight_id=f"business_insight_{i}",
                    business_domain=business_domain,
                    insight_type=insight_type,
                    business_impact=self._assess_business_impact(significance, insight_type),
                    description=description,
                    business_explanation=business_context.get("explanation", ""),
                    recommended_actions=business_context.get("actions", []),
                    potential_risks=business_context.get("risks", []),
                    kpi_implications=business_context.get("kpi_implications", []),
                    confidence_score=significance
                )
                
                business_insights.append(business_insight)
            
            return business_insights

        except Exception as e:
            logger.error(f"Error generating business insights: {str(e)}")
            return business_insights

    async def _generate_llm_business_context(
        self,
        statistical_insight: Dict[str, Any],
        business_domain: str,
        query_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Use LLM to generate business context for a statistical insight."""
        try:
            system_prompt = f"""You are a senior business analyst specializing in {business_domain} analytics.
Your role is to translate statistical findings into actionable business insights.

You must return ONLY valid JSON in the exact format specified. Do not include any explanations,
markdown formatting, or additional text. Your response must be parseable as JSON."""

            user_prompt = f"""Statistical Finding:
{json.dumps(statistical_insight, indent=2)}

Business Domain: {business_domain}
Query Context: {query_context.get('enhanced_query', 'Not provided')}

Provide business interpretation of this statistical finding:

Return ONLY this JSON structure (no other text):
{{
  "explanation": "Clear business explanation of what this finding means",
  "actions": ["specific action 1", "specific action 2", "specific action 3"],
  "risks": ["potential risk 1", "potential risk 2"],
  "kpi_implications": ["KPI impact 1", "KPI impact 2"],
  "business_priority": "high|medium|low"
}}"""

            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.3
            )

            # Clean and parse JSON response
            cleaned_response = response.strip().removeprefix("```json").removesuffix("```")
            business_context = json.loads(cleaned_response)

            return business_context

        except Exception as e:
            logger.warning(f"LLM business context generation failed: {e}")
            return self._fallback_business_context(statistical_insight, business_domain)

    def _fallback_business_context(self, statistical_insight: Dict[str, Any], business_domain: str) -> Dict[str, Any]:
        """Generate fallback business context when LLM fails."""
        insight_type = statistical_insight.get("insight_type", "unknown")

        # Use predefined patterns
        if insight_type in self.business_patterns:
            domain_patterns = self.business_patterns[insight_type]
            explanation = domain_patterns.get(business_domain, domain_patterns.get("general", "Statistical pattern detected"))
        else:
            explanation = f"Statistical finding in {business_domain} data requires investigation"

        return {
            "explanation": explanation,
            "actions": ["Review data quality", "Investigate underlying causes", "Monitor trend"],
            "risks": ["Data quality issues", "Missed business opportunities"],
            "kpi_implications": ["May impact key performance indicators"],
            "business_priority": "medium"
        }

    def _assess_business_impact(self, significance: float, insight_type: str) -> str:
        """Assess the business impact level of an insight."""
        if significance >= 0.8:
            return "high"
        elif significance >= 0.5:
            return "medium"
        else:
            return "low"

    async def _analyze_domain_implications(self, business_insights: List[BusinessInsight], business_domain: str) -> Dict[str, Any]:
        """Analyze domain-specific implications of the business insights."""
        try:
            domain_analysis = {
                "domain": business_domain,
                "total_insights": len(business_insights),
                "high_impact_insights": len([i for i in business_insights if i.business_impact == "high"]),
                "key_themes": [],
                "domain_specific_patterns": [],
                "strategic_implications": []
            }

            # Identify key themes
            insight_types = [insight.insight_type for insight in business_insights]
            theme_counts = {}
            for insight_type in insight_types:
                theme_counts[insight_type] = theme_counts.get(insight_type, 0) + 1

            domain_analysis["key_themes"] = [
                {"theme": theme, "count": count}
                for theme, count in sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)
            ]

            # Domain-specific pattern analysis
            if business_domain == "sales":
                domain_analysis["domain_specific_patterns"] = self._analyze_sales_patterns(business_insights)
            elif business_domain == "marketing":
                domain_analysis["domain_specific_patterns"] = self._analyze_marketing_patterns(business_insights)
            elif business_domain == "finance":
                domain_analysis["domain_specific_patterns"] = self._analyze_finance_patterns(business_insights)

            return domain_analysis

        except Exception as e:
            logger.error(f"Error analyzing domain implications: {str(e)}")
            return {"domain": business_domain, "error": str(e)}

    def _analyze_sales_patterns(self, business_insights: List[BusinessInsight]) -> List[Dict[str, Any]]:
        """Analyze sales-specific patterns."""
        patterns = []

        # Look for sales funnel patterns
        correlation_insights = [i for i in business_insights if i.insight_type == "correlation"]
        if correlation_insights:
            patterns.append({
                "pattern_type": "sales_funnel_efficiency",
                "description": "Correlations detected that may indicate sales funnel performance",
                "business_relevance": "Strong correlations in sales data often indicate funnel efficiency or bottlenecks"
            })

        # Look for trend patterns
        trend_insights = [i for i in business_insights if "trend" in i.insight_type]
        if trend_insights:
            patterns.append({
                "pattern_type": "sales_momentum",
                "description": "Trends detected in sales metrics",
                "business_relevance": "Sales trends indicate market momentum and strategy effectiveness"
            })

        return patterns

    def _analyze_marketing_patterns(self, business_insights: List[BusinessInsight]) -> List[Dict[str, Any]]:
        """Analyze marketing-specific patterns."""
        patterns = []

        # Look for campaign performance patterns
        anomaly_insights = [i for i in business_insights if i.insight_type == "anomaly"]
        if anomaly_insights:
            patterns.append({
                "pattern_type": "campaign_spikes",
                "description": "Anomalies detected that may indicate campaign performance spikes",
                "business_relevance": "Marketing anomalies often represent successful campaigns or viral content"
            })

        return patterns

    def _analyze_finance_patterns(self, business_insights: List[BusinessInsight]) -> List[Dict[str, Any]]:
        """Analyze finance-specific patterns."""
        patterns = []

        # Look for cost/revenue patterns
        distribution_insights = [i for i in business_insights if i.insight_type == "distribution"]
        if distribution_insights:
            patterns.append({
                "pattern_type": "financial_distribution",
                "description": "Distribution patterns in financial data",
                "business_relevance": "Financial distributions reveal spending patterns and revenue concentration"
            })

        return patterns

    async def _assess_kpi_implications(
        self,
        business_insights: List[BusinessInsight],
        business_domain: str,
        column_metadata: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Assess implications for key performance indicators."""
        kpi_implications = []

        try:
            # Map insights to potential KPIs
            for insight in business_insights:
                if insight.business_impact == "high":
                    kpi_implication = {
                        "insight_id": insight.insight_id,
                        "affected_kpis": insight.kpi_implications,
                        "impact_level": insight.business_impact,
                        "monitoring_recommendation": f"Monitor {insight.insight_type} patterns for KPI impact",
                        "alert_threshold": "Set up alerts if pattern changes significantly"
                    }
                    kpi_implications.append(kpi_implication)

            return kpi_implications

        except Exception as e:
            logger.error(f"Error assessing KPI implications: {str(e)}")
            return kpi_implications

    async def _perform_risk_assessment(self, business_insights: List[BusinessInsight], business_domain: str) -> Dict[str, Any]:
        """Perform risk assessment based on business insights."""
        try:
            risk_assessment = {
                "overall_risk_level": "low",
                "identified_risks": [],
                "mitigation_strategies": [],
                "monitoring_recommendations": []
            }

            # Aggregate risks from all insights
            all_risks = []
            for insight in business_insights:
                all_risks.extend(insight.potential_risks)

            # Categorize and prioritize risks
            risk_categories = {}
            for risk in all_risks:
                category = self._categorize_risk(risk, business_domain)
                if category not in risk_categories:
                    risk_categories[category] = []
                risk_categories[category].append(risk)

            # Determine overall risk level
            high_impact_insights = [i for i in business_insights if i.business_impact == "high"]
            if len(high_impact_insights) > 2:
                risk_assessment["overall_risk_level"] = "high"
            elif len(high_impact_insights) > 0:
                risk_assessment["overall_risk_level"] = "medium"

            risk_assessment["identified_risks"] = [
                {"category": cat, "risks": risks}
                for cat, risks in risk_categories.items()
            ]

            return risk_assessment

        except Exception as e:
            logger.error(f"Error performing risk assessment: {str(e)}")
            return {"overall_risk_level": "unknown", "error": str(e)}

    def _categorize_risk(self, risk: str, business_domain: str) -> str:
        """Categorize a risk based on its description and business domain."""
        risk_lower = risk.lower()

        if "data" in risk_lower or "quality" in risk_lower:
            return "data_quality"
        elif "opportunity" in risk_lower or "revenue" in risk_lower:
            return "business_opportunity"
        elif "compliance" in risk_lower or "regulatory" in risk_lower:
            return "compliance"
        else:
            return "operational"

    async def _generate_business_recommendations(
        self,
        business_insights: List[BusinessInsight],
        domain_analysis: Dict[str, Any],
        risk_assessment: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate comprehensive business recommendations."""
        recommendations = []

        try:
            # Priority-based recommendations
            high_impact_insights = [i for i in business_insights if i.business_impact == "high"]

            for insight in high_impact_insights:
                recommendation = {
                    "recommendation_id": f"rec_{insight.insight_id}",
                    "priority": "high",
                    "title": f"Address {insight.insight_type} in {insight.business_domain}",
                    "description": insight.business_explanation,
                    "actions": insight.recommended_actions,
                    "expected_impact": "High business impact expected",
                    "timeline": "Immediate action recommended"
                }
                recommendations.append(recommendation)

            # Risk-based recommendations
            if risk_assessment.get("overall_risk_level") == "high":
                recommendations.append({
                    "recommendation_id": "risk_mitigation",
                    "priority": "high",
                    "title": "Implement Risk Mitigation Strategy",
                    "description": "Multiple high-impact insights indicate elevated business risk",
                    "actions": ["Conduct detailed risk analysis", "Implement monitoring systems", "Develop contingency plans"],
                    "expected_impact": "Reduced business risk exposure",
                    "timeline": "Within 2 weeks"
                })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating business recommendations: {str(e)}")
            return recommendations
