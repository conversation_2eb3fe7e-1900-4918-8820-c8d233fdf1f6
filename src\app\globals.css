@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* ChatGPT-style variables for light mode */
    --sidebar-width: 16rem; /* 256px - responsive sidebar width */
    --header-height: 4rem; /* 64px - responsive header height */
    --sidebar-bg: #fcfcfc;
    --sidebar-surface-primary: #f9f9f9;
    --sidebar-surface-secondary: #ececec;
    --sidebar-surface-tertiary: #e3e3e3;
    --sidebar-border: rgba(0, 0, 0, 0.1);
    --sidebar-text-primary: #0d0d0d;
    --sidebar-text-secondary: #5d5d5d;
    --sidebar-text-tertiary: #8f8f8f;
    --sidebar-icon: #7d7d7d;
    --surface-hover: rgba(0, 0, 0, 0.05);
    --surface-selected: rgba(0, 0, 0, 0.1);
    --interactive-bg-secondary-hover: rgba(13, 13, 13, 0.05);
    --interactive-bg-secondary-press: rgba(13, 13, 13, 0.1);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* ChatGPT-style variables for dark mode */
    --sidebar-width: 16rem; /* 256px - responsive sidebar width */
    --header-height: 4rem; /* 64px - responsive header height */
    --sidebar-bg: #171717;
    --sidebar-surface-primary: #212121;
    --sidebar-surface-secondary: #303030;
    --sidebar-surface-tertiary: #2f2f2f;
    --sidebar-border: rgba(255, 255, 255, 0.1);
    --sidebar-text-primary: #fff;
    --sidebar-text-secondary: #f3f3f3;
    --sidebar-text-tertiary: #afafaf;
    --sidebar-icon: #a4a4a4;
    --surface-hover: rgba(255, 255, 255, 0.1);
    --surface-selected: rgba(255, 255, 255, 0.15);
    --interactive-bg-secondary-hover: rgba(255, 255, 255, 0.1);
    --interactive-bg-secondary-press: rgba(255, 255, 255, 0.05);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  height: 100%;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Smooth scrolling for modal content */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #94a3b8;
}

/* Analytical Content Styling */
.analytical-markdown h2 {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.analytical-markdown h3 {
  position: relative;
}

.analytical-markdown h3:before {
  content: '';
  position: absolute;
  left: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

.analytical-markdown strong {
  position: relative;
  display: inline-block;
}

.analytical-markdown li strong {
  color: #1d4ed8;
  font-weight: 600;
}

/* Dark mode adjustments for analytical content */
@media (prefers-color-scheme: dark) {
  .analytical-markdown h2 {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .analytical-markdown h3:before {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
  }

  .analytical-markdown li strong {
    color: #60a5fa;
  }
}

/* Improve modal scrolling performance */
.dialog-scrollable {
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
  contain: layout style;
}

a {
  color: inherit;
  text-decoration: none;
}

/* React Grid Layout Styling */
.react-grid-layout {
  position: relative;
}

.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}

/* Optimize chart container space utilization */
.grid-item {
  display: flex;
  flex-direction: column;
}

.grid-item > * {
  flex: 1;
  min-height: 0;
}

/* Ensure charts fill their containers */
.recharts-wrapper {
  width: 100% !important;
  height: 100% !important;
}

/* Optimize card spacing for charts */
.chart-card .card-header {
  flex-shrink: 0;
}

.chart-card .card-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.react-grid-item.cssTransforms {
  transition-property: transform;
}

.react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8ZG90cyBmaWxsPSIjODg4IiBkPSJtMTUgMTJjMCAxLjY1NC0xLjM0NiAzLTMgM3MtMy0xLjM0Ni0zLTMgMS4zNDYtMyAzLTMgMyAxLjM0NiAzIDN6bTAgNWMwIDEuNjU0LTEuMzQ2IDMtMyAzcy0zLTEuMzQ2LTMtMyAxLjM0Ni0zIDMtMyAzIDEuMzQ2IDMgM3ptMCA1YzAgMS42NTQtMS4zNDYgMy0zIDNzLTMtMS4zNDYtMy0zIDEuMzQ2LTMgMy0zIDMgMS4zNDYgMyAzem01LTEwYzAgMS42NTQtMS4zNDYgMy0zIDNzLTMtMS4zNDYtMy0zIDEuMzQ2LTMgMy0zIDMgMS4zNDYgMyAzem0wIDVjMCAxLjY1NC0xLjM0NiAzLTMgM3MtMy0xLjM0Ni0zLTMgMS4zNDYtMyAzLTMgMyAxLjM0NiAzIDN6bTAgNWMwIDEuNjU0LTEuMzQ2IDMtMyAzcy0zLTEuMzQ2LTMtMyAxLjM0Ni0zIDMtMyAzIDEuMzQ2IDMgM3ptNS0xMGMwIDEuNjU0LTEuMzQ2IDMtMyAzcy0zLTEuMzQ2LTMtMyAxLjM0Ni0zIDMtMyAzIDEuMzQ2IDMgM3ptMCA1YzAgMS42NTQtMS4zNDYgMy0zIDNzLTMtMS4zNDYtMy0zIDEuMzQ2LTMgMy0zIDMgMS4zNDYgMyAzem0wIDVjMCAxLjY1NC0xLjM0NiAzLTMgM3MtMy0xLjM0Ni0zLTMgMS4zNDYtMyAzLTMgMyAxLjM0NiAzIDN6Ii8+Cjwvc3ZnPgo=');
  background-position: bottom right;
  padding: 0 3px 3px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: se-resize;
  opacity: 0.4;
  transition: opacity 0.2s ease;
}

.react-grid-item:hover > .react-resizable-handle {
  opacity: 0.8;
}

.react-grid-item.react-grid-placeholder {
  background: hsl(var(--primary) / 0.2);
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  border-radius: 0.75rem;
}

/* Dark mode adjustments for grid layout */
.dark .react-grid-item > .react-resizable-handle {
  opacity: 0.6;
}

.dark .react-grid-item:hover > .react-resizable-handle {
  opacity: 1;
}