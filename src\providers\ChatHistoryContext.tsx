"use client";
import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback, useRef } from "react";
import axios from "axios";
import { useAuth } from "./AuthContext";
import { useApi } from "./ApiContext";
import {
  ChatHistoryItem,
  ChatHistoryContextType,
  ChatMessage as ChatMessageType,
  ChatListItem,
  ApiChatMessage
} from '@/types';
import { generateSessionId } from '@/lib/utils';

// Local message interface for internal use
interface Message {
  role: 'user' | 'agent';
  content: string;
  timestamp?: Date;
  outputFiles?: Array<{
    database_name: string;
    file_path: string;
    format: string;
  }>;
  sqlQueries?: Record<string, string>;
}

const ChatHistoryContext = createContext<ChatHistoryContextType | undefined>(undefined);

export const ChatHistoryProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { user, isAuthenticated, logout, isLoading: authLoading } = useAuth();
  const { listUserChats, getChatHistory, deleteChat: apiDeleteChat } = useApi();
  const [chatHistory, setChatHistory] = useState<ChatHistoryItem[]>([]);
  const [activeChat, setActiveChatState] = useState<ChatHistoryItem | null>(null);
  const [chatMessages, setChatMessages] = useState<{ [sessionId: string]: Message[] }>({});
  const [isLoadingChats, setIsLoadingChats] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [pendingFirstMessage, setPendingFirstMessage] = useState<string | null>(null);
  const [isCreatingChat, setIsCreatingChat] = useState(false);

  const ongoingLoads = useRef(new Set<string>());
  const hasLoadedChats = useRef(false); // Track if we've already loaded chats for this session
  const ongoingSetActiveChat = useRef(false); // Track if setActiveChat is in progress

  // Generate session ID in the format expected by backend: sess_{8_hex_chars}
  const generateSessionId = useCallback((): string => {
    const hexChars = '0123456789abcdef';
    let result = 'sess_';
    for (let i = 0; i < 8; i++) {
      result += hexChars[Math.floor(Math.random() * 16)];
    }
    return result;
  }, []);

  // Convert backend ChatListItem to frontend ChatHistoryItem
  const convertBackendChat = useCallback((backendChat: ChatListItem): ChatHistoryItem => {
    const lastUpdated = new Date(backendChat.last_seen);
    return {
      id: `chat_${backendChat.session_id}`, // Create a unique frontend ID
      session_id: backendChat.session_id,
      title: backendChat.title || "Untitled Chat",
      created_at: lastUpdated, // Use last_seen as approximation for created_at
      last_updated: lastUpdated,
      message_count: backendChat.message_count || 0,
    };
  }, []);

  // Convert backend ChatMessage to frontend Message
  const convertBackendMessage = useCallback((backendMessage: ApiChatMessage): Message => {
    return {
      role: backendMessage.role === 'assistant' ? 'agent' : backendMessage.role as 'user',
      content: backendMessage.content,
      timestamp: new Date(), // Backend doesn't provide timestamp, use current time
    };
  }, []);

  // Load conversation history for a specific session
  const loadChatHistory = useCallback(async (sessionId: string): Promise<void> => {
    if (!isAuthenticated || !user) return;

    // Prevent concurrent loads for the same session
    if (ongoingLoads.current.has(sessionId)) {
      console.log(`Load for session ${sessionId} already in progress, skipping.`);
      return;
    }

    setIsLoadingHistory(true);
    ongoingLoads.current.add(sessionId); // Mark as loading

    try {
      const backendMessages = await getChatHistory(sessionId);
      const convertedMessages = backendMessages.map(convertBackendMessage);
      
      setChatMessages((prev: { [sessionId: string]: Message[] }) => ({
        ...prev,
        [sessionId]: convertedMessages,
      }));
      // Update message_count in chatHistory to reflect actual loaded messages
      setChatHistory((prevChatHistory: ChatHistoryItem[]) =>
        prevChatHistory.map((chatItem) =>
          chatItem.session_id === sessionId
            ? { ...chatItem, message_count: convertedMessages.length, last_updated: new Date() }
            : chatItem
        )
      );

    } catch (error: unknown) {
      console.error('Error loading chat history from backend:', error);
      
      // Handle 401 errors by logging out the user
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        console.log('Authentication token invalid while loading chat history, logging out...');
        logout();
        return;
      }
      
      // Handle 404 errors gracefully (chat doesn't exist yet)
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        console.log('Chat history not found (404) - this is normal for new chats');
        setChatMessages((prev: { [sessionId: string]: Message[] }) => ({
          ...prev,
          [sessionId]: [],
        }));
        // For 404, explicitly set message_count to 0 for this chat in chatHistory
        setChatHistory((prevChatHistory: ChatHistoryItem[]) =>
          prevChatHistory.map((chatItem) =>
            chatItem.session_id === sessionId
              ? { ...chatItem, message_count: 0, last_updated: new Date() }
              : chatItem
          )
        );
      } else {
        // For other errors, still set empty array but log the error
        console.error('Unexpected error loading chat history:', error);
        setChatMessages((prev: { [sessionId: string]: Message[] }) => ({
          ...prev,
          [sessionId]: [],
        }));
        // Also set message_count to 0 here
        setChatHistory((prevChatHistory: ChatHistoryItem[]) =>
          prevChatHistory.map((chatItem) =>
            chatItem.session_id === sessionId
              ? { ...chatItem, message_count: 0, last_updated: new Date() }
              : chatItem
          )
        );
      }
    } finally {
      setIsLoadingHistory(false);
      ongoingLoads.current.delete(sessionId); // Unmark as loading
    }
  }, [isAuthenticated, user, logout, getChatHistory, convertBackendMessage]);

  const setActiveChat = useCallback(async (chat: ChatHistoryItem | null): Promise<void> => {
    console.log('setActiveChat called with:', chat);
    
    // Prevent concurrent setActiveChat operations
    if (ongoingSetActiveChat.current) {
      console.log('setActiveChat already in progress, ignoring call');
      return;
    }
    
    // If chat is null, clear the active chat state
    if (chat === null) {
      setActiveChatState(null);
      return;
    }
    
    // If chat is already the active chat, skip to prevent unnecessary reloads
    if (activeChat && activeChat.id === chat.id) {
      console.log('Chat is already active, skipping to prevent duplicate calls');
      return;
    }
    
    ongoingSetActiveChat.current = true;
    
    try {
      // Update the active chat state
      setActiveChatState(chat);
      
      // Check if we already have messages for this chat
      const existingMessages = chatMessages[chat.session_id] || [];
      const messagesLoaded = existingMessages.length > 0;
      
      // Only load history if messages aren't already loaded
      // Skip loading for new chats (no session_id yet)
      if (!isLoadingHistory && !messagesLoaded && chat.session_id) {
        console.log('Loading chat history for session:', chat.session_id);
        await loadChatHistory(chat.session_id);
      } else {
        console.log('Skipping history load - either already loaded or new chat (no session_id yet)');
      }
    } finally {
      ongoingSetActiveChat.current = false;
    }
  }, [loadChatHistory, chatMessages, activeChat, isLoadingHistory]);

  const addChat = useCallback((title?: string): ChatHistoryItem => {
    // Prevent multiple simultaneous chat creations
    if (isCreatingChat) {
      console.log('Chat creation already in progress, returning existing or throwing error');
      // Return the most recent chat or throw an error
      const latestChat = chatHistory[0];
      if (latestChat) {
        return latestChat;
      }
      throw new Error('Chat creation already in progress');
    }

    setIsCreatingChat(true);
    
    try {
      const sessionId = generateSessionId();
      const chatId = `chat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const now = new Date();
      
      const newChat: ChatHistoryItem = {
        id: chatId,
        session_id: sessionId,
        title: title || "New Chat",
        created_at: now,
        last_updated: now,
        message_count: 0,
      };

      // Add to local state immediately for responsive UI
      setChatHistory(prev => [newChat, ...prev]);
      
      return newChat;
    } finally {
      // Reset the creating state after a short delay to allow for proper state updates
      setTimeout(() => {
        setIsCreatingChat(false);
      }, 500);
    }
  }, [generateSessionId, isCreatingChat, chatHistory]);

  // Update a chat's session_id (used when backend creates the actual session_id)
  const updateChatSessionId = useCallback((chatId: string, newSessionId: string): ChatHistoryItem | null => {
    let updatedChat: ChatHistoryItem | null = null;
    let oldSessionId: string | undefined = undefined;
    
    // First, get the old session_id and update the chat
    setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => {
      if (chat.id === chatId) {
        oldSessionId = chat.session_id; // Capture the old session_id
        updatedChat = { ...chat, session_id: newSessionId };
        return updatedChat;
      }
      return chat;
    }));

    // Update the messages mapping with the new session_id if we have an old one
    if (oldSessionId && oldSessionId !== newSessionId) {
      setChatMessages((prev: { [sessionId: string]: Message[] }) => {
        const messages = prev[oldSessionId!] || [];
        const updated = { ...prev };
        delete updated[oldSessionId!]; // Remove old session_id key
        updated[newSessionId] = messages; // Add with new session_id key
        return updated;
      });
    }
    
    return updatedChat;
  }, []);

  const removeChat = useCallback((id: string): void => {
    setChatHistory((prev: ChatHistoryItem[]) => {
      const chatToRemove = prev.find(chat => chat.id === id);
      if (chatToRemove) {
        setChatMessages((prevMessages: { [sessionId: string]: Message[] }) => {
          const updated = { ...prevMessages };
          delete updated[chatToRemove.session_id];
          return updated;
        });
        
        // If we're removing the active chat, clear it
        setActiveChatState((current: ChatHistoryItem | null) => current?.id === id ? null : current);
        
        return prev.filter(chat => chat.id !== id);
      }
      return prev;
    });
  }, []);

  const deleteChat = useCallback(async (id: string): Promise<void> => {
    if (!isAuthenticated || !user) return;

    // Find the chat to get the session_id
    const chatToDelete = chatHistory.find(chat => chat.id === id);
    if (!chatToDelete) {
      console.warn('Chat not found for deletion:', id);
      return;
    }

    try {
      // Call the backend API to delete the chat
      await apiDeleteChat(chatToDelete.session_id);
      console.log('Chat deleted successfully from backend:', chatToDelete.session_id);
      
      // Remove from local state after successful backend deletion
      removeChat(id);
      
    } catch (error: unknown) {
      console.error('Error deleting chat from backend:', error);
      
      // Handle 401 errors by logging out the user
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        console.log('Authentication token invalid while deleting chat, logging out...');
        logout();
        return;
      }
      
      // Handle 404 errors (chat already deleted) by removing locally
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        console.log('Chat not found on backend (404) - removing locally');
        removeChat(id);
        return;
      }
      
      // For other errors, don't remove locally and re-throw
      throw error;
    }
  }, [isAuthenticated, user, chatHistory, apiDeleteChat, removeChat, logout]);

  const renameChat = useCallback((id: string, newTitle: string): void => {
    setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => 
      chat.id === id 
        ? { ...chat, title: newTitle, last_updated: new Date() }
        : chat
    ));
  }, []);

  // Generate a chat title from the first user message
  const generateChatTitle = useCallback((firstMessage: string): string => {
    // Take first 50 characters and clean up
    let title = firstMessage.trim().substring(0, 50);
    
    // Remove common question words and clean up
    title = title.replace(/^(what|how|when|where|why|who|can|could|would|should|is|are|do|does|did)\s+/i, '');
    
    // Capitalize first letter
    title = title.charAt(0).toUpperCase() + title.slice(1);
    
    // Add ellipsis if truncated
    if (firstMessage.length > 50) {
      title += '...';
    }
    
    return title || 'New Chat';
  }, []);

  const addMessageToChat = useCallback((sessionId: string, message: Message): void => {
    const messageWithTimestamp = {
      ...message,
      timestamp: message.timestamp || new Date(),
    };

    setChatMessages((prev: { [sessionId: string]: Message[] }) => ({
      ...prev,
      [sessionId]: [...(prev[sessionId] || []), messageWithTimestamp],
    }));

    // Update the last_updated time and message count for the chat
    setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => 
      chat.session_id === sessionId 
        ? { 
            ...chat, 
            last_updated: new Date(),
            message_count: chat.message_count + 1
          }
        : chat
    ));

    // Auto-generate title from first user message (for new chats)
    if (message.role === 'user') {
      setChatHistory((prev: ChatHistoryItem[]) => prev.map(chat => {
        if (chat.session_id === sessionId && (chat.title === 'New Chat' || chat.title === 'Untitled Chat')) {
          const title = generateChatTitle(message.content);
          return { ...chat, title };
        }
        return chat;
      }));
    }
  }, [generateChatTitle]);

  // Load chats when user authentication state changes
  useEffect(() => {
    console.log('ChatHistoryContext useEffect triggered:', {
      authLoading,
      isAuthenticated,
      hasAccessToken: !!user?.access_token,
      userId: user?.user_id,
      hasLoadedChats: hasLoadedChats.current
    });

    // Skip if still loading auth or not authenticated
    if (authLoading || !isAuthenticated || !user?.access_token) {
      console.log('Skipping chat load - auth loading or not authenticated');
      if (!authLoading && !isAuthenticated) {
        // Only clear data when definitely not authenticated (not during loading)
        setChatHistory([]);
        setChatMessages({});
        setActiveChatState(null);
        hasLoadedChats.current = false; // Reset the flag when user logs out
      }
      return;
    }

    // Prevent loading chats multiple times for the same authenticated session
    if (hasLoadedChats.current) {
      console.log('Chats already loaded for this session, skipping...');
      return;
    }

    // Load chats only once after authentication
    console.log('Loading chats for authenticated user...');
    hasLoadedChats.current = true; // Set flag before starting the load
    setIsLoadingChats(true);
    listUserChats()
      .then(backendChats => {
        console.log('Successfully loaded chats from backend:', backendChats.length);
        const convertedChats = backendChats.map(convertBackendChat);
        convertedChats.sort((a, b) => b.last_updated.getTime() - a.last_updated.getTime());
        setChatHistory(convertedChats);
      })
      .catch((error: unknown) => {
        console.error('Error loading chats from backend:', error);
        hasLoadedChats.current = false; // Reset flag on error so we can retry
        if (axios.isAxiosError(error) && error.response?.status === 401) {
          console.log('401 error while loading chats, logging out...');
          logout();
          return;
        }
        setChatHistory([]);
      })
      .finally(() => {
        setIsLoadingChats(false);
      });

  }, [authLoading, isAuthenticated, user?.access_token, user?.user_id]); // Removed function dependencies to prevent infinite loops

  // 🏢 ENTERPRISE FUNCTION: Refresh chat list from backend
  const refreshChatList = useCallback(async (): Promise<void> => {
    if (!isAuthenticated || !user?.access_token) {
      return;
    }

    try {
      console.log('Refreshing chat list from backend...');
      const backendChats = await listUserChats();
      const convertedChats = backendChats.map(convertBackendChat);
      convertedChats.sort((a, b) => b.last_updated.getTime() - a.last_updated.getTime());
      setChatHistory(convertedChats);
      console.log('Chat list refreshed successfully');
    } catch (error: unknown) {
      console.error('Error refreshing chat list:', error);
      if (axios.isAxiosError(error) && error.response?.status === 401) {
        logout();
      }
    }
  }, [isAuthenticated, user?.access_token]); // Removed function dependencies

  // Load specific chat by ID - useful for direct navigation to /chat/{chatId}
  const loadChatById = useCallback(async (chatId: string): Promise<ChatHistoryItem | null> => {
    console.log('Loading chat by ID:', chatId);

    // First check if chat is already in our chat history
    let chat = chatHistory.find(c => c.id === chatId);

    if (chat) {
      console.log('Chat found in existing history:', chat);
      await setActiveChat(chat);
      return chat;
    }

    // If not found and we haven't loaded chats yet, wait for them to load
    if (isLoadingChats) {
      console.log('Chats still loading, waiting...');
      // Return null and let the component handle the loading state
      return null;
    }

    // If chats are loaded but chat not found, the chat doesn't exist
    console.log('Chat not found in loaded chat history - chat may not exist:', chatId);
    return null;
  }, [chatHistory, isLoadingChats, setActiveChat]);

  return (
    <ChatHistoryContext.Provider value={{
      chatHistory,
      activeChat,
      chatMessages,
      isLoadingChats,
      isLoadingHistory,
      pendingFirstMessage,
      setPendingFirstMessage,
      addChat,
      updateChatSessionId,
      deleteChat,
      renameChat,
      setActiveChat,
      loadChatById,
      addMessageToChat,
      loadChatHistory,
      refreshChatList,
      generateSessionId,
    }}>
      {children}
    </ChatHistoryContext.Provider>
  );
};

export const useChatHistory = () => {
  const ctx = useContext(ChatHistoryContext);
  if (!ctx) throw new Error("useChatHistory must be used within a ChatHistoryProvider");
  return ctx;
};
