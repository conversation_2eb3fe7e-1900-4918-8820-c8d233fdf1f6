"use client";

import React, { useState } from 'react';
import { validateOnboardingCompletion } from '@/lib/utils/onboarding-state';
import { OnboardingStep } from '@/types/auth';

// Mock the step components for testing
const MockComponent = () => <div>Mock Component</div>;

export default function DebugOnboardingPage() {
  const [testResults, setTestResults] = useState<string[]>([]);

  const runValidationTests = () => {
    const results: string[] = [];
    
    results.push('🧪 Testing Onboarding Validation Logic...\n');

    // Test case 1: All steps incomplete
    const stepsAllIncomplete: OnboardingStep[] = [
      { id: 'welcome', title: 'Welcome', description: 'Welcome step', component: MockComponent, isCompleted: false },
      { id: 'profile', title: 'Profile Setup', description: 'Profile step', component: MockComponent, isCompleted: false },
      { id: 'preferences', title: 'Preferences', description: 'Preferences step', component: MockComponent, isCompleted: false, isOptional: true },
      { id: 'completion', title: 'Complete', description: 'Completion step', component: MockComponent, isCompleted: false },
    ];

    const validation1 = validateOnboardingCompletion(stepsAllIncomplete);
    results.push(`Test 1 - All incomplete:`);
    results.push(`  Can complete: ${validation1.canComplete} (expected: false)`);
    results.push(`  Missing steps: [${validation1.missingSteps.join(', ')}]`);
    results.push(`  Errors: ${validation1.errors.join('; ')}\n`);

    // Test case 2: Required steps complete, completion step incomplete
    const stepsRequiredComplete: OnboardingStep[] = [
      { id: 'welcome', title: 'Welcome', description: 'Welcome step', component: MockComponent, isCompleted: true },
      { id: 'profile', title: 'Profile Setup', description: 'Profile step', component: MockComponent, isCompleted: true },
      { id: 'preferences', title: 'Preferences', description: 'Preferences step', component: MockComponent, isCompleted: false, isOptional: true },
      { id: 'completion', title: 'Complete', description: 'Completion step', component: MockComponent, isCompleted: false },
    ];

    const validation2 = validateOnboardingCompletion(stepsRequiredComplete);
    results.push(`Test 2 - Required complete, completion incomplete:`);
    results.push(`  Can complete: ${validation2.canComplete} (expected: true)`);
    results.push(`  Missing steps: [${validation2.missingSteps.join(', ')}]`);
    results.push(`  Errors: ${validation2.errors.join('; ')}\n`);

    // Test case 3: All steps complete
    const stepsAllComplete: OnboardingStep[] = [
      { id: 'welcome', title: 'Welcome', description: 'Welcome step', component: MockComponent, isCompleted: true },
      { id: 'profile', title: 'Profile Setup', description: 'Profile step', component: MockComponent, isCompleted: true },
      { id: 'preferences', title: 'Preferences', description: 'Preferences step', component: MockComponent, isCompleted: true, isOptional: true },
      { id: 'completion', title: 'Complete', description: 'Completion step', component: MockComponent, isCompleted: true },
    ];

    const validation3 = validateOnboardingCompletion(stepsAllComplete);
    results.push(`Test 3 - All complete:`);
    results.push(`  Can complete: ${validation3.canComplete} (expected: true)`);
    results.push(`  Missing steps: [${validation3.missingSteps.join(', ')}]`);
    results.push(`  Errors: ${validation3.errors.join('; ')}\n`);

    // Test case 4: Missing required step (profile)
    const stepsMissingRequired: OnboardingStep[] = [
      { id: 'welcome', title: 'Welcome', description: 'Welcome step', component: MockComponent, isCompleted: true },
      { id: 'profile', title: 'Profile Setup', description: 'Profile step', component: MockComponent, isCompleted: false },
      { id: 'preferences', title: 'Preferences', description: 'Preferences step', component: MockComponent, isCompleted: false, isOptional: true },
      { id: 'completion', title: 'Complete', description: 'Completion step', component: MockComponent, isCompleted: false },
    ];

    const validation4 = validateOnboardingCompletion(stepsMissingRequired);
    results.push(`Test 4 - Missing required step:`);
    results.push(`  Can complete: ${validation4.canComplete} (expected: false)`);
    results.push(`  Missing steps: [${validation4.missingSteps.join(', ')}]`);
    results.push(`  Errors: ${validation4.errors.join('; ')}\n`);

    // Test case 5: Different completion step IDs
    const stepsWithDifferentCompletionId: OnboardingStep[] = [
      { id: 'welcome', title: 'Welcome', description: 'Welcome step', component: MockComponent, isCompleted: true },
      { id: 'profile', title: 'Profile Setup', description: 'Profile step', component: MockComponent, isCompleted: true },
      { id: 'finish', title: 'Finish', description: 'Finish step', component: MockComponent, isCompleted: false },
    ];

    const validation5 = validateOnboardingCompletion(stepsWithDifferentCompletionId);
    results.push(`Test 5 - Different completion ID (finish):`);
    results.push(`  Can complete: ${validation5.canComplete} (expected: true)`);
    results.push(`  Missing steps: [${validation5.missingSteps.join(', ')}]`);
    results.push(`  Errors: ${validation5.errors.join('; ')}\n`);

    results.push('🎉 Validation tests completed!');

    setTestResults(results);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            Debug Onboarding Validation
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Test the onboarding validation logic to debug completion issues
          </p>

          <button
            onClick={runValidationTests}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg mb-6"
          >
            Run Validation Tests
          </button>

          {testResults.length > 0 && (
            <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.map((line, index) => (
                <div key={index} className="whitespace-pre-wrap">
                  {line}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
