// Profile and Settings related type definitions

export interface UserProfile {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  bio?: string;
  profile_picture_url?: string;
  created_at: string;
  last_login?: string;
  auth_method?: 'email' | 'google_oauth' | 'github_oauth';
  email_verified?: boolean;
  is_active?: boolean;
}

export interface UpdateProfileRequest {
  full_name?: string;
  bio?: string;
  email?: string;
  profile_picture_url?: string;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

export interface EmailPreferencesRequest {
  marketing_emails: boolean;
  security_alerts: boolean;
  product_updates: boolean;
  weekly_digest: boolean;
}

export interface PrivacySettingsRequest {
  profile_visibility: 'public' | 'private';
  data_collection: boolean;
  analytics_opt_out: boolean;
  third_party_sharing: boolean;
}

export interface OAuthConnection {
  provider: 'google' | 'github';
  connected: boolean;
  connected_at?: string;
  email?: string;
}

export interface AccountDeletionRequest {
  confirmation_text: string;
  reason?: string;
}

// API Response types
export interface UpdateProfileResponse {
  success: boolean;
  profile: UserProfile;
  message?: string;
}

export interface ChangePasswordResponse {
  success: boolean;
  message: string;
}

export interface DataExportResponse {
  download_url: string;
  expires_at: string;
  file_size: number;
}

export interface PreferencesResponse {
  success: boolean;
  message: string;
} 