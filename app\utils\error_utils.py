from functools import wraps
import logging
from typing import Any, Callable, Coroutine, Dict
import inspect
from datetime import datetime

from app.utils.response_formatter import ResponseFormatter
from app.agents.base import AgentResponse

logger = logging.getLogger(__name__)


def friendly_errors(action_label: str = "processing") -> Callable:
    # kept for backward compatibility
    return friendly_agent_errors(action_label)


def friendly_agent_errors(action_label: str = "processing") -> Callable:
    """Decorator that converts unexpected exceptions into friendly responses.

    It transparently supports both *regular async functions* (awaitables that
    eventually resolve to a value) **and** *async generators* that yield a
    stream of events.  In the latter case the returned wrapper is itself an
    async-generator so it can still be consumed with ``async for``.
    """

    def decorator(func: Callable[..., Any]):  # Accept any callable; we inspect later

        # ────────────────────────────────────────────────────────────
        # CASE 1 ▸ original function is an async-generator
        # ────────────────────────────────────────────────────────────
        if inspect.isasyncgenfunction(func):

            @wraps(func)
            async def wrapper_gen(*args, **kwargs):  # type: ignore[override]
                self_obj = args[0] if args else None

                try:
                    async for item in func(*args, **kwargs):  # type: ignore[misc]
                        yield item
                except Exception as exc:  # noqa: BLE001
                    logger.exception("Unhandled streaming error in %s: %s", func.__qualname__, exc)
                    friendly_text = ResponseFormatter.error(action_label, str(exc))

                    error_event = {
                        "type": "error",
                        "agent": getattr(self_obj, "agent_id", "unknown_agent"),
                        "data": {"message": friendly_text, "status": "failed"},
                        "timestamp": datetime.utcnow().isoformat() + "Z",
                    }
                    yield error_event

            return wrapper_gen

        # ────────────────────────────────────────────────────────────
        # CASE 2 ▸ original function is a regular async coroutine
        # ────────────────────────────────────────────────────────────

        @wraps(func)
        async def wrapper_cor(*args, **kwargs):
            self_obj = args[0] if args else None

            try:
                return await func(*args, **kwargs)
            except Exception as exc:  # noqa: BLE001
                logger.exception("Unhandled error in %s: %s", func.__qualname__, exc)
                friendly_text = ResponseFormatter.error(action_label, str(exc))

                if hasattr(self_obj, "agent_id"):
                    return AgentResponse(
                        agent_id=getattr(self_obj, "agent_id", "unknown_agent"),
                        has_relevant_info=False,
                        error=friendly_text,
                    ).to_dict()

                return {
                    "agent": getattr(self_obj, "agent_id", "unknown_agent"),
                    "success": False,
                    "error": friendly_text,
                }

        return wrapper_cor

    return decorator

__all__ = ["friendly_errors", "friendly_agent_errors"] 