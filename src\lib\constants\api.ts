// API-related constants

export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    ME: '/auth/me',
    GOOGLE: '/auth/google',
    COMPLETE_ONBOARDING: '/auth/onboarding/complete',
  },
  
  // Database endpoints
  DATABASES: {
    LIST: '/databases/listdatabases',
    CONNECT: '/databases/connectdatabase',
    DISCONNECT: '/databases/disconnectdatabase',
    SCHEMA: '/databases/schema',
  },
  
  // Query endpoints
  QUERY: {
    ASK: '/query/ask',
  },
  
  // Chat endpoints
  CHAT: {
    LIST: '/chat/list',
    HISTORY: '/chat/history',
    DELETE: '/chat/delete',
  },
  
  // Report endpoints
  REPORTS: {
    LIST: '/reports/list',
  },
  
  // Question endpoints
  ASK: {
    QUESTION: '/ask/question',
  },

  // Chart endpoints
  CHART: {
    QUERY: '/chart/query',
  },

  // Dashboard endpoints
  DASHBOARD: {
    LIST: '/dashboard/list',
    CREATE: '/dashboard/create',
    GET: '/dashboard/get',
    UPDATE: '/dashboard/update',
    DELETE: '/dashboard/delete',
  },
} as const;

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
} as const;

// Environment configuration
export const ENVIRONMENT_CONFIG = {
  development: {
    defaultApiBase: 'http://localhost:8000',
    name: 'Development',
    enableDebugLogs: true,
  },
  production: {
    defaultApiBase: 'https://agentreportbackend.vercel.app',
    name: 'Production',
    enableDebugLogs: false,
  },
  staging: {
    defaultApiBase: 'https://staging-agentreportbackend.vercel.app',
    name: 'Staging',
    enableDebugLogs: true,
  },
} as const;

// Validate environment configuration
export const validateEnvironment = (): void => {
  const nodeEnv = process.env.NODE_ENV;
  const apiBase = process.env.NEXT_PUBLIC_API_BASE;

  // Check if NODE_ENV is valid
  if (nodeEnv && !Object.keys(ENVIRONMENT_CONFIG).includes(nodeEnv)) {
    console.warn(`⚠️  Unknown NODE_ENV: ${nodeEnv}. Expected: development, production, or staging`);
  }

  // Check if API base URL is set
  if (!apiBase) {
    console.warn('⚠️  NEXT_PUBLIC_API_BASE is not set. Using environment defaults.');
  } else {
    // Validate URL format
    try {
      new URL(apiBase);
    } catch (error) {
      console.error(`❌ Invalid NEXT_PUBLIC_API_BASE URL: ${apiBase}`);
    }
  }
};

// Get current environment configuration
export const getCurrentEnvironmentConfig = () => {
  const nodeEnv = process.env.NODE_ENV as keyof typeof ENVIRONMENT_CONFIG;
  return ENVIRONMENT_CONFIG[nodeEnv] || ENVIRONMENT_CONFIG.development;
};

// Get API base URL with proper formatting and validation
export const getApiBaseUrl = (): string => {
  // Validate environment on first call
  if (typeof window === 'undefined') {
    validateEnvironment();
  }

  let rawBaseUrl = process.env.NEXT_PUBLIC_API_BASE ?? '';
  const envConfig = getCurrentEnvironmentConfig();

  // Fallback to environment-specific defaults if not set
  if (!rawBaseUrl) {
    rawBaseUrl = envConfig.defaultApiBase;
    if (envConfig.enableDebugLogs) {
      console.log(`🔧 Using default ${envConfig.name} API URL: ${rawBaseUrl}`);
    }
  }

  // Remove trailing slashes and add /api
  const apiBaseUrl = `${rawBaseUrl.replace(/\/+$/, '')}/api`;

  // Log the API base URL in development/staging for debugging
  if (envConfig.enableDebugLogs && typeof window !== 'undefined') {
    console.log(`🔗 ${envConfig.name} API Base URL:`, apiBaseUrl);
  }

  return apiBaseUrl;
};

// Get the raw base URL without /api suffix (useful for OAuth redirects)
export const getRawApiBaseUrl = (): string => {
  let rawBaseUrl = process.env.NEXT_PUBLIC_API_BASE ?? '';
  const envConfig = getCurrentEnvironmentConfig();

  if (!rawBaseUrl) {
    rawBaseUrl = envConfig.defaultApiBase;
  }

  return rawBaseUrl.replace(/\/+$/, '');
};

// Storage keys for localStorage
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  TOKEN_TYPE: 'tokenType',
  USER_ID: 'userId',
  EXPIRES_AT: 'expiresAt',
} as const;
