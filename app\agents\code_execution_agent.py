"""
Code-Execution-Agent v2
───────────────────────
Runs code bundle in a temp dir, first executes `setup_code+test_code`.
If the test fails -> returns `error_test` so <PERSON><PERSON> can retry once.
"""

from __future__ import annotations
import asyncio, json, logging, os, tempfile, textwrap, uuid, subprocess, sys, time
from pathlib import Path
from typing import Dict, Any, List, Optional
from decimal import Decimal
import platform

from app.agents.base import Agent, AgentResponse, StandardizedAgentOutputs, AgentMemoryItem
from app.services.report_storage_service import ReportStorageService
from app.config.settings import REPORTS_BUCKET

logger = logging.getLogger(__name__)
_TIMEOUT = 300  # seconds


class CodeExecutionAgent(Agent):
    def __init__(self, agent_id: str | None = None) -> None:
        self.agent_id = agent_id or "code_execution_agent"
        self._store   = ReportStorageService()
        self.initialised = False

    async def initialize(self) -> None:
        self.initialised = True

    async def process(self, msg: Dict[str, Any]) -> Dict[str, Any]:
        if not self.initialised:
            await self.initialize()

        try:
            bundle      = msg.get("code_artifacts", {})
            datasets    = msg.get("datasets", [])  
            session_id  = msg.get("session_id", "unknown")
            user_id     = msg.get("user_id", "unknown")
            stage       = msg.get("stage", "main")  # exploration, analysis, visualization, or main

            logger.info(f"🔧 Executing code for session {session_id}")
            logger.debug(f"User: {user_id}, Datasets: {len(datasets)}, Stage: {stage}")

            # Validate required fields
            if not bundle:
                logger.error("❌ No code artifacts provided")
                return AgentResponse.error(
                    self.agent_id, 
                    "No code artifacts provided",
                    error_details={"provided_keys": list(msg.keys())}
                ).to_dict()

            # one temp dir per run
            with tempfile.TemporaryDirectory() as tmp:
                tmp_path = Path(tmp)
                logger.debug(f"Temp directory: {tmp_path}")

                # Download previous stage results if needed
                await self._download_previous_results(tmp_path, session_id, user_id, stage)

                # 1 · write user code ------------------------------------------------
                script = tmp_path / "analysis.py"
                try:
                    script_content = self._compose_script(bundle, datasets, stage)
                    script.write_text(script_content, encoding='utf-8')
                    logger.debug(f"Script written: {len(script_content)} characters")
                except Exception as e:
                    logger.error(f"❌ Failed to compose script: {e}")
                    return AgentResponse.error(
                        self.agent_id,
                        f"Script composition failed: {e}",
                        error_details={"script_error": str(e), "bundle_keys": list(bundle.keys())}
                    ).to_dict()

                # 2 · run ------------------------------------------------------------
                logger.info("⚡ Executing analysis script...")
                result = await self._run_python(script)
                logger.info(f"📊 Execution complete: return_code={result['return_code']}, duration={result['duration']:.2f}s")

                # auto-retry if self-test failed once
                if result["return_code"] != 0 and "AssertionError" in result["stderr"][:400]:
                    logger.info("🔄 Self-test failed — attempting 1 retry")
                    result = await self._run_python(script)  # run same file again (user may modify)
                    logger.info(f"🔁 Retry result: return_code={result['return_code']}")

                # Log execution status
                if result["return_code"] == 0:
                    logger.info("✅ Script executed successfully")
                else:
                    logger.error(f"❌ Script execution failed with code {result['return_code']}")
                
                # Only log stderr if there's an error, and truncate stdout
                if result.get("stderr") and result["return_code"] != 0:
                    logger.error(f"📤 STDERR:\n{result['stderr'][-1000:]}")  # Last 1000 chars
                
                # Log a summary of stdout if successful
                if result.get("stdout") and result["return_code"] == 0:
                    stdout_lines = result["stdout"].split('\n')
                    if len(stdout_lines) > 10:
                        logger.info(f"📤 STDOUT Summary: {len(stdout_lines)} lines of output")
                        # Log key analysis results if found (not just ML)
                        for line in stdout_lines[-10:]:  # Last 10 lines often have summary
                            if any(keyword in line.lower() for keyword in ['summary:', 'total:', 'analysis complete', 'findings:', 'insights:']):
                                logger.info(f"🎯 {line.strip()}")
                    else:
                        logger.debug(f"📤 STDOUT:\n{result['stdout']}")

                # 3 · upload artefacts ----------------------------------------------
                uploaded = await self._upload_outputs(tmp_path, session_id, user_id, stage)
                if uploaded:
                    logger.info(f"📁 Uploaded {len(uploaded)} files: {[f['filename'] for f in uploaded]}")

                # 3.5 · Extract analysis results if available ---------------------------
                analysis_results = await self._extract_analysis_results(tmp_path)
                if analysis_results:
                    logger.info(f"🎯 Analysis Results: {analysis_results.get('summary', 'Results extracted successfully')}")
                else:
                    logger.debug(f"📊 Analysis results extracted: {analysis_results}")

                # 4 · build final response with standardized format -----------------
                execution_success = result.get("return_code", -1) == 0
                
                # Determine if retry is needed
                if not execution_success and result.get("stderr"):
                    # Check for common retry-able errors
                    stderr = result.get("stderr", "")
                    if any(error_type in stderr for error_type in ["ModuleNotFoundError", "ImportError", "SyntaxError"]):
                        status = "retry_needed"
                    else:
                        status = "error"
                else:
                    status = "completed" if execution_success else "error"

                # Create key results
                key_results = {
                    "execution_success": execution_success,
                    "return_code": result.get("return_code", -1),
                    "files_generated": len(uploaded),
                    "has_analysis_results": bool(analysis_results),
                    "execution_time": result.get("duration", 0),
                    "stage": stage
                }

                # Add analysis metrics if available
                if analysis_results:
                    if "accuracy" in analysis_results:
                        key_results["model_accuracy"] = analysis_results["accuracy"]
                    if "best_model" in analysis_results:
                        key_results["best_model"] = analysis_results["best_model"]

                # Create context for next agent
                context_for_next_agent = {
                    "execution_results": {
                        "return_code": result.get("return_code", -1),
                        "stdout": result.get("stdout", "")[-1000:],  # Last 1000 chars
                        "stderr": result.get("stderr", "")[-1000:],
                        "execution_time": result.get("duration", 0),
                        "output_files": uploaded,
                        "analysis_results": analysis_results
                    },
                    "ready_for_dashboard": execution_success and bool(uploaded),
                    "stage": stage
                }

                # Create memory items
                memory_items = [
                    AgentMemoryItem(
                        item_type="execution_result",
                        content={
                            "success": execution_success,
                            "return_code": result.get("return_code", -1),
                            "stage": stage,
                            "files_count": len(uploaded)
                        },
                        importance="high",
                        context_hint=f"Code execution {'succeeded' if execution_success else 'failed'} for {stage} stage"
                    ).to_dict()
                ]

                if analysis_results:
                    memory_items.append(
                        AgentMemoryItem(
                            item_type="analysis_insights",
                            content=analysis_results,
                            importance="high",
                            context_hint="Analysis results and insights from code execution"
                        ).to_dict()
                    )

                # Create detailed error information for failed executions
                error_details = {}
                if not execution_success:
                    error_details = {
                        "return_code": result.get("return_code", -1),
                        "stderr": result.get("stderr", "")[:1000],  # First 1000 chars for error analysis
                        "stdout": result.get("stdout", "")[:500],   # First 500 chars for context
                        "stage": stage,
                        "execution_time": result.get("duration", 0)
                    }

                # Create the response
                if execution_success:
                    return AgentResponse.success(
                        self.agent_id,
                        data={  # Legacy compatibility
                            "summary": f"{stage.title()} execution finished",
                            "return_code": result.get("return_code", -1),
                            "stdout": result.get("stdout", "")[-1000:],
                            "stderr": result.get("stderr", "")[-1000:],
                            "output_files": uploaded,
                            "execution_time": result.get("duration", 0),
                            "stage": stage,
                            "analysis_results": analysis_results
                        },
                        result_summary=f"Successfully executed {stage} code, generated {len(uploaded)} files" + 
                                      (f" with {analysis_results.get('accuracy', 0):.1%} model accuracy" if analysis_results.get('accuracy') else ""),
                        key_results=key_results,
                        context_for_next_agent=context_for_next_agent,
                        memory_items=memory_items,
                        metadata={
                            "stage": stage,
                            "files_generated": [f["filename"] for f in uploaded],
                            "has_insights": bool(analysis_results)
                        },
                        execution_time=result.get("duration", 0),
                        output_files=uploaded
                    ).to_dict()
                else:
                    # Execution failed
                    error_message = f"Code execution failed with return code {result.get('return_code', -1)}"
                    if result.get("stderr"):
                        error_message += f": {result.get('stderr', '')[:200]}..."

                    return AgentResponse(
                        agent_id=self.agent_id,
                        has_relevant_info=False,
                        status=status,  # "error" or "retry_needed"
                        error=error_message,
                        error_details=error_details,
                        data={  # Legacy compatibility
                            "summary": f"{stage.title()} execution failed",
                            "return_code": result.get("return_code", -1),
                            "stdout": result.get("stdout", "")[-1000:],
                            "stderr": result.get("stderr", "")[-1000:],
                            "output_files": uploaded,
                            "execution_time": result.get("duration", 0),
                            "stage": stage
                        },
                        result_summary=f"Code execution failed: {error_message}",
                        key_results=key_results,
                        context_for_next_agent=context_for_next_agent,
                        memory_items=memory_items,
                        metadata={
                            "stage": stage,
                            "error_type": "execution_failure",
                            "retry_recommended": status == "retry_needed"
                        },
                        execution_time=result.get("duration", 0)
                    ).to_dict()

        except Exception as e:
            logger.exception("💥 Code execution failed with exception")
            return AgentResponse.error(
                self.agent_id,
                f"Code execution failed: {e}",
                error_details={
                    "exception_type": type(e).__name__,
                    "exception_message": str(e),
                    "stage": msg.get("stage", "unknown")
                }
            ).to_dict()

    # helpers -----------------------------------------------------------------
    
    def _convert_decimals(self, obj):
        """Convert Decimal objects to regular Python types for JSON serialization."""
        if isinstance(obj, Decimal):
            # Convert to int if it's a whole number, otherwise to float
            if obj % 1 == 0:
                return int(obj)
            else:
                return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_decimals(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_decimals(item) for item in obj]
        else:
            return obj

    def _compose_script(self,
                        bundle: Dict[str, Any],
                        datasets: list[dict[str, Any]],
                        stage: str = "main") -> str:

        # Convert any Decimal objects to regular Python types before JSON serialization
        datasets_clean = self._convert_decimals(datasets)
        
        # make the dataset list JSON-serialisable inside the script
        datasets_json = json.dumps(datasets_clean, indent=2)

        # Check if this is an interactive stage execution
        if "exploration_code" in bundle or stage == "exploration":
            # Exploration stage
            return "\n\n".join([
                "# -*- coding: utf-8 -*-",
                "# --- setup ---",
                bundle.get("setup_code", "# No setup code provided"),
                "# --- datasets metadata (auto-injected) ---",
                f"datasets_to_load = {datasets_json}",
                "# --- exploration ---",
                bundle.get("exploration_code", "# No exploration code provided"),
                "# --- finish ---",
                "print('Exploration stage finished OK')",
            ])
        elif "analysis_code" in bundle or stage == "analysis":
            # Analysis stage - load previous results  
            return "\n\n".join([
                "# -*- coding: utf-8 -*-",
                "# --- setup (reuse from exploration) ---",
                "import pandas as pd",
                "import numpy as np", 
                "import json",
                "from pathlib import Path",
                "import os",
                "",
                "# Load datasets metadata",
                f"datasets_to_load = {datasets_json}",
                "",
                "# Load previous exploration results",
                "try:",
                "    with open('exploration_results.json', 'r', encoding='utf-8') as f:",
                "        exploration_results = json.load(f)",
                "    print('[INFO] Loaded exploration results: ' + str(list(exploration_results.keys())))",
                "except FileNotFoundError:",
                "    print('[WARNING] No exploration results found, proceeding without them')",
                "    exploration_results = {}",
                "except Exception as e:",
                "    print('[ERROR] Failed to load exploration results: ' + str(e))",
                "    exploration_results = {}",
                "",
                "# --- analysis ---",
                bundle.get("analysis_code", "# No analysis code provided"),
                "# --- finish ---",
                "print('Analysis stage finished OK')",
            ])
        elif "visualization_code" in bundle or stage == "visualization":
            # Visualization stage - load all previous results
            return "\n\n".join([
                "# -*- coding: utf-8 -*-",
                "# --- setup (reuse from previous stages) ---",
                "import pandas as pd",
                "import numpy as np",
                "import matplotlib.pyplot as plt",
                "import seaborn as sns",
                "import json",
                "from pathlib import Path",
                "import os",
                "",
                "# Load datasets metadata",
                f"datasets_to_load = {datasets_json}",
                "",
                "# Load previous results",
                "try:",
                "    with open('exploration_results.json', 'r', encoding='utf-8') as f:",
                "        exploration_results = json.load(f)",
                "    print('[INFO] Loaded exploration results')",
                "except FileNotFoundError:",
                "    exploration_results = {}",
                "except Exception as e:",
                "    print('[ERROR] Failed to load exploration results: ' + str(e))",
                "    exploration_results = {}",
                "",
                "try:",
                "    with open('analysis_results.json', 'r', encoding='utf-8') as f:",
                "        analysis_results = json.load(f)",
                "    print('[INFO] Loaded analysis results')",
                "except FileNotFoundError:",
                "    analysis_results = {}",
                "except Exception as e:",
                "    print('[ERROR] Failed to load analysis results: ' + str(e))",
                "    analysis_results = {}",
                "",
                "# --- visualization ---",
                bundle.get("visualization_code", "# No visualization code provided"),
                "# --- finish ---",
                "print('Visualization stage finished OK')",
            ])
        else:
            # Original single-shot execution
            return "\n\n".join([
                "# -*- coding: utf-8 -*-",
                "# --- setup ---",
                bundle.get("setup_code", "# No setup code provided"),
                "# --- datasets metadata (auto-injected) ---",
                f"datasets_to_load = {datasets_json}",
                "# --- main ---",
                bundle.get("main_code", "# No main code provided"),
                "# --- tests ---",
                bundle.get("test_code", "# No test code provided"),
                "# --- finish ---",
                "print('Analysis script finished OK')",
            ])

    async def _run_python(self, script: Path) -> Dict[str, Any]:
        """Run Python script with Windows compatibility."""
        start_time = time.time()
        
        try:
            # For Windows compatibility, use different approach
            if platform.system() == "Windows":
                # Use regular subprocess with asyncio wrapper for Windows
                result = await asyncio.get_event_loop().run_in_executor(
                    None, self._run_subprocess_sync, script
                )
            else:
                # Use asyncio subprocess for Unix-like systems
                proc = await asyncio.create_subprocess_exec(
                    sys.executable, str(script),
                    stdout=asyncio.subprocess.PIPE, 
                    stderr=asyncio.subprocess.PIPE,
                    cwd=script.parent
                )
                try:
                    stdout, stderr = await asyncio.wait_for(proc.communicate(), timeout=_TIMEOUT)
                    result = {
                        "return_code": proc.returncode,
                        "stdout": stdout.decode('utf-8', errors='replace'),
                        "stderr": stderr.decode('utf-8', errors='replace'),
                    }
                except asyncio.TimeoutError:
                    proc.kill()
                    result = {
                        "return_code": -1,
                        "stdout": "",
                        "stderr": "Execution timed out"
                    }
                    
        except Exception as e:
            logger.exception("Failed to run Python script")
            result = {
                "return_code": -1,
                "stdout": "",
                "stderr": f"Execution failed: {e}"
            }

        duration = time.time() - start_time
        result["duration"] = duration
        return result

    def _run_subprocess_sync(self, script: Path) -> Dict[str, Any]:
        """Synchronous subprocess execution for Windows compatibility."""
        try:
            proc = subprocess.Popen(
                [sys.executable, str(script)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=script.parent,
                text=True,
                encoding='utf-8',
                errors='replace'  # Replace invalid UTF-8 characters instead of failing
            )
            
            try:
                stdout, stderr = proc.communicate(timeout=_TIMEOUT)
                return {
                    "return_code": proc.returncode,
                    "stdout": stdout or "",  # Ensure not None
                    "stderr": stderr or "",  # Ensure not None
                }
            except subprocess.TimeoutExpired:
                proc.kill()
                return {
                    "return_code": -1,
                    "stdout": "",
                    "stderr": "Execution timed out"
                }
                
        except Exception as e:
            return {
                "return_code": -1,
                "stdout": "",
                "stderr": f"Subprocess execution failed: {e}"
            }

    async def _download_previous_results(self, tmp_path: Path, session_id: str, user_id: str, stage: str):
        """Download previous stage results to make them available in current execution."""
        try:
            if stage in ["analysis", "visualization"]:
                # Try to download exploration_results.json using new unified structure
                exploration_key = f"users/{user_id}/sessions/{session_id}/artifacts/exploration_results.json"
                try:
                    exploration_data = self._store.download_bytes(exploration_key)
                    exploration_file = tmp_path / "exploration_results.json"
                    exploration_file.write_bytes(exploration_data)
                    logger.info(f"✅ Downloaded exploration results for {stage} stage")
                except Exception as e:
                    # Fallback to legacy structure
                    try:
                        legacy_key = f"reports/{user_id}/{session_id}/exploration_results.json"
                        exploration_data = self._store.download_bytes(legacy_key)
                        exploration_file = tmp_path / "exploration_results.json"
                        exploration_file.write_bytes(exploration_data)
                        logger.info(f"✅ Downloaded exploration results (legacy) for {stage} stage")
                    except Exception:
                        logger.info(f"ℹ️ No exploration results found in new or legacy structure")
            
            if stage == "visualization":
                # Try to download analysis_results.json using new unified structure
                analysis_key = f"users/{user_id}/sessions/{session_id}/artifacts/analysis_results.json"
                try:
                    analysis_data = self._store.download_bytes(analysis_key)
                    analysis_file = tmp_path / "analysis_results.json"
                    analysis_file.write_bytes(analysis_data)
                    logger.info(f"✅ Downloaded analysis results for {stage} stage")
                except Exception as e:
                    # Fallback to legacy structure
                    try:
                        legacy_key = f"reports/{user_id}/{session_id}/analysis_results.json"
                        analysis_data = self._store.download_bytes(legacy_key)
                        analysis_file = tmp_path / "analysis_results.json"
                        analysis_file.write_bytes(analysis_data)
                        logger.info(f"✅ Downloaded analysis results (legacy) for {stage} stage")
                    except Exception:
                        logger.info(f"ℹ️ No analysis results found in new or legacy structure")
        except Exception as e:
            logger.warning(f"⚠️ Failed to download previous results: {e}")

    async def _upload_outputs(self, folder: Path, session_id: str, user_id: str, stage: str = "main") -> List[Dict[str, Any]]:
        """Upload output files with error handling and stage-specific naming."""
        uploaded: list[Dict[str, Any]] = []
        try:
            # Check for output subdirectory first (common pattern for generated code)
            output_dir = folder / "output"
            search_dirs = []
            
            if output_dir.exists() and output_dir.is_dir():
                search_dirs.append(output_dir)
                logger.info(f"🔍 Found output subdirectory: {output_dir}")
                
            # Always search the main directory too
            search_dirs.append(folder)
            
            for search_dir in search_dirs:
                logger.info(f"🔍 Searching for output files in: {search_dir}")
                try:
                    files_found = list(search_dir.iterdir())
                    logger.info(f"📄 Found {len(files_found)} items in {search_dir}")
                    
                    for f in files_found:
                        if f.is_file() and f.suffix not in {".py"}:  # Include .txt files for ML results
                            try:
                                # Use unified storage structure for organized file handling
                                blob = f.read_bytes()
                                
                                if f.name.endswith('_results.json') or f.name in ['exploration_results.json', 'analysis_results.json']:
                                    # Store artifacts using unified structure
                                    result = self._store.upload_artifact(
                                        blob=blob,
                                        user_id=user_id,
                                        session_id=session_id,
                                        artifact_name=f.name
                                    )
                                    url = result['url']
                                    key = result['key']
                                elif f.suffix.lower() in ['.png', '.jpg', '.jpeg', '.svg']:
                                    # Store visualizations using unified structure
                                    result = self._store.upload_visualization(
                                        blob=blob,
                                        user_id=user_id,
                                        session_id=session_id,
                                        chart_type=f.stem,
                                        file_extension=f.suffix.lstrip('.')
                                    )
                                    url = result['url']
                                    key = result['key']
                                else:
                                    # Store other files as reports using unified structure
                                    url = self._store.upload_report(
                                        blob=blob,
                                        user_id=user_id,
                                        session_id=session_id,
                                        report_type=f.stem,
                                        file_extension=f.suffix.lstrip('.') or 'dat'
                                    )
                                    key = f"users/{user_id}/sessions/{session_id}/reports/{f.name}"
                                uploaded.append({"filename": f.name, "url": url, "key": key})
                                logger.debug(f"✅ Uploaded: {f.name}")
                            except Exception as e:
                                logger.warning(f"Failed to upload {f.name}: {e}")
                except Exception as e:
                    logger.warning(f"Failed to scan directory {search_dir}: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to process output folder: {e}")
        
        logger.info(f"📁 Total files uploaded: {len(uploaded)}")
        return uploaded

    async def _extract_analysis_results(self, folder: Path) -> Dict[str, Any]:
        """Extract analysis results from output files."""
        analysis_results = {}
        
        try:
            # Check for output subdirectory first (like _upload_outputs does)
            output_dir = folder / "output"
            search_dirs = []
            
            if output_dir.exists() and output_dir.is_dir():
                search_dirs.append(output_dir)
                logger.debug(f"🔍 Found output subdirectory for analysis results: {output_dir}")
                
            # Always search the main directory too
            search_dirs.append(folder)
            
            for search_dir in search_dirs:
                logger.debug(f"🔍 Searching for analysis results in: {search_dir}")
                
                # Look for common analysis result files that the generated code creates
                for filename in ["analysis_summary.txt", "data_summary.txt", "insights_summary.txt", 
                               "results_summary.txt", "findings.txt", "report.txt",
                               "model_comparison.csv", "statistical_results.csv", "summary_stats.csv"]:
                    result_file = search_dir / filename
                    if result_file.exists():
                        try:
                            logger.debug(f"📄 Found analysis results file: {filename}")
                            
                            if filename.endswith('.csv'):
                                # Parse CSV files (like model_comparison.csv)
                                import pandas as pd
                                df = pd.read_csv(result_file)
                                
                                if 'Accuracy' in df.columns:
                                    best_idx = df['Accuracy'].idxmax()
                                    best_model = df.loc[best_idx, 'Model']
                                    accuracy = df.loc[best_idx, 'Accuracy']
                                    
                                    analysis_results['best_model'] = best_model
                                    analysis_results['accuracy'] = float(accuracy)
                                    
                                    # Add other metrics if available
                                    for metric in ['Precision', 'Recall', 'F1 Score', 'AUC-ROC']:
                                        if metric in df.columns and not pd.isna(df.loc[best_idx, metric]):
                                            analysis_results[metric.lower().replace(' ', '_').replace('-', '_')] = float(df.loc[best_idx, metric])
                                    
                                    logger.info(f"📊 Found analysis results in {filename}: {best_model} with {accuracy:.1%} accuracy")
                                    
                            else:
                                # Parse text files
                                content = result_file.read_text(encoding='utf-8')
                                lines = content.split('\n')
                                
                                for line in lines:
                                    line = line.strip()
                                    if not line:
                                        continue
                                    
                                    # Extract general analysis insights and findings
                                    # Look for key findings or summary statements
                                    if any(keyword in line.lower() for keyword in ['key finding:', 'insight:', 'conclusion:', 'summary:']):
                                        if 'key_findings' not in analysis_results:
                                            analysis_results['key_findings'] = []
                                        analysis_results['key_findings'].append(line)
                                        continue
                                        
                                    # Parse various formats for accuracy (ML-specific but optional)
                                    if 'accuracy' in line.lower():
                                        try:
                                            # Extract accuracy value from various formats
                                            import re
                                            # Look for patterns like "Accuracy: 0.97", "accuracy = 0.97", "accuracy 97%"
                                            numbers = re.findall(r'(\d+\.?\d*)', line)
                                            if numbers:
                                                acc_val = float(numbers[0])
                                                # Convert percentage to decimal if needed
                                                if acc_val > 1:
                                                    acc_val = acc_val / 100.0
                                                analysis_results['accuracy'] = acc_val
                                                logger.info(f"📄 Found accuracy in {filename}: {acc_val:.1%}")
                                        except (ValueError, IndexError):
                                            pass
                                    
                                    # Look for best model (ML-specific but optional)
                                    if 'best model' in line.lower() and 'best_model' not in analysis_results:
                                        try:
                                            model_name = line.split(':', 1)[1].strip()
                                            analysis_results['best_model'] = model_name
                                        except IndexError:
                                            pass
                                    
                                    # Look for statistical metrics (broader than just ML)
                                    for metric in ['correlation', 'mean', 'median', 'std', 'count', 'total', 'average', 'f1 score', 'precision', 'recall']:
                                        if metric in line.lower() and ':' in line:
                                            try:
                                                import re
                                                numbers = re.findall(r'(\d+\.?\d*)', line)
                                                if numbers:
                                                    analysis_results[metric.replace(' ', '_')] = float(numbers[0])
                                            except (ValueError, IndexError):
                                                pass
                                    
                                    # Capture general summary information
                                    if 'total' in line.lower() and ('records' in line.lower() or 'rows' in line.lower()):
                                        analysis_results['data_summary'] = line
                                
                                if analysis_results:
                                    logger.info(f"📄 Extracted analysis results from {filename}")
                                    
                        except Exception as e:
                            logger.warning(f"Failed to parse {filename}: {e}")
                            
                # If we found results, break out of search_dirs loop to avoid duplicates
                if analysis_results:
                    break
                    
        except Exception as e:
            logger.error(f"Failed to extract analysis results: {e}")
            
        if analysis_results:
            logger.info(f"🎯 Final Analysis Results: {analysis_results}")
        else:
            logger.warning("⚠️ No analysis results found in any output files")
            
        return analysis_results
