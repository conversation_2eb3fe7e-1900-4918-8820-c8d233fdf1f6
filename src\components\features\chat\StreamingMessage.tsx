import React, { useState, useEffect, useRef, useMemo } from 'react';
import { TypewriterConfig } from '@/types/streaming';
import AnalyticalContentRenderer from './AnalyticalContentRenderer';

interface StreamingMessageProps {
  content: string;
  isStreaming: boolean;
  className?: string;
  typewriterConfig?: Partial<TypewriterConfig>;
}

const defaultTypewriterConfig: TypewriterConfig = {
  speed: 0, // No delay for real-time streaming
  enableCursor: true,
  cursorChar: '|',
};

export const StreamingMessage: React.FC<StreamingMessageProps> = React.memo(({
  content,
  isStreaming,
  className = '',
  typewriterConfig = {},
}) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const [showCursor, setShowCursor] = useState(false);
  const cursorIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const [lastContentLength, setLastContentLength] = useState(0);

  // Memoize config to prevent unnecessary re-renders
  const config = useMemo(() => ({ ...defaultTypewriterConfig, ...typewriterConfig }), [typewriterConfig]);

  // Real-time content display (no typewriter delay for streaming)
  useEffect(() => {
    if (config.speed === 0) {
      // For real-time streaming, show content immediately
      setDisplayedContent(content);
      setLastContentLength(content.length);
    } else {
      // Traditional typewriter effect for non-streaming content
      if (!content) {
        setDisplayedContent('');
        setLastContentLength(0);
        return;
      }

      // If content is shorter than current display (e.g., reset), reset immediately
      if (content.length < displayedContent.length) {
        setDisplayedContent(content);
        setLastContentLength(content.length);
        return;
      }

      // If we're already displaying the full content, no need to animate
      if (displayedContent === content && !isStreaming) {
        return;
      }

      let currentIndex = displayedContent.length;
      const interval = setInterval(() => {
        if (currentIndex < content.length) {
          currentIndex++;
          setDisplayedContent(content.slice(0, currentIndex));
          setLastContentLength(currentIndex);
        } else {
          clearInterval(interval);
        }
      }, config.speed);

      return () => clearInterval(interval);
    }
  }, [content, config.speed, isStreaming, displayedContent.length]);

  // Memoize the rendered content to prevent unnecessary re-renders
  const memoizedAnalyticalContent = useMemo(() => (
    <AnalyticalContentRenderer
      content={displayedContent}
      isStreaming={isStreaming}
      className="inline"
    />
  ), [displayedContent, isStreaming]);

  // Cursor blinking effect
  useEffect(() => {
    if (!config.enableCursor || !isStreaming) {
      setShowCursor(false);
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
        cursorIntervalRef.current = null;
      }
      return;
    }

    // Show cursor immediately when streaming starts
    setShowCursor(true);

    // Start cursor blinking
    cursorIntervalRef.current = setInterval(() => {
      setShowCursor(prev => !prev);
    }, 600);

    return () => {
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
        cursorIntervalRef.current = null;
      }
    };
  }, [isStreaming, config.enableCursor]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (cursorIntervalRef.current) {
        clearInterval(cursorIntervalRef.current);
      }
    };
  }, []);

  return (
    <div className={`streaming-message ${className}`}>
      <div className="relative">
        <div className="text-sm">
          {memoizedAnalyticalContent}
          {isStreaming && config.enableCursor && (
            <span
              className={`inline-block ml-0.5 text-blue-500 font-bold transition-opacity duration-150 ${
                showCursor ? 'opacity-100' : 'opacity-0'
              }`}
              style={{ width: '2px' }}
            >
              {config.cursorChar}
            </span>
          )}
        </div>
        {/* Subtle glow effect when streaming */}
        {isStreaming && (
          <div className="absolute inset-0 bg-gradient-to-r from-blue-50/20 to-transparent rounded pointer-events-none animate-pulse" />
        )}
      </div>
    </div>
  );
});

StreamingMessage.displayName = 'StreamingMessage';

export default StreamingMessage;
