"""User Models

This module defines user-related models used for authentication and user management.
"""

from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field, validator
import uuid
import re

class UserRole(str, Enum):
    """Enumeration of supported user roles."""
    ADMIN = "admin"
    USER = "user"

class AuthProvider(str, Enum):
    """Enumeration of supported authentication providers."""
    LOCAL = "local"
    GOOGLE = "google"

class User(BaseModel):
    """Model representing a user in the system."""
    id: str = Field(default_factory=lambda: f"user_{uuid.uuid4().hex[:8]}")
    email: str
    hashed_password: Optional[str] = None
    full_name: Optional[str] = None
    is_active: bool = True
    is_email_verified: bool = False
    role: UserRole = UserRole.USER
    auth_provider: AuthProvider = AuthProvider.LOCAL
    auth_provider_id: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    profile_picture: Optional[str] = None
    settings: Dict[str, Any] = Field(default_factory=dict)
    is_new_user: Optional[bool] = None  # For user state detection
    
    @validator('email')
    def validate_email(cls, v):
        """Validate email format."""
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, v):
            raise ValueError("Invalid email format")
        return v
    
    class Config:
        """Pydantic config for the User model."""
        use_enum_values = True
        
class UserCreate(BaseModel):
    """Model for user creation request."""
    email: str
    password: str
    full_name: Optional[str] = None
    
    @validator('email')
    def validate_email(cls, v):
        """Validate email format."""
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, v):
            raise ValueError("Invalid email format")
        return v
    
    @validator('password')
    def password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one number")
        return v

class UserLogin(BaseModel):
    """Model for user login request."""
    email: str
    password: str
    
    @validator('email')
    def validate_email(cls, v):
        """Validate email format."""
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, v):
            raise ValueError("Invalid email format")
        return v

class UserResponse(BaseModel):
    """Model for user response (for API responses)."""
    id: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_email_verified: bool
    role: str
    auth_provider: str
    created_at: datetime
    profile_picture: Optional[str]

class UserUpdate(BaseModel):
    """Model for user update request."""
    full_name: Optional[str] = None
    password: Optional[str] = None
    profile_picture: Optional[str] = None
    
    @validator('password')
    def password_strength(cls, v):
        """Validate password strength if provided."""
        if v is not None:
            if len(v) < 8:
                raise ValueError("Password must be at least 8 characters long")
            if not any(c.isupper() for c in v):
                raise ValueError("Password must contain at least one uppercase letter")
            if not any(c.islower() for c in v):
                raise ValueError("Password must contain at least one lowercase letter")
            if not any(c.isdigit() for c in v):
                raise ValueError("Password must contain at least one number")
        return v

class Token(BaseModel):
    """Model for authentication token with user state detection."""
    access_token: str
    token_type: str = "bearer"
    expires_at: datetime
    refresh_token: Optional[str] = None
    user_id: str
    is_new_user: bool = False

class AuthenticationResponse(BaseModel):
    """Enhanced authentication response with user state detection."""
    access_token: str
    refresh_token: Optional[str] = None
    token_type: str = "bearer"
    expires_in: int
    user_id: str
    is_new_user: bool
    user_data: Optional[Dict[str, Any]] = None
    client_type: Optional[str] = None
    correlation_id: Optional[str] = None

class TokenData(BaseModel):
    """Model for token data (for internal use)."""
    user_id: str
    email: Optional[str] = None
    role: Optional[str] = None
    exp: Optional[int] = None

class PasswordReset(BaseModel):
    """Model for password reset request."""
    token: str
    new_password: str
    
    @validator('new_password')
    def password_strength(cls, v):
        """Validate password strength."""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one number")
        return v 