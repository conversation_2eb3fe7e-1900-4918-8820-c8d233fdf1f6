"""
File-Ingestion Service
––––––––––––––––––––––
• Accepts an UploadFile from FastAPI
• Streams it straight to S3 under  user-uploads/<user>/<uuid>.<ext>
• Derives very light metadata (columns, row-count sample, format)
• Registers the file in the Dataset-Catalog table so the normal
  DatasetManagerAgent pipeline can pick it up immediately.
"""

from __future__ import annotations

import csv, io, logging, uuid
from typing import Dict, Any, Tu<PERSON>, List

import boto3
import pandas as pd
from fastapi import UploadFile

from app.services.dataset_catalog_service import DatasetCatalogService
from app.config.settings import REPORTS_BUCKET

logger = logging.getLogger(__name__)


class FileIngestionService:
    """Light-weight helper to ingest ad-hoc user files."""

    def __init__(self) -> None:
        self.s3 = boto3.client("s3")
        self.catalog = DatasetCatalogService()

    # ──────────────────────────────────────────────────────────────
    # public
    # ──────────────────────────────────────────────────────────────
    async def ingest_file(
        self,
        *,
        upload: UploadFile,
        user_id: str,
    ) -> Dict[str, Any]:
        """
        • uploads the raw bytes to S3
        • extracts a cheap header+row-count sample
        • returns the freshly created dataset-meta dict
        """
        # 1 ▸ decide file-format + dataset ID
        file_ext = (upload.filename.rsplit(".", 1)[-1] or "bin").lower()
        file_fmt = "excel" if file_ext in ("xlsx", "xls") else file_ext
        dataset_id = f"user_ds_{uuid.uuid4().hex[:8]}"

        # 2 ▸ read file content into memory first - with proper error handling
        try:
            # Try to reset file position to beginning (some file objects don't support this)
            try:
                await upload.seek(0)
            except (AttributeError, OSError):
                # If seeking is not supported, continue without it
                pass
                
            file_content = await upload.read()
            
            # Validate that we actually got content
            if not file_content:
                raise ValueError("File appears to be empty or could not be read")
                
        except Exception as e:
            logger.error("Failed to read uploaded file %s: %s", upload.filename, e)
            raise ValueError(f"Could not read uploaded file: {e}") from e

        # 3 ▸ upload to S3 using unified structure
        from app.services.unified_storage_service import UnifiedStorageService
        
        unified_storage = UnifiedStorageService()
        
        result = unified_storage.upload_user_dataset(
            blob=file_content,
            user_id=user_id,
            dataset_id=dataset_id,
            file_extension=file_ext,
            content_type=upload.content_type
        )
        s3_key = result['key']
        logger.info("📁 Uploaded user file %s to S3: %s", upload.filename, s3_key)

        # 4 ▸ collect lightweight metadata (use separate BytesIO object)
        metadata_obj = io.BytesIO(file_content)
        columns, row_count = self._quick_scan(metadata_obj, file_fmt)

        # 5 ▸ register in catalog so Dataset-agents see it
        dataset_id = await self.catalog.register_dataset(
            user_id=user_id,
            original_source="user_upload",
            s3_key=s3_key,
            columns=columns,
            row_count=row_count,
            file_format=file_fmt,
        )

        dataset_meta = {
            "dataset_id": dataset_id,  # Use the returned dataset_id from catalog
            "user_id": user_id,
            "s3_key": s3_key,
            "columns": columns,
            "row_count": row_count,
            "file_format": file_fmt,
            "source": "user_upload",
        }

        return dataset_meta

    # ──────────────────────────────────────────────────────────────
    # helpers
    # ──────────────────────────────────────────────────────────────
    def _quick_scan(self, file_obj: io.BytesIO | io.BufferedReader, fmt: str) -> Tuple[list[str], int]:
        """Return a *very* cheap `(columns, row_count)` sample."""
        try:
            if fmt == "excel":
                df = pd.read_excel(file_obj, nrows=15000)  # cap rows for speed
            else:  # assume csv/tsv
                file_obj.seek(0)
                df = pd.read_csv(file_obj, nrows=15000)
            cols = df.columns.tolist()[:100]  # never more than 100 cols
            rows = len(df)
            return cols, rows
        except Exception as exc:
            logger.warning("Header extraction failed: %s", exc)
            # fallback: naive header read with fresh BytesIO to avoid wrapper issues
            fallback_obj = io.BytesIO(file_obj.getvalue())
            fallback_obj.seek(0)
            try:
                # Try to decode as text first
                content_str = fallback_obj.getvalue().decode('utf-8', errors='ignore')
                lines = content_str.split('\n')
                if lines:
                    # Parse first line as CSV header
                    reader = csv.reader([lines[0]])
                    cols = next(reader, [])[:100]
                    # Count non-empty lines
                    rows = len([line for line in lines if line.strip()])
                    return cols, rows
            except Exception:
                logger.warning("Fallback CSV parsing also failed")
            return [], 0
