"""
Orchestrator Agent

Central coordinator that:
1. understands the user's NL query,
2. asks DB-manager agents for schema or data,
3. calls the SQL agent,
4. formats the final answer with the Output agent.
"""

from __future__ import annotations

import asyncio
import json
import logging
# Initialize module-level logger before any use, including within import guards
logger = logging.getLogger(__name__)

from typing import Any, Dict, List, Optional, AsyncGenerator
from datetime import datetime

from app.agents.base import Agent, AgentResponse
from app.agents.database_query.database_manager_agent import DatabaseManagerAgent
from app.agents.database_query.output_agent import OutputAgent
from app.agents.database_query.sql_agent import SQLAgent
from app.agents.database_query.query_understanding_agent import QueryUnderstandingAgent
from app.agents.database_query.advanced_sql_agent import AdvancedSQLAgent
from app.agents.database_query.table_discussion_agent import TableDiscussionAgent
from app.utils.response_formatter import ResponseFormatter
from app.utils.error_utils import friendly_agent_errors
from app.services.intelligent_cache_service import intelligent_cache_service
from app.services.conversational_context_service import conversational_context_service
from app.utils.bedrock_client import BedrockClient
from app.utils.constants import FOLLOW_UP_INDICATORS
from app.prompts.orchestrator import SYSTEM_PROMPT, QUERY_ENHANCEMENT_SYSTEM_PROMPT

# Phase 3: Intelligent Data Analysis & Insights
try:
    from app.agents.database_query.data_insights_agent import DataInsightsAgent
    from app.agents.database_query.business_context_agent import BusinessContextAgent
    from app.agents.database_query.comparative_analysis_agent import ComparativeAnalysisAgent
    PHASE_3_AVAILABLE = True
except ImportError:
    PHASE_3_AVAILABLE = False
    logger.warning("Phase 3 agents not available - advanced analysis features disabled")
from app.models.database import Database
from app.services.database_service import DatabaseService


class OrchestratorAgent(Agent):
    """Top-level orchestrator."""
    
    SYSTEM_PROMPT = SYSTEM_PROMPT
    
    # ──────────────────────────────────────────────────────────────────────────
    # Init & bootstrap
    # ──────────────────────────────────────────────────────────────────────────
    def __init__(self, databases: List[Database], agent_id: str | None = None):
        self.agent_id = agent_id or "orchestrator_agent"
        self.databases = databases

        self.database_service = DatabaseService()
        self.bedrock_client = BedrockClient()

        self.db_agents: List[DatabaseManagerAgent] = []
        self.sql_agent = SQLAgent(self.database_service)
        self.advanced_sql_agent = AdvancedSQLAgent(self.database_service)
        self.output_agent = OutputAgent()
        self.query_understanding_agent = QueryUnderstandingAgent()
        self.table_discussion_agent = TableDiscussionAgent()

        # Performance optimization: Add caching layers
        self.query_cache: Dict[str, Dict[str, Any]] = {}
        self.schema_cache: Dict[str, Dict[str, Any]] = {}
        self.cache_ttl: Dict[str, float] = {}
        self.CACHE_DURATION = 300  # 5 minutes

        # Phase 3: Intelligent Data Analysis & Insights agents
        if PHASE_3_AVAILABLE:
            self.data_insights_agent = DataInsightsAgent()
            self.business_context_agent = BusinessContextAgent()
            self.comparative_analysis_agent = ComparativeAnalysisAgent()
            self.phase3_enabled = True
            logger.info("Phase 3 intelligent analysis agents initialized")
        else:
            self.phase3_enabled = False
            logger.info("Phase 3 agents not available - using basic analysis only")

        self.initialized: bool = False
        
    async def initialize(self) -> None:
        if self.initialized:
            return
            
        # DB-manager agents
        self.db_agents = [
            DatabaseManagerAgent(db, self.database_service) for db in self.databases
        ]
        logger.info(
            "Orchestrator initialised with %d DB-agents: %s",
            len(self.db_agents),
            [a.database.id for a in self.db_agents],
        )

        # child agents
        await self.sql_agent.initialize()
        await self.advanced_sql_agent.initialize()
        await self.output_agent.initialize()
        await self.query_understanding_agent.initialize()
        await self.table_discussion_agent.initialize()

        # Phase 3 agents initialization
        if self.phase3_enabled:
            await self.data_insights_agent.initialize()
            await self.business_context_agent.initialize()
            await self.comparative_analysis_agent.initialize()
            logger.info("Phase 3 agents initialized successfully")

        self.initialized = True

    # ──────────────────────────────────────────────────────────────────────────
    # External API
    # ──────────────────────────────────────────────────────────────────────────
    @friendly_agent_errors("orchestrating")
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """Process a user query through the entire agent system."""
        if not self.initialized:
            await self.initialize()
            
        # Extract message parameters
        query, output_format, conversation_hist, user_id, session_id, target_databases, target_tables, target_columns, tone_pref, format_pref = self._extract_message_params(message)
        
        if not query:
            return AgentResponse(
                self.agent_id, False, error="No query provided"
            ).to_dict()
            
        try:
            # Enhanced caching with intelligent cache service
            cache_context = {
                "target_databases": target_databases,
                "target_tables": target_tables,
                "target_columns": target_columns
            }

            # Check intelligent cache first
            cached_result = await intelligent_cache_service.get_query_result(query, cache_context)
            if cached_result is not None:
                logger.info(f"Returning intelligent cached result for query: '{query[:50]}...'")
                cached_result["metadata"]["cached"] = True
                cached_result["metadata"]["cache_type"] = "intelligent"
                return cached_result

            # Fallback to legacy cache
            cache_key = self._generate_cache_key(query, target_databases, target_tables, target_columns)
            if self._is_cache_valid(cache_key):
                logger.info(f"Returning legacy cached result for query: '{query[:50]}...'")
                cached_result = self.query_cache[cache_key]["data"]
                cached_result["metadata"]["cached"] = True
                cached_result["metadata"]["cache_type"] = "legacy"
                return cached_result

            # 1 ▸ Enhanced query understanding and analysis
            query_analysis = await self._analyze_query_with_understanding(
                query, conversation_hist, target_databases, target_tables, target_columns,
                message.get("session_id"), message.get("user_id")
            )
            enhanced_query = query_analysis["enhanced_query"]
            logger.info(f"Processing query: '{enhanced_query}' (Intent: {query_analysis.get('intent', 'unknown')})")

            # Check if this is a table discussion query
            is_table_discussion = query_analysis.get("analysis_metadata", {}).get("is_table_discussion", False)

            if is_table_discussion:
                # Route to table discussion agent
                table_discussion_response = await self._handle_table_discussion(
                    enhanced_query, query_analysis, target_databases, target_tables,
                    message.get("session_id"), message.get("user_id")
                )

                if table_discussion_response.get("has_relevant_info", False):
                    return table_discussion_response
                # If table discussion fails, continue with normal flow

            # 2 ▸ Get database info from database agents
            relevant_db_infos = await self._get_relevant_database_info(
                enhanced_query, target_databases, target_tables, target_columns
            )

            # 3 ▸ Hand off to SQL agent (enhanced for complex queries)
            sql_resp = await self._generate_sql_queries_enhanced(
                enhanced_query, query_analysis, relevant_db_infos, output_format, user_id, session_id
            )

            # 4 ▸ Phase 3: Intelligent Data Analysis & Insights (if enabled)
            enhanced_sql_resp = await self._apply_phase3_analysis(
                enhanced_query, query_analysis, sql_resp, output_format
            )

            # 5 ▸ Format final output using simple markdown generation
            simple_markdown = await self._format_simple_output(enhanced_query, enhanced_sql_resp, session_id)

            # Create simple response structure
            final_resp = AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={"summary": simple_markdown, "message": simple_markdown}
            ).to_dict()

            # 6 ▸ Update conversational context with query results
            if session_id:
                await self._update_conversational_context(
                    session_id, query, enhanced_query, enhanced_sql_resp,
                    query_analysis, simple_markdown
                )

            # 7 ▸ Prepare and return response
            resp_obj = AgentResponse.from_dict(final_resp)
            resp_obj.metadata.setdefault("original_query", query)
            resp_obj.metadata.setdefault("enhanced_query", enhanced_query)
            resp_obj.metadata.setdefault("query_analysis", {
                "intent": query_analysis.get("intent", "unknown"),
                "complexity": query_analysis.get("complexity", "simple"),
                "confidence": query_analysis.get("confidence", 0.0),
                "entities": query_analysis.get("entities", []),
                "clarifications": query_analysis.get("clarifications", [])
            })

            # ── Phase 2: clarification loop ────────────────────────────
            if query_analysis.get("clarifications") or query_analysis.get("confidence", 1.0) < 0.6:
                clar_qs = query_analysis.get("clarifications", [])
                clar_text = ResponseFormatter.ask_clarification(clar_qs)
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    data={"clarification_required": True, "questions": clar_qs, "message": clar_text}
                ).to_dict()

            # Cache successful results using intelligent cache service
            if resp_obj.has_relevant_info:
                # Cache in intelligent cache service
                intelligent_cache_service.cache_query_result(
                    query, cache_context, resp_obj.to_dict(), ttl=600
                )
                # Also cache in legacy cache for backward compatibility
                self._cache_result(cache_key, resp_obj.to_dict())

            return resp_obj.to_dict()

        except Exception as exc:
            logger.exception("Orchestrator error")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Error processing query: {exc}"
            ).to_dict()

    @friendly_agent_errors("orchestrating-stream")
    async def process_stream(self, message: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Process a user query with streaming support for the final answer generation."""
        if not self.initialized:
            await self.initialize()

        def create_event(event_type: str, agent: str, data: Any) -> Dict[str, Any]:
            return {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }

        # Extract message parameters
        query, output_format, conversation_hist, user_id, session_id, target_databases, target_tables, target_columns, tone_pref, format_pref = self._extract_message_params(message)
        
        if not query:
            yield create_event("error", self.agent_id, {"message": "No query provided", "status": "failed"})
            return

        try:
            # 1 ▸ Enhanced query understanding and analysis
            yield create_event("agent_status", self.agent_id, {"message": "🧠 Understanding your question...", "status": "processing"})
            query_analysis = await self._analyze_query_with_understanding(
                query, conversation_hist, target_databases, target_tables, target_columns,
                message.get("session_id"), message.get("user_id")
            )
            enhanced_query = query_analysis["enhanced_query"]

            # Provide feedback on query understanding
            intent = query_analysis.get("intent", "unknown")
            confidence = query_analysis.get("confidence", 0.0)
            yield create_event("query_analysis", self.agent_id, {
                "message": f"🎯 Understood as {intent} query (confidence: {confidence:.1%})",
                "intent": intent,
                "confidence": confidence,
                "enhanced_query": enhanced_query
            })

            logger.info(f"Processing query: '{enhanced_query}' (Intent: {intent}, Confidence: {confidence:.1%})")

            # ── Phase 2: clarification loop (stream early) ─────────────────
            if query_analysis.get("clarifications") or query_analysis.get("confidence", 1.0) < 0.6:
                clar_qs = query_analysis.get("clarifications", [])
                clar_text = ResponseFormatter.ask_clarification(clar_qs)
                yield create_event("clarification_request", self.agent_id, {
                    "message": clar_text,
                    "questions": clar_qs,
                    "clarification_required": True
                })
                return

            # 2 ▸ Get database info from database agents
            yield create_event("agent_status", self.agent_id, {"message": "📊 Searching relevant databases...", "status": "processing"})
            relevant_db_infos = await self._get_relevant_database_info(
                enhanced_query, target_databases, target_tables, target_columns
            )

            # 3 ▸ Hand off to SQL agent (enhanced for complex queries)
            yield create_event("agent_status", self.agent_id, {"message": "🔧 Generating advanced SQL queries...", "status": "processing"})
            sql_resp = await self._generate_sql_queries_enhanced(
                enhanced_query, query_analysis, relevant_db_infos, output_format, user_id, session_id
            )

            # 4 ▸ Format final output with streaming
            yield create_event("agent_status", self.agent_id, {"message": "✨ Generating your answer...", "status": "processing"})

            # Check if output agent supports streaming
            if hasattr(self.output_agent, 'process_stream'):
                # Generate simple markdown response for streaming
                simple_markdown = await self._format_simple_output(enhanced_query, sql_resp, session_id)

                # Stream the markdown response word by word
                words = simple_markdown.split()
                accumulated_text = ""

                for i, word in enumerate(words):
                    # Add space after word except for the last one
                    token_to_send = word + (" " if i < len(words) - 1 else "")
                    accumulated_text += token_to_send

                    # Send token stream event
                    yield create_event("token_stream", self.agent_id, {"token": token_to_send})

                    # Add realistic delay between tokens
                    await asyncio.sleep(0.03)  # 30ms delay for realistic streaming

                # Send completion event
                yield create_event("token_complete", self.agent_id, {
                    "complete_response": simple_markdown,
                    "conversational_answer": simple_markdown
                })

                # Update conversational context with query results
                if session_id:
                    await self._update_conversational_context(
                        session_id, query, enhanced_query, sql_resp,
                        query_analysis, simple_markdown
                    )

                # Send final conversational response with clean message
                final_response = {
                    "query": enhanced_query,
                    "answer": simple_markdown,
                    "session_id": message.get("session_id"),
                }
                yield create_event("conversation_complete", self.agent_id, {
                    "message": simple_markdown,  # Clean message for frontend display
                    "response": final_response,
                    "timestamp": datetime.utcnow().isoformat() + "Z"
                })
            else:
                # Fallback to non-streaming output using simple formatting
                simple_markdown = await self._format_simple_output(enhanced_query, sql_resp, session_id)

                # Create simple response structure
                final_resp = AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=True,
                    data={"summary": simple_markdown, "message": simple_markdown}
                ).to_dict()

                # Send final result
                resp_obj = AgentResponse.from_dict(final_resp)
                resp_obj.metadata.setdefault("original_query", query)
                resp_obj.metadata.setdefault("enhanced_query", enhanced_query)

                yield create_event("agent_result", self.agent_id, resp_obj.to_dict())

        except Exception as exc:
            logger.exception("Orchestrator streaming error")
            yield create_event("error", self.agent_id, {"message": f"❌ Error processing query: {exc}", "status": "failed"})
    
    def _extract_message_params(self, message: Dict[str, Any]) -> tuple:
        """Extract and normalize parameters from the incoming message."""
        query = message.get("query", "")
        output_format = message.get("output_format", "csv")
        conversation_hist = message.get("conversation_history", [])
        user_id = message.get("user_id", "")
        session_id = message.get("session_id", "")
        tone_pref = message.get("tone", "conversational")
        format_pref = message.get("format", "chat")

        # Normalize targeting inputs so we never get None
        target_databases = (message.get("target_databases") or [])
        target_tables = (message.get("target_tables") or {})    # db_id -> [tables]
        target_columns = (message.get("target_columns") or {})  # db_id -> {table:[cols]}
        
        return query, output_format, conversation_hist, user_id, session_id, target_databases, target_tables, target_columns, tone_pref, format_pref

    async def _handle_table_discussion(
        self,
        query: str,
        query_analysis: Dict[str, Any],
        target_databases: List[str],
        target_tables: Dict[str, List[str]],
        session_id: Optional[str],
        user_id: Optional[str]
    ) -> Dict[str, Any]:
        """Handle table discussion queries using the table discussion agent."""

        try:
            # Determine which database to use
            database_id = None
            if target_databases:
                database_id = target_databases[0]
            elif self.databases:
                database_id = self.databases[0].id

            if not database_id:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    error="No database available for table discussion"
                ).to_dict()

            # Extract table names from target_tables
            table_names = []
            if target_tables and database_id in target_tables:
                table_names = target_tables[database_id]

            # Prepare message for table discussion agent
            table_discussion_message = {
                "query": query,
                "database_id": database_id,
                "table_names": table_names,
                "session_id": session_id,
                "user_id": user_id,
                "exploration_depth": self._determine_exploration_depth(query_analysis)
            }

            # Process with table discussion agent
            response = await self.table_discussion_agent.process(table_discussion_message)

            # Convert to orchestrator response format
            if response.get("has_relevant_info", False):
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=True,
                    data=response.get("data", {}),
                    metadata={
                        "query_type": "table_discussion",
                        "database_id": database_id,
                        "tables_explored": response.get("data", {}).get("tables_explored", [])
                    }
                ).to_dict()
            else:
                return response

        except Exception as e:
            logger.error(f"Table discussion handling failed: {e}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Table discussion failed: {str(e)}"
            ).to_dict()

    def _determine_exploration_depth(self, query_analysis: Dict[str, Any]) -> str:
        """Determine the appropriate exploration depth based on query analysis."""

        intent = query_analysis.get("intent", "explore")
        complexity = query_analysis.get("complexity", "simple")

        # Map intent and complexity to exploration depth
        if complexity == "very_complex" or intent in ["analytical", "comparative"]:
            return "analytical"
        elif complexity == "complex" or intent in ["exploratory", "reporting"]:
            return "comprehensive"
        elif intent in ["metadata", "structure"]:
            return "detailed"
        else:
            return "basic"
    
    async def _get_relevant_database_info(
        self,
        query: str,
        target_databases: List[str],
        target_tables: Dict[str, List[str]],
        target_columns: Dict[str, Dict[str, List[str]]]
    ) -> List[Dict[str, Any]]:
        """Get relevant database information from database agents with optimized parallel processing."""
        # Select which database agents to query
        agents_to_query = self._select_db_agents(target_databases)

        # Optimize: Skip if no agents to query
        if not agents_to_query:
            logger.warning("No database agents available for query")
            return []

        # Prepare database agent tasks with timeout and error handling
        db_tasks = await self._prepare_db_agent_tasks_optimized(
            agents_to_query, query, target_tables, target_columns
        )

        # Execute all tasks in parallel with timeout and error handling
        start_time = asyncio.get_event_loop().time()
        try:
            # Increased timeout to accommodate complex queries and potential optimization
            db_results = await asyncio.wait_for(
                asyncio.gather(*db_tasks, return_exceptions=True),
                timeout=240.0  # 4 minute timeout for database operations (increased from 30 seconds)
            )
        except asyncio.TimeoutError:
            logger.error("Database agent queries timed out after 4 minutes")
            # Return partial results if any agents completed before timeout
            return []

        execution_time = asyncio.get_event_loop().time() - start_time
        logger.info("Received %d DB-agent responses in %.2f seconds", len(db_results), execution_time)

        # Debug: Log the results from each agent
        for i, result in enumerate(db_results):
            if isinstance(result, Exception):
                logger.error(f"DB-agent {i} failed with exception: {result}")
            elif isinstance(result, dict):
                has_info = result.get("has_relevant_info", False)
                error = result.get("error")
                agent_id = result.get("agent_id", f"agent_{i}")
                database_name = result.get("database_name", "Unknown")
                if error:
                    logger.warning(f"DB-agent {agent_id} ({database_name}) returned error: {error}")
                else:
                    table_count = len(result.get("data", {}).get("tables", []))
                    logger.info(f"DB-agent {agent_id} ({database_name}) has_relevant_info: {has_info}, tables: {table_count}")
            else:
                logger.warning(f"DB-agent {i} returned unexpected result type: {type(result)}")

        # Filter out exceptions and extract relevant database info
        valid_results = [r for r in db_results if not isinstance(r, Exception)]
        if len(valid_results) < len(db_results):
            logger.warning(f"{len(db_results) - len(valid_results)} database agent queries failed")

        relevant_db_infos = self._extract_relevant_db_info(valid_results)

        # ────────────────────────────────────────────────────────────
        # If more than one database looks relevant, pick the *most* relevant
        # instead of returning all – this prevents duplicate answers.
        # Relevance heuristic: larger `table_count` (from metadata) or, as a
        # fallback, the number of tables returned.
        # ────────────────────────────────────────────────────────────
        if len(relevant_db_infos) > 1:
            ranked: List[tuple[int, Dict[str, Any]]] = []
            for res, original in zip(db_results, relevant_db_infos):
                resp_obj = AgentResponse.from_dict(res)
                score = resp_obj.metadata.get("table_count", len(original.get("tables", [])))
                ranked.append((score, original))

            # Sort descending by score and keep the best one
            ranked.sort(key=lambda t: t[0], reverse=True)
            relevant_db_infos = [ranked[0][1]]

        # Fallback if targeting missed everything
        if not relevant_db_infos and (target_databases or target_tables or target_columns):
            relevant_db_infos = await self._fallback_database_search(
                agents_to_query, query
            )

        return relevant_db_infos
    
    def _select_db_agents(self, target_databases: List[str]) -> List[DatabaseManagerAgent]:
        """Select which database agents to query based on targeting."""
        if target_databases:
            return [a for a in self.db_agents if a.database.id in target_databases]
        return self.db_agents
    
    async def _prepare_db_agent_tasks(
        self,
        agents: List[DatabaseManagerAgent],
        query: str,
        target_tables: Dict[str, List[str]],
        target_columns: Dict[str, Dict[str, List[str]]]
    ) -> List:
        """Deprecated wrapper kept for backward-compatibility.
        The original implementation duplicated logic now available in
        `_prepare_db_agent_tasks_optimized`. Delegating ensures a single,
        well-maintained code path and avoids redundancy.
        """
        return await self._prepare_db_agent_tasks_optimized(
            agents, query, target_tables, target_columns
        )

    async def _prepare_db_agent_tasks_optimized(
        self,
        agents: List[DatabaseManagerAgent],
        query: str,
        target_tables: Dict[str, List[str]],
        target_columns: Dict[str, Dict[str, List[str]]]
    ) -> List:
        """Prepare tasks for database agents with optimized initialization."""
        # Initialize all agents in parallel first
        initialization_tasks = []
        for agent in agents:
            if not getattr(agent, "initialized", False):
                initialization_tasks.append(agent.initialize())

        if initialization_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*initialization_tasks, return_exceptions=True),
                    timeout=60.0  # Increased to 60 seconds for initialization
                )
            except asyncio.TimeoutError:
                logger.error("Database agent initialization timed out after 60 seconds")

        # Prepare processing tasks
        db_tasks = []
        for agent in agents:
            agent_msg = {"query": query}

            if target_tables and agent.database.id in target_tables:
                agent_msg["target_tables"] = target_tables[agent.database.id]

            if target_columns and agent.database.id in target_columns:
                agent_msg["target_columns"] = target_columns[agent.database.id]

            # Wrap each task with timeout and error handling
            db_tasks.append(self._safe_agent_process(agent, agent_msg))

        return db_tasks

    async def _safe_agent_process(self, agent: DatabaseManagerAgent, message: Dict[str, Any]) -> Dict[str, Any]:
        """Safely process agent with timeout and error handling."""
        try:
            # Increased timeout for complex database operations to accommodate query optimization
            return await asyncio.wait_for(agent.process(message), timeout=300.0)  # 5 minutes per agent
        except asyncio.TimeoutError:
            logger.error(f"Agent {agent.agent_id} timed out after 5 minutes")
            return {
                "has_relevant_info": False,
                "error": "Agent timeout - database operation took too long. Please try a simpler query or add more specific filters.",
                "agent_id": agent.agent_id,
                "database_name": getattr(agent.database, 'name', 'Unknown'),
                "error_type": "timeout"
            }
        except Exception as e:
            logger.error(f"Agent {agent.agent_id} failed: {str(e)}")
            return {
                "has_relevant_info": False,
                "error": str(e),
                "agent_id": agent.agent_id,
                "database_name": getattr(agent.database, 'name', 'Unknown')
            }
    
    def _extract_relevant_db_info(self, db_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract relevant database info from agent responses."""
        return [
            AgentResponse.from_dict(r).data
            for r in db_results
            if AgentResponse.from_dict(r).has_relevant_info
                and "tables" in AgentResponse.from_dict(r).data
                and AgentResponse.from_dict(r).data["tables"]
        ]
    
    async def _fallback_database_search(
        self, 
        previous_agents: List[DatabaseManagerAgent], 
        query: str
    ) -> List[Dict[str, Any]]:
        """Fallback search using remaining databases if targeted search found nothing."""
        logger.info("Targeting returned nothing – trying remaining databases")
        leftover_agents = [a for a in self.db_agents if a not in previous_agents]
        
        if not leftover_agents:
            return []
            
        fallback_tasks = [a.process({"query": query}) for a in leftover_agents]
        fallback_results = await asyncio.gather(*fallback_tasks)
        
        relevant_db_infos = []
        for r in fallback_results:
            resp = AgentResponse.from_dict(r)
            if resp.has_relevant_info and "tables" in resp.data and resp.data["tables"]:
                relevant_db_infos.append(resp.data)
                
        return relevant_db_infos
    
    async def _generate_sql_queries(
        self, 
        query: str, 
        database_infos: List[Dict[str, Any]], 
        output_format: str,
        user_id: str,
        session_id: str
    ) -> Dict[str, Any]:
        """Generate SQL queries using the SQL agent."""
        sql_payload = {
            "query": query,
            "database_infos": database_infos,
            "output_format": output_format,
            "user_id": user_id,
            "session_id": session_id,
        }
        return await self.sql_agent.process(sql_payload)

    async def _generate_sql_queries_enhanced(
        self,
        query: str,
        query_analysis: Dict[str, Any],
        database_infos: List[Dict[str, Any]],
        output_format: str,
        user_id: str,
        session_id: str
    ) -> Dict[str, Any]:
        """Generate SQL queries using enhanced agent for complex queries."""

        # Determine if we should use advanced SQL generation
        complexity = query_analysis.get("complexity", "simple")
        intent = query_analysis.get("intent", "operational")
        confidence = query_analysis.get("confidence", 1.0)

        # Use advanced agent for complex queries or analytical intents
        use_advanced = (
            complexity in ["complex", "very_complex"] or
            intent in ["analytical", "comparative", "reporting"]
        )

        if use_advanced and database_infos:
            logger.info(f"Using advanced SQL generation for {complexity} {intent} query")

            # Prepare message for advanced SQL agent
            advanced_payload = {
                "query": query,
                "query_analysis": query_analysis,
                "database_infos": database_infos,
                "output_format": output_format,
                "user_id": user_id,
                "session_id": session_id,
                "generation_mode": self._determine_generation_mode(complexity, intent),
                "validation_level": self._determine_validation_level(complexity, intent)
            }

            try:
                advanced_response = await self.advanced_sql_agent.process(advanced_payload)
                advanced_data = AgentResponse.from_dict(advanced_response)

                if advanced_data.has_relevant_info:
                    # Convert advanced response to standard SQL agent format
                    return self._convert_advanced_response_to_standard(advanced_data.data, query)
                else:
                    logger.warning("Advanced SQL agent failed, falling back to standard agent")

            except Exception as e:
                logger.error(f"Advanced SQL generation failed: {e}, falling back to standard agent")

        # Fallback to standard SQL agent
        logger.info("Using standard SQL generation")
        sql_payload = {
            "query": query,
            "database_infos": database_infos,
            "output_format": output_format,
            "user_id": user_id,
            "session_id": session_id,
        }
        return await self.sql_agent.process(sql_payload)

    def _determine_generation_mode(self, complexity: str, intent: str) -> str:
        """Determine the generation mode based on query characteristics."""
        if complexity in ["complex", "very_complex"]:
            return "optimized"
        elif intent in ["analytical", "comparative", "reporting"]:
            return "optimized"
        else:
            return "standard"

    def _determine_validation_level(self, complexity: str, intent: str) -> str:
        """Determine the validation level based on query characteristics."""
        if complexity == "very_complex":
            return "strict"
        elif complexity == "complex" or intent in ["analytical", "comparative", "reporting"]:
            return "comprehensive"
        else:
            return "basic"

    def _convert_advanced_response_to_standard(self, advanced_data: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Convert advanced SQL agent response to standard SQL agent format."""
        database_results = advanced_data.get("database_results", [])

        # Convert to standard format
        standard_results = []
        for db_result in database_results:
            if db_result.get("success", False) and db_result.get("final_sql"):
                standard_result = {
                    "database_id": db_result.get("database_id"),
                    "database_name": db_result.get("database_name"),
                    "database_type": db_result.get("database_type"),
                    "query": db_result.get("final_sql"),
                    "data": None,  # Will be populated during execution
                    "metadata": {
                        "generation_mode": "advanced",
                        "validation_results": db_result.get("validation_results", []),
                        "performance_metrics": db_result.get("performance_metrics", {}),
                        "iterations": db_result.get("iterations", 0)
                    }
                }
                standard_results.append(standard_result)

        return {
            "results": standard_results,
            "query": query,
            "metadata": {
                "advanced_generation": True,
                "summary": advanced_data.get("summary", {}),
                "total_databases": advanced_data.get("metadata", {}).get("total_databases", 0),
                "successful_generations": advanced_data.get("metadata", {}).get("successful_generations", 0)
            }
        }
    
    async def _format_output(
        self,
        query: str,
        sql_agent_response: Dict[str, Any],
        output_format: str,
        tone_pref: str,
        format_pref: str,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Format the final output using the output agent."""
        return await self.output_agent.process({
            "query": query,
            "sql_agent_response": sql_agent_response,
            "output_format": output_format,
            "tone": tone_pref,
            "format": format_pref,
            "session_id": session_id,
            "user_id": user_id
        })

    async def _format_simple_output(
        self,
        query: str,
        sql_agent_response: Dict[str, Any],
        session_id: Optional[str] = None
    ) -> str:
        """Format output using simple markdown generation for easy frontend rendering."""
        try:
            # Extract successful results and errors from SQL agent response
            sql_data = sql_agent_response.get("data", {})
            results = sql_data.get("results", [])
            errors = sql_data.get("errors", [])

            # Separate successful results from errors
            successful_results = []
            error_results = []

            for result in results:
                if result.get("error"):
                    error_results.append(result)
                else:
                    successful_results.append(result)

            # Add any additional errors
            error_results.extend(errors)

            # Generate simple markdown response with session context
            markdown = await self.output_agent.generate_simple_markdown_response(
                query=query,
                successful_results=successful_results,
                errors=error_results,
                session_id=session_id
            )

            return markdown

        except Exception as e:
            logger.error(f"Error formatting simple output: {str(e)}")
            return f"I found some data related to your query about '{query}', but encountered an error while formatting the response. Please try again."
    
    # ────────────────────────────────────────────────────────────────────────
    # helper: talk to DB-manager agents
    # ────────────────────────────────────────────────────────────────────────
    async def _fan_out_to_db_agents(
        self,
        query: str,
        target_dbs: List[str],
        target_tables: Dict[str, List[str]],
        target_cols: Dict[str, Dict[str, List[str]]],
    ) -> List[Dict[str, Any]]:
        """Distribute a query to multiple database agents."""
        # Select the database agents to use
        agents = self._select_db_agents(target_dbs)

        # Prepare tasks
        tasks = []
        for ag in agents:
            msg = {"query": query}
            if ag.database.id in target_tables:
                msg["target_tables"] = target_tables[ag.database.id]
            if ag.database.id in target_cols:
                msg["target_columns"] = target_cols[ag.database.id]
            tasks.append(ag.process(msg))

        # Execute all tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=False)

        # Extract relevant database info
        infos = []
        for res in results:
            resp = AgentResponse.from_dict(res)
            if resp.has_relevant_info and resp.data.get("tables"):
                infos.append(resp.data)

        return infos

    # ────────────────────────────────────────────────────────────────────────
    # helper: execute SQL directly via DatabaseService
    # ────────────────────────────────────────────────────────────────────────
    async def _execute_sql_query(
        self, 
        sql_query: str, 
        output_format: str,
        target_databases: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Run SQL on one database (first match)."""
        # Select the database agent to use
        agent = self._select_agent_for_sql_execution(target_databases)

        try:
            # Execute the query
            df_or_docs = await self.database_service.execute_query(
                agent.database.id, sql_query
            )
            
            # Format the result
            formatted = self.sql_agent._format_result(df_or_docs, output_format)
            
            return self._create_successful_sql_response(
                agent.database.id, agent.database.name, sql_query, formatted
            )
            
        except Exception as exc:
            logger.error("SQL execution failed: %s", exc)
            return self._create_error_sql_response(
                agent.database.id, agent.database.name, sql_query, str(exc)
            )
    
    def _select_agent_for_sql_execution(self, target_databases: Optional[List[str]]) -> DatabaseManagerAgent:
        """Select a database agent for SQL execution."""
        if target_databases:
            return next((a for a in self.db_agents if a.database.id in target_databases), self.db_agents[0])
        return self.db_agents[0]
    
    def _create_successful_sql_response(self, db_id: str, db_name: str, sql_query: str, formatted_data: Any) -> Dict[str, Any]:
        """Create a response for successful SQL execution."""
        return {
            "results": [
                {
                    "database_id": db_id,
                    "database_name": db_name,
                    "query": sql_query,
                    "data": formatted_data,
                }
            ],
            "query": sql_query,
        }
    
    def _create_error_sql_response(self, db_id: str, db_name: str, sql_query: str, error: str) -> Dict[str, Any]:
        """Create a response for failed SQL execution."""
        return {
            "error": error,
            "results": [
                {
                    "database_id": db_id,
                    "database_name": db_name,
                    "query": sql_query,
                    "error": error,
                }
            ],
        }

    # ────────────────────────────────────────────────────────────────────────
    # Enhanced Query Understanding
    # ────────────────────────────────────────────────────────────────────────
    async def _analyze_query_with_understanding(
        self,
        query: str,
        conversation_history: List[Dict[str, Any]],
        target_databases: Optional[List[str]] = None,
        target_tables: Optional[Dict[str, List[str]]] = None,
        target_columns: Optional[Dict[str, Dict[str, List[str]]]] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Analyze query using the enhanced understanding agent."""

        # Prepare database schema context for better understanding
        database_schemas = []
        try:
            # Get schema information from available databases
            for db_agent in self.db_agents[:3]:  # Limit to avoid overwhelming the context
                schema_info = {
                    "database_id": db_agent.database.id,
                    "database_name": db_agent.database.name,
                    "database_type": db_agent.database.db_type.value,
                    "tables": []
                }

                # Get basic table information if available
                if hasattr(db_agent, 'sub_agents') and db_agent.sub_agents:
                    for sub_agent in db_agent.sub_agents[:5]:  # First 5 sub-agents
                        if hasattr(sub_agent, 'tables'):
                            for table in sub_agent.tables[:3]:  # First 3 tables per sub-agent
                                schema_info["tables"].append({
                                    "name": table.name,
                                    "columns": [col.name for col in table.columns[:5]]  # First 5 columns
                                })

                database_schemas.append(schema_info)
        except Exception as e:
            logger.warning(f"Could not gather schema context: {e}")

        # Get conversational context if session_id is available
        conversational_context = {}
        if session_id:
            try:
                context_data = await conversational_context_service.extract_context_for_query(session_id, query)
                references = await conversational_context_service.identify_references_in_query(session_id, query)
                conversational_context = {**context_data, "references": references}
            except Exception as e:
                logger.warning(f"Failed to get conversational context: {e}")

        # Prepare message for query understanding agent
        understanding_message = {
            "query": query,
            "conversation_history": conversation_history,
            "database_schemas": database_schemas,
            "conversational_context": conversational_context,
            "user_context": {
                "target_databases": target_databases,
                "target_tables": target_tables,
                "target_columns": target_columns
            },
            "session_id": session_id,
            "user_id": user_id
        }

        # Get enhanced understanding
        try:
            understanding_response = await self.query_understanding_agent.process(understanding_message)
            understanding_data = AgentResponse.from_dict(understanding_response)

            if understanding_data.has_relevant_info:
                return understanding_data.data
            else:
                logger.warning("Query understanding agent returned no relevant info")
                return self._fallback_query_analysis(query, conversation_history)

        except Exception as e:
            logger.error(f"Query understanding failed: {e}")
            return self._fallback_query_analysis(query, conversation_history)

    def _fallback_query_analysis(self, query: str, conversation_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Fallback query analysis when enhanced understanding fails."""
        # Use the original enhancement logic as fallback
        enhanced_query = query
        if conversation_history:
            try:
                # Simple enhancement using the original method
                enhanced_query = asyncio.create_task(
                    self._enhance_query_with_context(query, conversation_history)
                ).result() if asyncio.get_event_loop().is_running() else query
            except Exception:
                enhanced_query = query

        return {
            "enhanced_query": enhanced_query,
            "intent": "unclear",
            "complexity": "simple",
            "entities": [],
            "requirements": [],
            "clarifications": [],
            "confidence": 0.5,
            "analysis_metadata": {
                "fallback_used": True,
                "original_query": query
            }
        }

    # ────────────────────────────────────────────────────────────────────────
    # Follow-up question handling (Legacy - kept for compatibility)
    # ────────────────────────────────────────────────────────────────────────
    async def _enhance_query_with_context(
        self, 
        query: str, 
        conversation_history: List[Dict[str, Any]]
    ) -> str:
        """Enhance a query with context from previous conversation."""
        if not conversation_history:
            return query
            
        # Check if this might be a follow-up question
        is_follow_up = await self._is_follow_up_question(query, conversation_history)
        
        if not is_follow_up:
            return query
            
        # Format conversation history for the prompt
        history_text = self._format_conversation_history(conversation_history)
            
        # Create the user prompt
        user_prompt = self._create_query_enhancement_prompt(query, history_text)
        
        # Call Bedrock to enhance the query
        enhanced_query = await self._get_enhanced_query(user_prompt)
        
        # Return the enhanced query, or the original if enhancement failed
        return enhanced_query if enhanced_query and len(enhanced_query) >= 5 else query
    
    def _format_conversation_history(self, conversation_history: List[Dict[str, Any]]) -> str:
        """Format the conversation history for the prompt."""
        history_text = ""
        for i, msg in enumerate(conversation_history[-5:]):  # Last 5 messages
            role = msg.get("role", "user" if i % 2 == 0 else "assistant")
            content = msg.get("content", "")
            history_text += f"{role}: {content}\n\n"
        return history_text
    
    def _create_query_enhancement_prompt(self, query: str, history_text: str) -> str:
        """Create the prompt for query enhancement."""
        return f"""Conversation history:
{history_text}

Current user question: {query}

If this is a follow-up question that relies on previous context, rewrite it as a complete, standalone question.
Otherwise, return the original question unchanged."""
    
    async def _get_enhanced_query(self, user_prompt: str) -> str:
        """Get an enhanced query from Bedrock."""
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=QUERY_ENHANCEMENT_SYSTEM_PROMPT,
            temperature=0.3
        )
        return response.strip()
        
    async def _is_follow_up_question(
        self, 
        query: str, 
        conversation_history: List[Dict[str, Any]]
    ) -> bool:
        """Determine if a query is a follow-up question."""
        query_lower = query.lower()
        
        # Check for pronouns and other indicators
        for indicator in FOLLOW_UP_INDICATORS:
            if indicator in query_lower.split():
                return True
                
        # Check for very short queries (likely follow-ups)
        if len(query_lower.split()) <= 3:
            return True
            
        return False

    async def _update_conversational_context(
        self,
        session_id: str,
        original_query: str,
        enhanced_query: str,
        sql_response: Dict[str, Any],
        query_analysis: Dict[str, Any],
        result_summary: str
    ) -> None:
        """Update conversational context with query results for future reference."""
        try:
            # Extract relevant information from SQL response
            sql_data = AgentResponse.from_dict(sql_response)
            tables_involved = []
            key_findings = []
            result_data = {}

            if sql_data.has_relevant_info and sql_data.data.get("results"):
                results = sql_data.data["results"]

                # Extract table names
                for result in results:
                    if result.get("database_name"):
                        tables_involved.append(result["database_name"])

                # Extract key findings from the result summary
                if "top" in original_query.lower():
                    key_findings.append("top_results_query")
                if "most" in original_query.lower():
                    key_findings.append("highest_values_query")
                if "compare" in original_query.lower():
                    key_findings.append("comparative_analysis")

                # Store simplified result data for context
                if results and len(results) > 0:
                    first_result = results[0]
                    if hasattr(first_result.get("data"), "head"):
                        # DataFrame - get first few rows
                        result_data = {
                            "sample_rows": first_result["data"].head(5).to_dict("records"),
                            "total_rows": len(first_result["data"]),
                            "columns": list(first_result["data"].columns)
                        }

            # Add to conversational context
            await conversational_context_service.add_query_context(
                session_id=session_id,
                query_text=enhanced_query,
                sql_generated=sql_data.data.get("sql", "") if sql_data.has_relevant_info else "",
                result_summary=result_summary,
                tables_involved=tables_involved,
                key_findings=key_findings,
                result_data=result_data,
                query_intent=query_analysis.get("intent"),
                entities_mentioned=query_analysis.get("entities", [])
            )

        except Exception as e:
            logger.warning(f"Failed to update conversational context: {e}")

    # ────────────────────────────────────────────────────────────────────────
    # Phase 3: Intelligent Data Analysis & Insights Integration
    # ────────────────────────────────────────────────────────────────────────
    async def _apply_phase3_analysis(
        self,
        query: str,
        query_analysis: Dict[str, Any],
        sql_response: Dict[str, Any],
        output_format: str
    ) -> Dict[str, Any]:
        """Apply Phase 3 intelligent analysis to SQL results."""
        if not self.phase3_enabled:
            logger.debug("Phase 3 analysis disabled, returning original SQL response")
            return sql_response

        try:
            # Check if we have valid SQL results to analyze
            sql_data = AgentResponse.from_dict(sql_response)
            if not sql_data.has_relevant_info or not sql_data.data.get("results"):
                logger.debug("No valid SQL results for Phase 3 analysis")
                return sql_response

            # Extract query results for analysis
            query_results = self._extract_query_results_for_analysis(sql_data.data)
            if not query_results:
                logger.debug("No analyzable data found in SQL results")
                return sql_response

            # Determine if analysis should be applied based on query characteristics
            should_analyze = self._should_apply_phase3_analysis(query_analysis, query_results)
            if not should_analyze:
                logger.debug("Query does not require Phase 3 analysis")
                return sql_response

            logger.info("Applying Phase 3 intelligent analysis to query results")

            # Step 1: Data Insights Analysis
            insights_response = await self.data_insights_agent.process({
                "query_results": query_results,
                "query_context": query_analysis,
                "analysis_preferences": {},
                "enable_advanced_analysis": True
            })

            insights_data = AgentResponse.from_dict(insights_response)
            if not insights_data.has_relevant_info:
                logger.warning("Data insights analysis failed")
                return sql_response

            # Step 2 & 3: Business Context and Comparative Analysis ── run in parallel where possible
            business_task = self.business_context_agent.process({
                "statistical_insights": insights_data.data.get("insights", []),
                "query_context": query_analysis,
                "business_domain": self._infer_business_domain(query_analysis),
                "column_metadata": {}
            })

            comparative_task = None
            if self._should_apply_comparative_analysis(query_analysis):
                comparative_task = self.comparative_analysis_agent.process({
                    "current_data": query_results,
                    "comparison_type": "period_over_period",
                    "time_column": self._detect_time_column(query_results),
                    "metric_columns": self._detect_metric_columns(query_results)
                })

            # Gather tasks concurrently (comparative may be None)
            if comparative_task:
                business_response, comparative_response = await asyncio.gather(business_task, comparative_task)
            else:
                business_response = await business_task
                comparative_response = None

            business_data = AgentResponse.from_dict(business_response)
            comparative_data = None
            if comparative_response is not None:
                comparative_result = AgentResponse.from_dict(comparative_response)
                if comparative_result.has_relevant_info:
                    comparative_data = comparative_result.data

            # Enhance SQL response with Phase 3 analysis
            enhanced_response = self._enhance_sql_response_with_phase3(
                sql_response, insights_data.data, business_data.data if business_data.has_relevant_info else {},
                comparative_data
            )

            logger.info("Phase 3 analysis completed successfully")
            return enhanced_response

        except Exception as e:
            logger.error(f"Phase 3 analysis failed: {e}")
            # Return original response if analysis fails
            return sql_response

    def _extract_query_results_for_analysis(self, sql_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract query results in format suitable for Phase 3 analysis."""
        try:
            results = sql_data.get("results", [])
            if not results:
                return []

            # Convert to Phase 3 expected format
            analysis_results = []
            for result in results:
                if result.get("data") and hasattr(result["data"], "to_dict"):
                    # DataFrame format
                    df_dict = result["data"].to_dict("records")
                    if df_dict:
                        analysis_results.append({
                            "columns": list(df_dict[0].keys()) if df_dict else [],
                            "data": [list(record.values()) for record in df_dict],
                            "database_name": result.get("database_name", "unknown")
                        })
                elif isinstance(result.get("data"), dict) and "columns" in result["data"]:
                    # Already in correct format
                    analysis_results.append(result["data"])

            return analysis_results

        except Exception as e:
            logger.error(f"Error extracting query results for analysis: {e}")
            return []

    def _should_apply_phase3_analysis(self, query_analysis: Dict[str, Any], query_results: List[Dict[str, Any]]) -> bool:
        """Determine if Phase 3 analysis should be applied."""
        # Check query intent first - must be analytical
        intent = query_analysis.get("intent", "operational")
        if intent not in ["analytical", "exploratory", "comparative", "reporting"]:
            return False

        # Check data size (only analyze if we have meaningful data)
        total_rows = sum(len(result.get("data", [])) for result in query_results)
        if total_rows < 5:  # Too little data for meaningful analysis
            return False

        # Check for numeric columns (needed for statistical analysis)
        has_numeric_data = False
        for result in query_results:
            columns = result.get("columns", [])
            data = result.get("data", [])
            if data and columns:
                # Check if any column contains numeric data
                for col_idx, col_name in enumerate(columns):
                    sample_values = [row[col_idx] for row in data[:5] if len(row) > col_idx]
                    if any(isinstance(val, (int, float)) for val in sample_values):
                        has_numeric_data = True
                        break
            if has_numeric_data:
                break

        # Only apply Phase 3 if we have both analytical intent AND numeric data
        return has_numeric_data

    def _should_apply_comparative_analysis(self, query_analysis: Dict[str, Any]) -> bool:
        """Determine if comparative analysis should be applied."""
        intent = query_analysis.get("intent", "operational")
        return intent in ["comparative", "analytical", "reporting"]

    def _infer_business_domain(self, query_analysis: Dict[str, Any]) -> str:
        """Infer business domain from query analysis."""
        query = query_analysis.get("enhanced_query", "").lower()
        entities = [entity.lower() for entity in query_analysis.get("entities", [])]

        # Check for domain keywords
        if any(keyword in query or keyword in " ".join(entities)
               for keyword in ["sales", "revenue", "deals", "customers", "orders"]):
            return "sales"
        elif any(keyword in query or keyword in " ".join(entities)
                 for keyword in ["marketing", "campaigns", "leads", "conversion", "ctr"]):
            return "marketing"
        elif any(keyword in query or keyword in " ".join(entities)
                 for keyword in ["finance", "cost", "profit", "budget", "expense"]):
            return "finance"
        elif any(keyword in query or keyword in " ".join(entities)
                 for keyword in ["operations", "efficiency", "performance", "productivity"]):
            return "operations"
        else:
            return "general"

    def _detect_time_column(self, query_results: List[Dict[str, Any]]) -> Optional[str]:
        """Detect time column in query results."""
        for result in query_results:
            columns = result.get("columns", [])
            for col in columns:
                if any(keyword in col.lower() for keyword in ["date", "time", "timestamp", "created", "updated"]):
                    return col
        return None

    def _detect_metric_columns(self, query_results: List[Dict[str, Any]]) -> List[str]:
        """Detect metric columns in query results."""
        metric_columns = []
        for result in query_results:
            columns = result.get("columns", [])
            data = result.get("data", [])

            if data and columns:
                for col_idx, col_name in enumerate(columns):
                    # Check if column contains numeric data
                    sample_values = [row[col_idx] for row in data[:5] if len(row) > col_idx]
                    if any(isinstance(val, (int, float)) for val in sample_values):
                        metric_columns.append(col_name)

        return list(set(metric_columns))  # Remove duplicates

    def _enhance_sql_response_with_phase3(
        self,
        original_response: Dict[str, Any],
        insights_data: Dict[str, Any],
        business_data: Dict[str, Any],
        comparative_data: Optional[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Enhance SQL response with Phase 3 analysis results."""
        enhanced_response = original_response.copy()

        # Add Phase 3 analysis to metadata
        if "metadata" not in enhanced_response:
            enhanced_response["metadata"] = {}

        enhanced_response["metadata"]["phase3_analysis"] = {
            "enabled": True,
            "insights_summary": {
                "total_insights": len(insights_data.get("insights", [])),
                "patterns_detected": len(insights_data.get("patterns", [])),
                "anomalies_found": len(insights_data.get("anomalies", [])),
                "recommendations": len(insights_data.get("recommendations", []))
            },
            "business_context": {
                "domain": business_data.get("business_metadata", {}).get("inferred_domain", "general"),
                "business_insights": len(business_data.get("business_insights", [])),
                "risk_level": business_data.get("risk_assessment", {}).get("overall_risk_level", "unknown")
            }
        }

        # Add comparative analysis if available
        if comparative_data:
            enhanced_response["metadata"]["phase3_analysis"]["comparative_analysis"] = {
                "insights": len(comparative_data.get("comparative_insights", [])),
                "trend_direction": comparative_data.get("trend_analysis", {}).get("overall_trend", "unknown")
            }

        # Add detailed analysis data for advanced users
        enhanced_response["phase3_analysis"] = {
            "statistical_insights": insights_data,
            "business_context": business_data,
            "comparative_analysis": comparative_data
        }

        return enhanced_response

    # ────────────────────────────────────────────────────────────────────────
    # Performance Optimization: Caching Methods
    # ────────────────────────────────────────────────────────────────────────
    def _generate_cache_key(
        self,
        query: str,
        target_databases: Optional[List[str]],
        target_tables: Optional[Dict[str, List[str]]],
        target_columns: Optional[Dict[str, Dict[str, List[str]]]]
    ) -> str:
        """Generate a cache key for the query and targeting parameters."""
        import hashlib

        # Create a deterministic string representation
        cache_data = {
            "query": query.lower().strip(),
            "target_databases": sorted(target_databases) if target_databases else [],
            "target_tables": dict(sorted(target_tables.items())) if target_tables else {},
            "target_columns": dict(sorted(target_columns.items())) if target_columns else {}
        }

        cache_string = str(cache_data)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid."""
        if cache_key not in self.query_cache:
            return False

        import time
        cache_time = self.cache_ttl.get(cache_key, 0)
        return time.time() - cache_time < self.CACHE_DURATION

    def _cache_result(self, cache_key: str, result: Dict[str, Any]) -> None:
        """Cache query result with timestamp."""
        import time
        self.query_cache[cache_key] = {"data": result}
        self.cache_ttl[cache_key] = time.time()

        # Limit cache size to prevent memory issues
        if len(self.query_cache) > 100:
            # Remove oldest entries
            oldest_keys = sorted(self.cache_ttl.keys(), key=lambda k: self.cache_ttl[k])[:20]
            for key in oldest_keys:
                self.query_cache.pop(key, None)
                self.cache_ttl.pop(key, None)

    def clear_cache(self) -> None:
        """Clear all cached data."""
        self.query_cache.clear()
        self.schema_cache.clear()
        self.cache_ttl.clear()
