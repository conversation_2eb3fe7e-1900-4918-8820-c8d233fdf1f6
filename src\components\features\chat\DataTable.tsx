import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Download, ExternalLink, Table as TableIcon, AlertCircle, Loader2 } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  TableCaption,
} from "@/components/ui/table";
import * as XLSX from 'xlsx';

interface OutputFile {
  database_name: string;
  file_path: string;
  format: string;
}

interface DataTableProps {
  outputFiles: OutputFile[];
  className?: string;
}

interface TableData {
  headers: string[];
  rows: string[][];
  fileName: string;
  format: string;
}

const DataTable: React.FC<DataTableProps> = ({ outputFiles, className = "" }) => {
  const [tableData, setTableData] = useState<TableData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(true); // Auto-expand by default

  // Parse CSV text into structured data
  const parseCSV = (csvText: string): { headers: string[]; rows: string[][] } => {
    const lines = csvText.trim().split('\n');
    if (lines.length === 0) return { headers: [], rows: [] };

    // Function to parse a CSV line respecting quoted fields
    const parseCSVLine = (line: string): string[] => {
      const result: string[] = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          if (inQuotes && line[i + 1] === '"') {
            // Handle escaped quotes
            current += '"';
            i++; // Skip the next quote
          } else {
            // Toggle quote state
            inQuotes = !inQuotes;
          }
        } else if (char === ',' && !inQuotes) {
          // End of field
          result.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }

      // Add the last field
      result.push(current.trim());
      return result;
    };

    const headers = parseCSVLine(lines[0]);
    const rows = lines.slice(1).map(line => parseCSVLine(line));

    return { headers, rows };
  };

  // Parse Excel file into structured data
  const parseExcel = (arrayBuffer: ArrayBuffer): { headers: string[]; rows: string[][] } => {
    try {
      const workbook = XLSX.read(arrayBuffer, { type: 'array' });
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      // Convert to JSON format first
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

      if (jsonData.length === 0) return { headers: [], rows: [] };

      const headers = jsonData[0] || [];
      const rows = jsonData.slice(1);

      return { headers, rows };
    } catch (err) {
      console.error('Error parsing Excel file:', err);
      throw new Error('Failed to parse Excel file');
    }
  };

  // Fetch and parse file data from S3 URL
  const fetchFileData = async (url: string, format: string, fileName: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.status} ${response.statusText}`);
      }

      let parsedData: { headers: string[]; rows: string[][] };

      if (format.toLowerCase() === 'csv') {
        const csvText = await response.text();
        parsedData = parseCSV(csvText);
      } else if (format.toLowerCase() === 'xlsx' || format.toLowerCase() === 'xls') {
        const arrayBuffer = await response.arrayBuffer();
        parsedData = parseExcel(arrayBuffer);
      } else {
        throw new Error(`Unsupported file format: ${format}`);
      }

      setTableData({
        headers: parsedData.headers,
        rows: parsedData.rows,
        fileName,
        format
      });
    } catch (err) {
      console.error('Failed to fetch file data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  // Auto-fetch data when component mounts or outputFiles change
  useEffect(() => {
    if (outputFiles && outputFiles.length > 0) {
      const primaryFile = outputFiles[0];
      const fileName = primaryFile.file_path.split('/').pop() || `data.${primaryFile.format}`;
      fetchFileData(primaryFile.file_path, primaryFile.format, fileName);
    }
  }, [outputFiles]);

  // Download file from S3 URL
  const downloadFile = (url: string, fileName: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (!outputFiles || outputFiles.length === 0) {
    return null;
  }

  const primaryFile = outputFiles[0]; // Use the first file as primary
  const fileName = primaryFile.file_path.split('/').pop() || `data.${primaryFile.format}`;

  return (
    <div className={`border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50/30 dark:bg-blue-900/10 p-4 mt-4 ${className}`}>
      {/* Header with file info and actions */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <TableIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h4 className="font-semibold text-sm text-blue-900 dark:text-blue-100">Query Results Data</h4>
            <p className="text-xs text-blue-700 dark:text-blue-300">
              {primaryFile.database_name} • {primaryFile.format.toUpperCase()} • {tableData?.rows.length || 0} rows
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            onClick={() => downloadFile(primaryFile.file_path, fileName)}
            size="sm"
            variant="outline"
            className="text-xs"
          >
            <Download className="w-3 h-3 mr-1" />
            Download
          </Button>

          <Button
            onClick={() => window.open(primaryFile.file_path, '_blank')}
            size="sm"
            variant="outline"
            className="text-xs"
          >
            <ExternalLink className="w-3 h-3 mr-1" />
            Open
          </Button>
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex items-center justify-center py-8 text-gray-600 dark:text-gray-400">
          <Loader2 className="w-4 h-4 animate-spin mr-2" />
          <span className="text-sm">Loading table data...</span>
        </div>
      )}

      {/* Error state */}
      {error && !isLoading && (
        <div className="flex items-center gap-2 text-red-600 dark:text-red-400 text-sm mb-3 p-3 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          <div>
            <p className="font-medium">Failed to load table data</p>
            <p className="text-xs mt-1">{error}</p>
          </div>
        </div>
      )}

      {/* Table display */}
      {isExpanded && tableData && !error && !isLoading && (
        <div className="max-h-96 overflow-auto border border-blue-200 dark:border-blue-700 rounded-lg bg-white dark:bg-gray-900">
          <Table>
            <TableCaption className="text-xs text-blue-600 dark:text-blue-400 font-medium py-2">
              Showing {Math.min(tableData.rows.length, 100)} of {tableData.rows.length} rows • {tableData.fileName}
            </TableCaption>
            <TableHeader>
              <TableRow>
                {tableData.headers.map((header, index) => (
                  <TableHead key={index} className="text-xs font-medium">
                    {header || `Column ${index + 1}`}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {tableData.rows.slice(0, 100).map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {tableData.headers.map((_, cellIndex) => (
                    <TableCell key={cellIndex} className="text-xs">
                      {row[cellIndex] || ''}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {tableData.rows.length > 100 && (
            <div className="p-2 text-xs text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-800 border-t">
              Showing first 100 rows of {tableData.rows.length} total rows. Download the full file to see all data.
            </div>
          )}
        </div>
      )}

      {/* Expand/Collapse button */}
      {tableData && !isLoading && (
        <div className="mt-2 flex justify-center">
          <Button
            onClick={() => setIsExpanded(!isExpanded)}
            size="sm"
            variant="ghost"
            className="text-xs"
          >
            {isExpanded ? 'Collapse Table' : 'Expand Table'}
          </Button>
        </div>
      )}

      {/* Collapsed state preview */}
      {!isExpanded && tableData && !isLoading && (
        <div className="text-center py-4 text-gray-600 dark:text-gray-400">
          <TableIcon className="w-6 h-6 mx-auto mb-2 opacity-50" />
          <p className="text-sm">
            Table with {tableData.rows.length} rows and {tableData.headers.length} columns
          </p>
          <p className="text-xs mt-1">Click "Expand Table" to view data</p>
        </div>
      )}
    </div>
  );
};

export default DataTable; 