import { useState, useRef, useCallback, useEffect } from 'react';
import { getApiBaseUrl, STORAGE_KEYS } from '@/lib/constants';
import { 
  UseTokenStreamingOptions, 
  UseTokenStreamingReturn, 
  StreamingState, 
  SSEEvent 
} from '@/types/streaming';

export const useTokenStreaming = (options: UseTokenStreamingOptions): UseTokenStreamingReturn => {
  const {
    sessionId,
    query,
    outputFormat = 'excel',
    onTokenReceived,
    onComplete,
    onError
  } = options;

  const [streamingState, setStreamingState] = useState<StreamingState>({
    isStreaming: false,
    streamingContent: '',
    error: null,
    isComplete: false,
  });

  const [isConnected, setIsConnected] = useState(false);
  const eventSourceRef = useRef<EventSource | null>(null);
  const accumulatedContentRef = useRef<string>('');

  // Cleanup function
  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setIsConnected(false);
  }, []);

  // Stop streaming
  const stopStreaming = useCallback(() => {
    cleanup();
    setStreamingState(prev => ({
      ...prev,
      isStreaming: false,
    }));
  }, [cleanup]);

  // Start streaming
  const startStreaming = useCallback(() => {
    // Reset state
    accumulatedContentRef.current = '';
    setStreamingState({
      isStreaming: true,
      streamingContent: '',
      error: null,
      isComplete: false,
    });

    // Get auth token
    const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    if (!token) {
      const error = 'No authentication token found';
      setStreamingState(prev => ({ ...prev, error, isStreaming: false }));
      onError?.(error);
      return;
    }

    try {
      // Create SSE URL with query parameters
      const baseUrl = getApiBaseUrl();
      const url = new URL(`${baseUrl}/ask/question`);
      url.searchParams.append('query', query);
      url.searchParams.append('output_format', outputFormat);
      url.searchParams.append('session_id', sessionId);

      // Create EventSource with custom headers (Note: EventSource doesn't support custom headers directly)
      // We'll need to pass the token as a query parameter or use a different approach
      url.searchParams.append('token', token);

      const eventSource = new EventSource(url.toString());
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log('✅ SSE connection opened');
        setIsConnected(true);
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data) as SSEEvent;
          console.log('📨 SSE event received:', data);

          switch (data.type) {
            case 'token_stream':
              if (data.data.token) {
                accumulatedContentRef.current += data.data.token;
                setStreamingState(prev => ({
                  ...prev,
                  streamingContent: accumulatedContentRef.current,
                }));
                onTokenReceived?.(data.data.token);
              }
              break;

            case 'conversation_complete':
              const finalContent = data.data.message || accumulatedContentRef.current;
              setStreamingState(prev => ({
                ...prev,
                streamingContent: finalContent,
                isStreaming: false,
                isComplete: true,
              }));
              onComplete?.(finalContent);
              cleanup();
              break;

            case 'error':
              const errorMsg = data.data.error || 'Unknown streaming error';
              setStreamingState(prev => ({
                ...prev,
                error: errorMsg,
                isStreaming: false,
              }));
              onError?.(errorMsg);
              cleanup();
              break;

            default:
              console.warn('Unknown SSE event type:', (data as any).type);
          }
        } catch (parseError) {
          console.error('Error parsing SSE event:', parseError);
          const error = 'Failed to parse streaming response';
          setStreamingState(prev => ({ ...prev, error, isStreaming: false }));
          onError?.(error);
        }
      };

      eventSource.onerror = (error) => {
        console.error('❌ SSE connection error:', error);
        const errorMsg = 'Connection to server lost';
        setStreamingState(prev => ({
          ...prev,
          error: errorMsg,
          isStreaming: false,
        }));
        onError?.(errorMsg);
        setIsConnected(false);
        
        // Attempt to reconnect after a delay
        setTimeout(() => {
          if (eventSourceRef.current?.readyState === EventSource.CLOSED) {
            console.log('🔄 Attempting to reconnect...');
            startStreaming();
          }
        }, 3000);
      };

    } catch (error) {
      console.error('Error starting SSE connection:', error);
      const errorMsg = 'Failed to start streaming connection';
      setStreamingState(prev => ({ ...prev, error: errorMsg, isStreaming: false }));
      onError?.(errorMsg);
    }
  }, [sessionId, query, outputFormat, onTokenReceived, onComplete, onError, cleanup]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    startStreaming,
    stopStreaming,
    streamingState,
    isConnected,
  };
};
