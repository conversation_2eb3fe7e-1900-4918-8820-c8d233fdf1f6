"""
Dataset-catalog service
–––––––––––––––––––––––
Keeps lightweight metadata for every dataset stored in S3 so
the Dataset-Manager agents can reason without opening the files.
For MVP we store it in DynamoDB, but the class is 100 % storage-agnostic.
"""

from __future__ import annotations
import logging, time, uuid
from typing import Dict, List, Any, Optional

import boto3
from boto3.dynamodb.conditions import Key

from app.config.settings import AWS_REGION, DATASET_CATALOG_TABLE

logger = logging.getLogger(__name__)


class DatasetCatalogService:
    """Encrypts column stats client-side → stores a tiny JSON blob server-side."""

    def __init__(self) -> None:
        ddb = boto3.resource("dynamodb", region_name=AWS_REGION)
        self.table = ddb.Table(DATASET_CATALOG_TABLE)

    # ------------------------------------------------------------------ #
    # high-level API
    # ------------------------------------------------------------------ #
    async def register_dataset(
        self,
        *,
        user_id: str,
        original_source: str,
        s3_key: str,
        columns: List[str],
        row_count: int,
        file_format: str,
    ) -> str:
        ds_id = f"ds_{uuid.uuid4().hex[:8]}"
        item = {
            "user_id": user_id,
            "dataset_id": ds_id,
            "s3_key": s3_key,
            "columns": columns,
            "row_count": row_count,
            "file_format": file_format,
            "created_at": int(time.time()),
            "source": original_source,
        }
        self.table.put_item(Item=item)
        return ds_id

    async def list_user_datasets(self, user_id: str) -> List[Dict[str, Any]]:
        resp = self.table.query(
            KeyConditionExpression=Key("user_id").eq(user_id),
            ProjectionExpression="dataset_id, s3_key, #cols, row_count, file_format, #app",
            ExpressionAttributeNames={"#cols": "columns", "#app": "source"}
        )
        return resp.get("Items", [])

    async def get_metadata(
        self, user_id: str, dataset_id: str
    ) -> Optional[Dict[str, Any]]:
        resp = self.table.get_item(
            Key={"user_id": user_id, "dataset_id": dataset_id}
        )
        return resp.get("Item")

    async def list_datasets(self) -> List[Dict[str, Any]]:
        """List all datasets across all users (for admin/analysis purposes)."""
        try:
            resp = self.table.scan()
            return resp.get("Items", [])
        except Exception as e:
            logger.error(f"Error listing all datasets: {e}")
            return []

    async def get_dataset(self, dataset_id: str) -> Optional[Dict[str, Any]]:
        """Get a dataset by ID (searches across all users)."""
        try:
            # Since we don't have the user_id, we need to scan for the dataset_id
            resp = self.table.scan(
                FilterExpression=Key("dataset_id").eq(dataset_id)
            )
            items = resp.get("Items", [])
            return items[0] if items else None
        except Exception as e:
            logger.error(f"Error getting dataset {dataset_id}: {e}")
            return None
