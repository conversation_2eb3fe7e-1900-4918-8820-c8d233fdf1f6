import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/providers/**/*.{js,ts,jsx,tsx,mdx}',
    './src/lib/**/*.{js,ts,jsx,tsx,mdx}',
    // Legacy paths for any remaining files
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        inter: ['var(--font-inter)', 'system-ui', 'sans-serif'], // Added system-ui as a fallback
        'noto-sans': ['var(--font-noto-sans)', 'system-ui', 'sans-serif'], // Added system-ui
      },
      colors: {
        // Existing design colors
        'design-white': '#ffffff', // From body bg
        'design-border': '#f0f2f4', // From header border
        'design-text-primary': '#111418', // From header text, h2 text
        'design-text-secondary': '#637588', // From placeholder, links
        'design-input-bg': '#f0f2f4', // From input bg
        'design-button-blue': '#197ce5', // From button bg
        'design-button-blue-hover': '#136acb', // Assuming a slightly darker hover state

        // shadcn/ui colors
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },

        // Custom spektr colors for the hero component
        'spektr-cyan': {
          50: '#ecfeff',
        },

        // Sidebar/ChatGPT-style custom properties
        'sidebar-bg': 'var(--sidebar-bg)',
        'sidebar-surface-primary': 'var(--sidebar-surface-primary)',
        'sidebar-surface-secondary': 'var(--sidebar-surface-secondary)',
        'sidebar-surface-tertiary': 'var(--sidebar-surface-tertiary)',
        'sidebar-border': 'var(--sidebar-border)',
        'sidebar-text-primary': 'var(--sidebar-text-primary)',
        'sidebar-text-secondary': 'var(--sidebar-text-secondary)',
        'sidebar-text-tertiary': 'var(--sidebar-text-tertiary)',
        'sidebar-icon': 'var(--sidebar-icon)',
        'surface-hover': 'var(--surface-hover)',
        'surface-selected': 'var(--surface-selected)',
      },
      width: {
        'sidebar': 'var(--sidebar-width)', // Responsive sidebar width
      },
      height: {
        '14': '3.5rem', // for h-14 used in inputs
        'header': 'var(--header-height)', // Responsive header height
      },
      spacing: {
        'sidebar': 'var(--sidebar-width)', // For left margin/padding
        'header': 'var(--header-height)', // For top margin/padding
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
        'xl': '0.75rem', // for rounded-xl used in inputs
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'), // From HTML script tag
    require('@tailwindcss/container-queries'), // From HTML script tag
  ],
};
export default config; 