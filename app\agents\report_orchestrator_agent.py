"""
Intelligent Conversational Report Orchestrator Agent
====================================================
A tool-based conversational agent that understands context and makes iterative decisions.

🧠 CORE PHILOSOPHY:
- Works like a real data scientist: iterative, conversational, context-aware
- Uses tools based on user intent and conversation history
- Can modify existing work instead of starting from scratch
- Makes back-and-forth decisions between analysis and code generation
- Understands when to use which tool based on user questions

🛠️ AVAILABLE TOOLS:
- analyze_conversation: Understand user intent and conversation context
- create_analysis_plan: Create initial analysis strategy  
- refine_analysis_plan: Modify existing analysis based on new requirements
- generate_code: Generate new code from scratch
- modify_existing_code: Modify existing code based on requirements
- execute_code: Run code and get results with feedback
- create_dashboard: Build visualization dashboard
- extract_previous_work: Get previous analysis/code from conversation

🤖 DECISION MAKING:
- Analyzes conversation history to understand context
- Determines if this is new work or modification of existing work
- Chooses appropriate tools based on user intent
- Makes iterative tool calls as needed
- Provides reasoning for each decision
"""

from __future__ import annotations
import asyncio
import json
import logging
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional

from app.agents.base import Agent, AgentResponse
from app.agents.code_execution_agent import CodeExecutionAgent
from app.agents.code_generation_agent import CodeGenerationAgent
from app.agents.dashboard_agent import DashboardAgent
from app.agents.data_analysis_agent import DataAnalysisAgent
from app.utils.bedrock_client import BedrockClient, ModelPurpose
from app.services.dataset_catalog_service import DatasetCatalogService
from app.services.conversation_service import ConversationService

logger = logging.getLogger(__name__)

# Configuration
_MAX_TOOL_CALLS = 10  # Maximum tool calls per user message
_TOOL_TIMEOUT = 300  # 5 minutes max per tool call

class ConversationalReportOrchestrator(Agent):
    """
    Tool-based conversational agent that works iteratively like a real data scientist.
    
    Uses conversation context to make intelligent decisions about which tools to use
    and when to use them. Can modify existing work instead of starting from scratch.
    """

    def __init__(self, user_id: str, *, agent_id: str | None = None) -> None:
        self.agent_id = agent_id or "conversational_report_orchestrator"
        self.user_id = user_id

        # Initialize tool agents
        self._data_analysis_agent = DataAnalysisAgent()
        self._code_generation_agent = CodeGenerationAgent()
        self._code_execution_agent = CodeExecutionAgent()
        self._dashboard_agent = DashboardAgent()
        
        # Services for context and decision making
        self._llm = BedrockClient(purpose=ModelPurpose.ANALYSIS)
        self._dataset_catalog = DatasetCatalogService()
        self._conversation_service = ConversationService()
        
        self.initialized = False

    async def initialize(self) -> None:
        """Initialize all sub-agents and services."""
        if self.initialized:
            return

        logger.info("🚀 Initializing Conversational Report Orchestrator")
        await asyncio.gather(
            self._data_analysis_agent.initialize(),
            self._code_generation_agent.initialize(),
            self._code_execution_agent.initialize(),
            self._dashboard_agent.initialize()
        )
        self.initialized = True
        logger.info("✅ All agents and services initialized successfully")

    async def process(self, message: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Main conversational processing loop with tool-based decision making.
        
        CONVERSATIONAL WORKFLOW:
        =======================
        1. 🧠 ANALYZE_CONVERSATION - Understand user intent and context
        2. 🛠️ CHOOSE_TOOLS - Decide which tools to use based on intent
        3. 🔄 ITERATIVE_EXECUTION - Execute tools with feedback loops
        4. 🎯 VALIDATE_COMPLETION - Ensure user's needs are met
        """
        
        if not self.initialized:
            await self.initialize()

        # Extract message details
        user_query = message.get("query", "")
        session_id = message.get("session_id", "unknown")
        datasets_loaded = message.get("datasets", [])
        
        logger.info("=" * 80)
        logger.info("🤖 STARTING CONVERSATIONAL ORCHESTRATION")
        logger.info(f"👤 User: {self.user_id}")
        logger.info(f"💬 Session: {session_id}")
        logger.info(f"❓ Query: {user_query}")
        logger.info(f"📁 Datasets: {len(datasets_loaded)}")
        logger.info("=" * 80)

        # Initialize orchestration state
        orchestration_state = {
            "user_query": user_query,
            "session_id": session_id,
            "datasets_loaded": datasets_loaded,
            "user_id": self.user_id,
            
            # Conversation context
            "conversation_history": [],
            "previous_work": {},
            "user_intent": {},
            
            # Tool execution state
            "tool_calls": [],
            "current_analysis_plan": None,
            "current_code": None,
            "execution_results": None,
            "dashboard_created": None,
            
            # Control
            "task_complete": False,
            "tool_call_count": 0
        }

        # Main tool-based orchestration loop
        while not orchestration_state["task_complete"] and orchestration_state["tool_call_count"] < _MAX_TOOL_CALLS:
            orchestration_state["tool_call_count"] += 1
            call_number = orchestration_state["tool_call_count"]
            
            logger.info(f"\n{'='*20} TOOL CALL {call_number}/{_MAX_TOOL_CALLS} {'='*20}")
            
            # 1. ANALYZE - What should we do next?
            logger.info("🧠 ANALYZE: Understanding user intent and context...")
            decision = await self._make_intelligent_tool_decision(orchestration_state)
            
            tool_name = decision["tool"]
            reasoning = decision["reasoning"]
            parameters = decision.get("parameters", {})
            
            logger.info(f"🛠️ TOOL DECISION: {tool_name}")
            logger.info(f"💭 REASONING: {reasoning}")
            
            # 2. EXECUTE - Use the chosen tool
            logger.info(f"⚡ EXECUTING TOOL: {tool_name}")
            async for event in self._execute_tool(tool_name, parameters, orchestration_state):
                yield event



            # 3.5. CAPTURE TOOL OUTCOME - Store the result for context
            tool_outcome = await self._capture_tool_outcome(tool_name, orchestration_state)
            
            # 4. UPDATE - Record tool call with outcome
            orchestration_state["tool_calls"].append({
                "call_number": call_number,
                "tool": tool_name,
                "reasoning": reasoning,
                "parameters": parameters,
                "outcome": tool_outcome,
                "timestamp": datetime.utcnow().isoformat()
            })

            # 5. CHECK COMPLETION - Are we done?
            if decision.get("complete", False) or self._check_task_completion(orchestration_state):
                orchestration_state["task_complete"] = True
                logger.info("🎉 TASK COMPLETED SUCCESSFULLY!")
                
        # Final completion message
        if not orchestration_state["task_complete"]:
            logger.warning(f"⚠️ Reached maximum tool calls ({_MAX_TOOL_CALLS}) without completion")
            yield self._create_event("agent_result", "orchestrator", {
                "message": f"⚠️ Complex request required {_MAX_TOOL_CALLS}+ tool calls. Results may be partial.",
                "status": "partial_completion",
                "tool_calls_made": orchestration_state["tool_call_count"]
            })
        else:
            yield self._create_event("agent_result", "orchestrator", {
                "message": "🎉 Task completed successfully!",
                "status": "completed",
                "tool_calls_made": orchestration_state["tool_call_count"]
            })

    async def _make_intelligent_tool_decision(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use LLM to make intelligent decisions about which tool to use next.
        This is the core intelligence of the conversational agent.
        """
        
        # Build comprehensive context for decision making
        context = await self._build_tool_decision_context(state)
        
        system_prompt = """You are an expert data scientist and AI orchestrator with access to specialized tools. Your job is to analyze the user's request and conversation context to decide which tool to use next.

CORE PRINCIPLES:
================
1. UNDERSTAND INTENT: What is the user really asking for?
2. LEVERAGE CONTEXT: Use conversation history and previous work
3. BE EFFICIENT: Don't redo work that's already been done
4. BE ITERATIVE: Work step-by-step like a real data scientist
5. RECOGNIZE COMPLETION: Stop when the task is done!

AVAILABLE TOOLS:
===============
- analyze_conversation: Understand user intent and extract conversation context
- create_analysis_plan: Create initial data analysis strategy (for new analysis)
- refine_analysis_plan: Modify existing analysis plan based on new requirements
- generate_code: Generate new code from scratch (for new analysis)
- modify_existing_code: Modify existing code based on new requirements
- execute_code: Run code and get results with feedback
- create_dashboard: Build visualization dashboard from results
- extract_previous_work: Get previous analysis/code from conversation history

DECISION LOGIC:
==============
FIRST TIME USERS / NEW SESSIONS:
- If no conversation history → start with analyze_conversation
- If need to understand data → create_analysis_plan
- If have plan but no code → generate_code
- If have code but not executed → execute_code

RETURNING USERS / EXISTING WORK:
- If user wants to modify existing code → modify_existing_code
- If user wants different analysis approach → refine_analysis_plan
- If user wants to try different parameters → modify_existing_code
- If user wants to see results → execute_code or create_dashboard

COMPLETION CONDITIONS (CRITICAL - LOOK FOR THESE!):
==========================================
✅ TASK IS COMPLETE when:
- All workflow stages completed: analysis_plan + code_generation + code_execution + dashboard
- Code executed successfully AND dashboard created
- User has not provided new requirements or modifications
- No errors that require fixing

⚠️ AVOID INFINITE LOOPS:
- If you see repeated tool calls of the same type (especially analyze_conversation)
- If the same tool has been called 2+ times in a row without progress
- Set "complete": true to break the loop

🛑 NEVER call analyze_conversation repeatedly just to "check if user needs anything else"
   If the main workflow is done, mark the task as complete!

🎯 HOW TO INDICATE COMPLETION:
When the task is complete, return: {"tool": "complete", "reasoning": "Task completed", "complete": true}
DO NOT use "none" or other invalid tool names.

CRITICAL: You must return ONLY valid JSON. No explanations or markdown."""

        prompt = f"""Analyze the current situation and decide which tool to use next.

CURRENT CONTEXT:
{context}

Based on this context, decide:
1. What tool should be used next?
2. What parameters should be passed to that tool?
3. Why is this the best choice?
4. Is the task complete after this tool call?

RESPONSE FORMAT (JSON only):
{{
    "tool": "tool_name",
    "reasoning": "Clear explanation of why this tool was chosen",
    "parameters": {{
        "param1": "value1",
        "param2": "value2"
    }},
    "complete": false,
    "confidence": 0.9
}}

IMPORTANT EXAMPLES:
- If user says "change the library from sklearn to xgboost": tool="modify_existing_code"
- If user asks for "analysis of my data" with no history: tool="analyze_conversation"
- If code failed with error: tool="modify_existing_code" with error feedback
- If user wants to see results: tool="execute_code" or "create_dashboard"
- If plan exists but no code: tool="generate_code"

Respond with ONLY the JSON:"""

        try:
            response = await asyncio.wait_for(
                self._llm.generate_response(prompt=prompt, system_prompt=system_prompt, temperature=0.1),
                timeout=30.0
            )
            
            cleaned_response = self._clean_llm_response(response)
            decision = json.loads(cleaned_response)
            
            # Validate required fields
            required_fields = ["tool", "reasoning"]
            if not all(field in decision for field in required_fields):
                logger.warning(f"Invalid decision format, using fallback")
                return self._fallback_tool_decision(state)
            
            # Set defaults
            decision.setdefault("parameters", {})
            decision.setdefault("complete", False)
            decision.setdefault("confidence", 0.8)
            
            logger.debug(f"✅ Tool decision: {decision['tool']} (confidence: {decision['confidence']})")
            return decision
            
        except (asyncio.TimeoutError, json.JSONDecodeError, Exception) as e:
            logger.warning(f"Tool decision failed: {e}, using fallback")
            return self._fallback_tool_decision(state)

    async def _build_tool_decision_context(self, state: Dict[str, Any]) -> str:
        """Build comprehensive context for tool decision making."""
        context_parts = []
        
        # Basic request info
        context_parts.append(f"USER QUERY: {state['user_query']}")
        context_parts.append(f"SESSION ID: {state['session_id']}")
        context_parts.append(f"TOOL CALL NUMBER: {state['tool_call_count']}")
        
        # Dataset context
        if state['datasets_loaded']:
            context_parts.append(f"DATASETS AVAILABLE: {len(state['datasets_loaded'])} datasets loaded")
            for i, dataset_id in enumerate(state['datasets_loaded'][:3]):  # Show first 3
                context_parts.append(f"  - Dataset {i+1}: {dataset_id}")
        else:
            context_parts.append("DATASETS AVAILABLE: No datasets loaded")
        
        # Conversation history
        try:
            history = await self._conversation_service.get_history(
                user_id=state['user_id'], 
                session_id=state['session_id'], 
                limit=10
            )
            if history:
                context_parts.append("CONVERSATION HISTORY:")
                for i, msg in enumerate(history[-5:]):  # Last 5 messages
                    role = msg['role']
                    content = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
                    context_parts.append(f"  {i+1}. {role}: {content}")
            else:
                context_parts.append("CONVERSATION HISTORY: No previous conversation")
        except Exception as e:
            context_parts.append(f"CONVERSATION HISTORY: Error loading history: {e}")
        
        # Current state with standardized response parsing
        if state.get('current_analysis_plan'):
            plan_type = state['current_analysis_plan'].get('analysis_type', 'Unknown')
            context_parts.append(f"CURRENT ANALYSIS PLAN: ✅ Exists ({plan_type})")
        else:
            context_parts.append("CURRENT ANALYSIS PLAN: ❌ None")
            
        if state.get('current_code'):
            context_parts.append("CURRENT CODE: ✅ Generated and available")
        else:
            context_parts.append("CURRENT CODE: ❌ None")
            
        # Check execution results from both legacy and standardized formats
        execution_results = state.get('execution_results')
        last_execution_response = state.get('last_execution_response')
        
        if execution_results or last_execution_response:
            # Try to get execution success from standardized format first
            execution_success = False
            return_code = -1
            error_details = {}
            
            if last_execution_response:
                key_results = last_execution_response.get('key_results', {})
                execution_success = key_results.get('execution_success', False)
                return_code = key_results.get('return_code', -1)
                error_details = last_execution_response.get('error_details', {})
            elif execution_results:
                execution_success = execution_results.get('return_code') == 0
                return_code = execution_results.get('return_code', -1)
            
            if execution_success:
                context_parts.append("EXECUTION RESULTS: ✅ Success")
                
                # Add analysis results from standardized format
                if last_execution_response:
                    key_results = last_execution_response.get('key_results', {})
                    if key_results.get('model_accuracy'):
                        accuracy = key_results['model_accuracy']
                        context_parts.append(f"  - Model accuracy: {accuracy:.1%}")
                    elif key_results.get('has_analysis_results'):
                        context_parts.append(f"  - Analysis completed with insights")
                        
                # Fallback to legacy format
                elif execution_results and 'analysis_results' in execution_results:
                    analysis_results = execution_results['analysis_results']
                    if 'accuracy' in analysis_results:
                        accuracy = analysis_results['accuracy']
                        context_parts.append(f"  - Model accuracy: {accuracy:.1%}")
                    if 'key_findings' in analysis_results:
                        findings_count = len(analysis_results['key_findings'])
                        context_parts.append(f"  - Key findings: {findings_count} insights")
            else:
                # Extract error information from standardized format
                error_summary = "Unknown error"
                if error_details.get('stderr'):
                    error_summary = error_details['stderr'][:100]
                elif execution_results and execution_results.get('stderr'):
                    error_summary = execution_results['stderr'][:100]
                    
                context_parts.append(f"EXECUTION RESULTS: ❌ Failed - {error_summary}")
                
                # Add retry recommendation if available
                if last_execution_response and last_execution_response.get('status') == 'retry_needed':
                    context_parts.append("  - ⚠️ Retry recommended")
        else:
            context_parts.append("EXECUTION RESULTS: ❌ None")
            
        # Dashboard status
        dashboard_data = state.get('dashboard_created')
        last_dashboard_response = state.get('last_dashboard_response')
        
        if dashboard_data or last_dashboard_response:
            if last_dashboard_response:
                key_results = last_dashboard_response.get('key_results', {})
                component_count = key_results.get('component_count', 0)
                context_parts.append(f"DASHBOARD: ✅ Created ({component_count} components)")
            else:
                context_parts.append("DASHBOARD: ✅ Created")
        else:
            context_parts.append("DASHBOARD: ❌ None")
        
        # Recent tool calls with outcomes
        tool_calls = state.get('tool_calls', [])
        if tool_calls:
            context_parts.append("TOOL CALL HISTORY:")
            for call in tool_calls[-5:]:  # Last 5 calls
                outcome_summary = call.get('outcome', {}).get('summary', 'No outcome recorded')
                context_parts.append(f"  - Call {call['call_number']}: {call['tool']} → {outcome_summary}")
                if call.get('reasoning'):
                    context_parts.append(f"    Reasoning: {call['reasoning'][:100]}...")
            
            # Determine workflow progress
            completed_stages = set()
            for call in tool_calls:
                tool_name = call['tool']
                outcome = call.get('outcome', {})
                if outcome.get('success', False):
                    if tool_name == 'create_analysis_plan':
                        completed_stages.add('analysis_plan')
                    elif tool_name == 'generate_code':
                        completed_stages.add('code_generation')
                    elif tool_name == 'execute_code':
                        completed_stages.add('code_execution')
                    elif tool_name == 'create_dashboard':
                        completed_stages.add('dashboard')
            
            context_parts.append(f"COMPLETED STAGES: {', '.join(completed_stages) if completed_stages else 'None'}")
            
            # Check for repeated tool calls (indication of loop)
            recent_tools = [call['tool'] for call in tool_calls[-3:]]
            if len(recent_tools) >= 2 and len(set(recent_tools)) == 1:
                context_parts.append(f"⚠️ WARNING: Repeated tool calls detected ({recent_tools[0]} called {len(recent_tools)} times in a row)")
        
        return "\n".join(context_parts)

    def _fallback_tool_decision(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback decision logic when LLM fails."""
        
        # Check for infinite loops only (not workflow completion)
        if self._check_task_completion(state):
            return {
                "tool": "complete",
                "reasoning": "Breaking infinite loop detected in tool calls",
                "parameters": {},
                "complete": True,
                "confidence": 0.9
            }
        
        # Simple heuristic-based decision making
        user_query = state.get('user_query', '').lower()
        
        # Check for modification requests
        if any(keyword in user_query for keyword in ['change', 'modify', 'update', 'use', 'instead', 'different']):
            if state.get('current_code'):
                return {
                    "tool": "modify_existing_code",
                    "reasoning": "User requested modification to existing code",
                    "parameters": {"modification_request": state['user_query']},
                    "complete": False,
                    "confidence": 0.8
                }
        
        # Progressive workflow (but don't automatically complete)
        if not state.get('current_analysis_plan'):
            return {
                "tool": "create_analysis_plan",
                "reasoning": "No analysis plan exists, creating one first",
                "parameters": {},
                "complete": False,
                "confidence": 0.9
            }
        elif not state.get('current_code'):
            return {
                "tool": "generate_code",
                "reasoning": "Analysis plan exists, generating code",
                "parameters": {},
                "complete": False,
                "confidence": 0.9
            }
        elif not state.get('execution_results') or state['execution_results'].get('return_code') != 0:
            return {
                "tool": "execute_code",
                "reasoning": "Code needs to be executed or has errors",
                "parameters": {},
                "complete": False,
                "confidence": 0.9
            }
        elif not state.get('dashboard_created'):
            return {
                "tool": "create_dashboard",
                "reasoning": "Results available, creating dashboard",
                "parameters": {},
                "complete": False,
                "confidence": 0.8
            }
        else:
            # Don't automatically complete - let LLM decide
            return {
                "tool": "analyze_conversation",
                "reasoning": "Checking if user has additional requirements or if task is truly complete",
                "parameters": {},
                "complete": False,
                "confidence": 0.6
            }

    async def _execute_tool(self, tool_name: str, parameters: Dict[str, Any], 
                           state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute the specified tool with given parameters."""
        
        try:
            if tool_name == "analyze_conversation":
                async for event in self._tool_analyze_conversation(parameters, state):
                    yield event
                    
            elif tool_name == "create_analysis_plan":
                async for event in self._tool_create_analysis_plan(parameters, state):
                    yield event
                    
            elif tool_name == "refine_analysis_plan":
                async for event in self._tool_refine_analysis_plan(parameters, state):
                    yield event
                    
            elif tool_name == "generate_code":
                async for event in self._tool_generate_code(parameters, state):
                    yield event
                    
            elif tool_name == "modify_existing_code":
                async for event in self._tool_modify_existing_code(parameters, state):
                    yield event
                    
            elif tool_name == "execute_code":
                async for event in self._tool_execute_code(parameters, state):
                    yield event
                    
            elif tool_name == "create_dashboard":
                async for event in self._tool_create_dashboard(parameters, state):
                    yield event
                    
            elif tool_name == "extract_previous_work":
                async for event in self._tool_extract_previous_work(parameters, state):
                    yield event
                    
            elif tool_name == "complete" or tool_name == "none":
                state["task_complete"] = True
                completion_message = "🎉 Task completed successfully!"
                if tool_name == "none":
                    logger.info("📝 LLM signaled completion with 'none' - treating as completed")
                    completion_message = "🎉 Task completed successfully! (LLM indicated no further action needed)"
                
                yield self._create_event("agent_result", "orchestrator", {
                    "message": completion_message,
                    "status": "completed"
                })
            else:
                logger.error(f"❌ Unknown tool: {tool_name}")
                yield self._create_event("error", "orchestrator", {
                    "message": f"Unknown tool: {tool_name}",
                    "status": "error"
                })
                
        except Exception as e:
            logger.error(f"❌ Error executing tool {tool_name}: {e}")
            yield self._create_event("error", "orchestrator", {
                "message": f"Error executing {tool_name}: {str(e)}",
                "status": "error"
            })

    # =========================================================================
    # TOOL IMPLEMENTATIONS
    # =========================================================================

    async def _tool_analyze_conversation(self, parameters: Dict[str, Any], 
                                       state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Analyze conversation context and user intent."""
        
        yield self._create_event("tool_status", "analyze_conversation", {
            "message": "🧠 Analyzing conversation context and user intent...",
            "status": "analyzing"
        })
        
        try:
            # Get conversation history
            history = await self._conversation_service.get_history(
                user_id=state['user_id'], 
                session_id=state['session_id'], 
                limit=20
            )
            
            state['conversation_history'] = history
            
            # Extract any previous work references
            previous_work = self._extract_work_references(history)
            state['previous_work'] = previous_work
            
            # Analyze user intent
            intent_analysis = await self._analyze_user_intent(state['user_query'], history)
            state['user_intent'] = intent_analysis
            
            yield self._create_event("tool_result", "analyze_conversation", {
                "message": "✅ Conversation analysis complete",
                "status": "completed",
                "intent": intent_analysis.get('primary_intent', 'Unknown'),
                "context_found": len(history) > 0,
                "previous_work_found": len(previous_work) > 0
            })
            
        except Exception as e:
            logger.error(f"❌ Conversation analysis failed: {e}")
            yield self._create_event("error", "analyze_conversation", {
                "message": f"Failed to analyze conversation: {str(e)}",
                "status": "error"
            })

    async def _tool_create_analysis_plan(self, parameters: Dict[str, Any], 
                                       state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Create initial data analysis plan."""
        
        yield self._create_event("tool_status", "create_analysis_plan", {
            "message": "🧠 Creating data analysis plan...",
            "status": "planning"
        })
        
        try:
            # Fetch dataset metadata
            datasets_metadata = await self._fetch_dataset_metadata(state["datasets_loaded"])
            
            message = {
                "query": state["user_query"],
                "datasets": datasets_metadata,
                "planning_mode": "initial"
            }
            
            response = await asyncio.wait_for(
                self._data_analysis_agent.process(message),
                timeout=180.0
            )
            
            response_data = AgentResponse.from_dict(response).data
            state["current_analysis_plan"] = response_data
            
            yield self._create_event("tool_result", "create_analysis_plan", {
                "message": "✅ Analysis plan created successfully",
                "status": "completed",
                "plan_type": response_data.get("analysis_type", "Unknown")
            })
            
        except Exception as e:
            logger.error(f"❌ Analysis plan creation failed: {e}")
            yield self._create_event("error", "create_analysis_plan", {
                "message": f"Failed to create analysis plan: {str(e)}",
                "status": "error"
            })

    async def _tool_refine_analysis_plan(self, parameters: Dict[str, Any], 
                                       state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Refine existing analysis plan based on new requirements."""
        
        yield self._create_event("tool_status", "refine_analysis_plan", {
            "message": "🔄 Refining analysis plan based on new requirements...",
            "status": "refining"
        })
        
        try:
            datasets_metadata = await self._fetch_dataset_metadata(state["datasets_loaded"])
            
            message = {
                "query": state["user_query"],
                "datasets": datasets_metadata,
                "planning_mode": "refine_for_analysis",
                "previous_results": {
                    "current_plan": state.get("current_analysis_plan", {}),
                    "execution_results": state.get("execution_results", {}),
                    "refinement_request": parameters.get("refinement_request", state["user_query"])
                }
            }
            
            response = await asyncio.wait_for(
                self._data_analysis_agent.process(message),
                timeout=180.0
            )
            
            response_data = AgentResponse.from_dict(response).data
            state["current_analysis_plan"] = response_data
            
            yield self._create_event("tool_result", "refine_analysis_plan", {
                "message": "✅ Analysis plan refined successfully",
                "status": "completed",
                "plan_type": response_data.get("analysis_type", "Unknown")
            })
            
        except Exception as e:
            logger.error(f"❌ Analysis plan refinement failed: {e}")
            yield self._create_event("error", "refine_analysis_plan", {
                "message": f"Failed to refine analysis plan: {str(e)}",
                "status": "error"
            })

    async def _tool_generate_code(self, parameters: Dict[str, Any], 
                                state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Generate new code from analysis plan."""
        
        yield self._create_event("tool_status", "generate_code", {
            "message": "💻 Generating Python analysis code...",
            "status": "generating"
        })
        
        try:
            datasets_metadata = await self._fetch_dataset_metadata(state["datasets_loaded"])
            
            message = {
                "query": state["user_query"],
                "analysis_plan": state["current_analysis_plan"],
                "datasets": datasets_metadata,
                "generation_mode": "single_shot",
                "retry": 0,
                "error_feedback": {}
            }
            
            response = await asyncio.wait_for(
                self._code_generation_agent.process(message),
                timeout=300.0
            )
            
            response_data = AgentResponse.from_dict(response).data
            state["current_code"] = response_data
            
            yield self._create_event("tool_result", "generate_code", {
                "message": "✅ Code generated successfully",
                "status": "completed",
                "code_sections": list(response_data.keys()) if response_data else []
            })
            
        except Exception as e:
            logger.error(f"❌ Code generation failed: {e}")
            yield self._create_event("error", "generate_code", {
                "message": f"Failed to generate code: {str(e)}",
                "status": "error"
            })

    async def _tool_modify_existing_code(self, parameters: Dict[str, Any], 
                                       state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Modify existing code based on user requirements."""
        
        modification_request = parameters.get("modification_request", state["user_query"])
        
        yield self._create_event("tool_status", "modify_existing_code", {
            "message": f"🔧 Modifying existing code based on: {modification_request[:100]}...",
            "status": "modifying"
        })
        
        try:
            datasets_metadata = await self._fetch_dataset_metadata(state["datasets_loaded"])
            
            # Extract comprehensive error feedback from the standardized response format
            error_feedback = {}
            execution_results = state.get("execution_results")
            
            if execution_results:
                # Check the execution results data (legacy format)
                if execution_results.get("return_code") != 0:
                    error_feedback = {
                        "return_code": execution_results.get("return_code"),
                        "stderr": execution_results.get("stderr", ""),
                        "stdout": execution_results.get("stdout", "")
                    }
                
                # Also check if there's a standardized response with richer error details
                if "error_details" in execution_results:
                    error_feedback.update(execution_results["error_details"])
                    
                # Extract from the last execution tool call if available
                last_execution_call = None
                for call in reversed(state.get("tool_calls", [])):
                    if call["tool"] == "execute_code":
                        last_execution_call = call
                        break
                        
                if last_execution_call and "response" in last_execution_call:
                    response = last_execution_call["response"]
                    if response.get("error_details"):
                        error_feedback.update(response["error_details"])
                        
                logger.info(f"🔍 Error feedback extracted: {bool(error_feedback)}")
                if error_feedback:
                    logger.info(f"🐛 Error details: {error_feedback.get('stderr', 'No stderr')[:200]}...")
            
            message = {
                "query": modification_request,
                "analysis_plan": state.get("current_analysis_plan", {}),
                "datasets": datasets_metadata,
                "generation_mode": "modification",
                "existing_code": state.get("current_code", {}),
                "modification_request": modification_request,
                "error_feedback": error_feedback,
                "retry": len([call for call in state.get("tool_calls", []) if call["tool"] == "modify_existing_code"])
            }
            
            response = await asyncio.wait_for(
                self._code_generation_agent.process(message),
                timeout=300.0
            )
            
            # Extract comprehensive response data including standardized format
            full_response = response  # Store full response
            agent_response = AgentResponse.from_dict(response)
            
            # Update state with the data (legacy compatibility)
            state["current_code"] = agent_response.data
            
            # Store the comprehensive response for future tools
            state["last_code_generation_response"] = full_response
            
            # Extract key results for feedback
            key_results = full_response.get("key_results", {})
            result_summary = full_response.get("result_summary", "Code modified successfully")
            
            yield self._create_event("tool_result", "modify_existing_code", {
                "message": f"✅ {result_summary}",
                "status": "completed",
                "modification": modification_request[:100] + "..." if len(modification_request) > 100 else modification_request,
                "code_sections": key_results.get("code_sections", []),
                "generation_mode": key_results.get("generation_mode", "modification"),
                "retry_count": key_results.get("retry_count", 0)
            })
            
        except Exception as e:
            logger.error(f"❌ Code modification failed: {e}")
            yield self._create_event("error", "modify_existing_code", {
                "message": f"Failed to modify code: {str(e)}",
                "status": "error"
            })

    async def _tool_execute_code(self, parameters: Dict[str, Any], 
                               state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Execute code and get results."""
        
        yield self._create_event("tool_status", "execute_code", {
            "message": "🔄 Executing analysis code...",
            "status": "executing"
        })
        
        try:
            message = {
                "code_artifacts": state["current_code"],
                "session_id": state["session_id"],
                "user_id": state["user_id"],
                "datasets": state["datasets_loaded"],
                "output_format": "comprehensive"
            }
            
            response = await asyncio.wait_for(
                self._code_execution_agent.process(message),
                timeout=600.0
            )
            
            # Store the complete response with all standardized information
            full_response = response
            agent_response = AgentResponse.from_dict(response)
            
            # Update state with comprehensive results
            state["execution_results"] = agent_response.data  # Legacy format
            state["last_execution_response"] = full_response  # Full standardized response
            
            # Extract standardized information
            key_results = full_response.get("key_results", {})
            error_details = full_response.get("error_details", {})
            result_summary = full_response.get("result_summary", "Code execution completed")
            
            # Check if execution was successful
            execution_success = key_results.get("execution_success", False)
            return_code = key_results.get("return_code", -1)
            
            if execution_success:
                success_msg = f"✅ {result_summary}"
                
                # Extract analysis results from key_results
                if key_results.get("model_accuracy"):
                    accuracy = key_results["model_accuracy"]
                    success_msg += f" - Model achieved {accuracy:.1%} accuracy"
                elif key_results.get("has_analysis_results"):
                    success_msg += f" - Analysis completed with insights"
                
                yield self._create_event("tool_result", "execute_code", {
                    "message": success_msg,
                    "status": "completed",
                    "return_code": return_code,
                    "files_generated": key_results.get("files_generated", 0),
                    "has_analysis_results": key_results.get("has_analysis_results", False),
                    "execution_time": key_results.get("execution_time", 0)
                })
            else:
                # Execution failed - extract detailed error information
                error_msg = agent_response.error or "Unknown execution error"
                
                # Add specific error details from standardized format
                if error_details.get("stderr"):
                    error_msg = f"Code execution failed: {error_details['stderr'][:200]}..."
                
                yield self._create_event("error", "execute_code", {
                    "message": f"❌ {error_msg}",
                    "status": "error",
                    "return_code": return_code,
                    "error_details": error_details,
                    "retry_recommended": full_response.get("status") == "retry_needed"
                })
                
        except Exception as e:
            logger.error(f"❌ Code execution failed: {e}")
            yield self._create_event("error", "execute_code", {
                "message": f"Failed to execute code: {str(e)}",
                "status": "error"
            })

    async def _tool_create_dashboard(self, parameters: Dict[str, Any], 
                                   state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Create dashboard from execution results."""
        
        yield self._create_event("tool_status", "create_dashboard", {
            "message": "📊 Creating interactive dashboard...",
            "status": "creating"
        })
        
        try:
            message = {
                "session_id": state["session_id"],
                "execution_results": state["execution_results"],
                "query": state["user_query"],
                "user_id": state["user_id"]
            }
            
            response = await asyncio.wait_for(
                self._dashboard_agent.process(message),
                timeout=180.0
            )
            
            # Extract comprehensive response data
            full_response = response
            agent_response = AgentResponse.from_dict(response)
            
            state["dashboard_created"] = agent_response.data
            state["last_dashboard_response"] = full_response
            
            # Extract standardized information
            key_results = full_response.get("key_results", {})
            result_summary = full_response.get("result_summary", "Dashboard created successfully")
            
            dashboard_url = key_results.get("dashboard_url") or agent_response.data.get("dashboard_url")
            component_count = key_results.get("component_count", 0)
            
            yield self._create_event("tool_result", "create_dashboard", {
                "message": f"✅ {result_summary}",
                "status": "completed",
                "dashboard_url": dashboard_url,
                "component_count": component_count,
                "dashboard_id": key_results.get("dashboard_id")
            })
            
        except Exception as e:
            logger.error(f"❌ Dashboard creation failed: {e}")
            yield self._create_event("error", "create_dashboard", {
                "message": f"Failed to create dashboard: {str(e)}",
                "status": "error"
            })

    async def _tool_extract_previous_work(self, parameters: Dict[str, Any], 
                                        state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Extract previous analysis/code from conversation history."""
        
        yield self._create_event("tool_status", "extract_previous_work", {
            "message": "🔍 Extracting previous work from conversation...",
            "status": "extracting"
        })
        
        try:
            # This would involve analyzing conversation history for code blocks,
            # analysis plans, and results. For now, we'll implement a basic version.
            
            history = state.get('conversation_history', [])
            extracted_work = self._extract_work_references(history)
            
            # Update state with extracted work
            if extracted_work.get('analysis_plan'):
                state['current_analysis_plan'] = extracted_work['analysis_plan']
            if extracted_work.get('code'):
                state['current_code'] = extracted_work['code']
            if extracted_work.get('results'):
                state['execution_results'] = extracted_work['results']
            
            yield self._create_event("tool_result", "extract_previous_work", {
                "message": "✅ Previous work extracted from conversation",
                "status": "completed",
                "work_found": len(extracted_work) > 0
            })
            
        except Exception as e:
            logger.error(f"❌ Previous work extraction failed: {e}")
            yield self._create_event("error", "extract_previous_work", {
                "message": f"Failed to extract previous work: {str(e)}",
                "status": "error"
            })

    # =========================================================================
    # HELPER METHODS
    # =========================================================================

    async def _capture_tool_outcome(self, tool_name: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """Capture the outcome of a tool call for context building."""
        outcome = {
            "success": False,
            "summary": "Tool execution attempted",
            "details": {}
        }
        
        try:
            if tool_name == "analyze_conversation":
                outcome["success"] = state.get("user_intent") is not None
                outcome["summary"] = "Conversation analyzed" if outcome["success"] else "Analysis failed"
                
            elif tool_name == "create_analysis_plan":
                plan = state.get("current_analysis_plan")
                outcome["success"] = plan is not None
                if outcome["success"]:
                    plan_type = plan.get("analysis_type", "Unknown")
                    outcome["summary"] = f"Created {plan_type} analysis plan"
                else:
                    outcome["summary"] = "Failed to create analysis plan"
                    
            elif tool_name == "generate_code":
                code = state.get("current_code")
                outcome["success"] = code is not None and len(code) > 0
                if outcome["success"]:
                    code_sections = len(code)
                    outcome["summary"] = f"Generated code with {code_sections} sections"
                else:
                    outcome["summary"] = "Failed to generate code"
                    
            elif tool_name == "modify_existing_code":
                code = state.get("current_code")
                outcome["success"] = code is not None
                outcome["summary"] = "Code modified" if outcome["success"] else "Code modification failed"
                
            elif tool_name == "execute_code":
                execution_results = state.get("execution_results")
                last_execution_response = state.get("last_execution_response")
                
                if last_execution_response:
                    key_results = last_execution_response.get("key_results", {})
                    outcome["success"] = key_results.get("execution_success", False)
                    if outcome["success"]:
                        files_count = key_results.get("files_generated", 0)
                        outcome["summary"] = f"Code executed successfully, {files_count} files generated"
                    else:
                        outcome["summary"] = "Code execution failed"
                elif execution_results:
                    outcome["success"] = execution_results.get("return_code") == 0
                    outcome["summary"] = "Code executed successfully" if outcome["success"] else "Code execution failed"
                else:
                    outcome["summary"] = "No execution results"
                    
            elif tool_name == "create_dashboard":
                dashboard = state.get("dashboard_created")
                last_dashboard_response = state.get("last_dashboard_response")
                
                if last_dashboard_response:
                    key_results = last_dashboard_response.get("key_results", {})
                    outcome["success"] = key_results.get("dashboard_created", False)
                    if outcome["success"]:
                        component_count = key_results.get("component_count", 0)
                        outcome["summary"] = f"Dashboard created with {component_count} components"
                    else:
                        outcome["summary"] = "Dashboard creation failed"
                elif dashboard:
                    outcome["success"] = True
                    outcome["summary"] = "Dashboard created successfully"
                else:
                    outcome["summary"] = "No dashboard created"
                    
            elif tool_name == "refine_analysis_plan":
                plan = state.get("current_analysis_plan")
                outcome["success"] = plan is not None
                outcome["summary"] = "Analysis plan refined" if outcome["success"] else "Plan refinement failed"
                
            elif tool_name == "extract_previous_work":
                work = state.get("previous_work", {})
                outcome["success"] = len(work) > 0
                outcome["summary"] = f"Extracted {len(work)} work items" if outcome["success"] else "No previous work found"
                
            else:
                outcome["summary"] = f"Unknown tool: {tool_name}"
                
        except Exception as e:
            logger.error(f"Error capturing tool outcome for {tool_name}: {e}")
            outcome["summary"] = f"Error capturing outcome: {e}"
            
        return outcome

    def _check_task_completion(self, state: Dict[str, Any]) -> bool:
        """Check if the task appears to be complete based on current state - focused on loop detection only."""
        
        # Check for repeated analyze_conversation calls (loop detection)
        tool_calls = state.get("tool_calls", [])
        recent_calls = tool_calls[-3:] if len(tool_calls) >= 3 else tool_calls
        repeated_analyze = all(call["tool"] == "analyze_conversation" for call in recent_calls) and len(recent_calls) >= 2
        
        if repeated_analyze:
            logger.info("🔄 Detected repeated analyze_conversation calls - marking task as complete to break loop")
            return True
        
        # Check for any tool being repeated 3+ times in a row (general loop detection)
        if len(tool_calls) >= 3:
            recent_tools = [call['tool'] for call in tool_calls[-3:]]
            if len(set(recent_tools)) == 1:  # Same tool repeated 3 times
                logger.info(f"🔄 Detected infinite loop - {recent_tools[0]} called repeatedly - marking task as complete")
                return True
            
        return False

    def _create_event(self, event_type: str, tool: str, data: Any) -> Dict[str, Any]:
        """Create standardized event for tool execution."""
        return {
            "event_type": event_type,
            "tool_id": tool,
            "timestamp": datetime.utcnow().isoformat(),
            "data": data
        }

    def _clean_llm_response(self, raw: str) -> str:
        """Clean LLM response for JSON parsing."""
        cleaned = raw.strip()
        if cleaned.startswith("```json"):
            cleaned = cleaned[7:]
        if cleaned.endswith("```"):
            cleaned = cleaned[:-3]
        return cleaned.strip()

    async def _fetch_dataset_metadata(self, dataset_ids: List[str]) -> List[Dict[str, Any]]:
        """Fetch dataset metadata for agents."""
        datasets_metadata = []
        
        for dataset_id in dataset_ids:
            try:
                metadata = await self._dataset_catalog.get_dataset(dataset_id)
                if metadata:
                    datasets_metadata.append({
                        "dataset_id": metadata["dataset_id"],
                        "columns": metadata.get("columns", []),
                        "row_count": metadata.get("row_count", 0),
                        "file_format": metadata.get("file_format", "unknown"),
                        "s3_key": metadata.get("s3_key", ""),
                        "source": metadata.get("source", "unknown")
                    })
                else:
                    datasets_metadata.append({
                        "dataset_id": dataset_id,
                        "columns": [],
                        "row_count": 0,
                        "file_format": "unknown",
                        "s3_key": "",
                        "source": "unknown"
                    })
            except Exception as e:
                logger.error(f"❌ Error fetching metadata for dataset {dataset_id}: {e}")
                datasets_metadata.append({
                    "dataset_id": dataset_id,
                    "columns": [],
                    "row_count": 0,
                    "file_format": "unknown",
                    "s3_key": "",
                    "source": "unknown"
                })
        
        return datasets_metadata

    def _extract_work_references(self, history: List[Dict[str, str]]) -> Dict[str, Any]:
        """Extract references to previous work from conversation history."""
        # This is a simplified implementation
        # In a real system, this would parse the conversation for code blocks,
        # analysis plans, and results
        
        extracted = {}
        
        for message in history:
            if message['role'] == 'assistant':
                content = message['content']
                
                # Look for analysis plan references
                if 'analysis_type' in content and 'analysis_goal' in content:
                    # This suggests there was an analysis plan
                    pass
                
                # Look for code blocks
                if '```python' in content or 'def ' in content:
                    # This suggests there was code
                    pass
                
                # Look for results
                if 'accuracy' in content or 'executed successfully' in content:
                    # This suggests there were results
                    pass
        
        return extracted

    async def _analyze_user_intent(self, query: str, history: List[Dict[str, str]]) -> Dict[str, Any]:
        """Analyze user intent from current query and conversation history."""
        
        # Simple intent classification
        query_lower = query.lower()
        
        intent_analysis = {
            "primary_intent": "unknown",
            "is_modification": False,
            "is_new_request": True,
            "requires_code_change": False,
            "requires_new_analysis": False
        }
        
        # Check for modification keywords
        modification_keywords = ['change', 'modify', 'update', 'use', 'instead', 'different', 'replace']
        if any(keyword in query_lower for keyword in modification_keywords):
            intent_analysis["is_modification"] = True
            intent_analysis["is_new_request"] = False
            intent_analysis["requires_code_change"] = True
            intent_analysis["primary_intent"] = "modify_existing"
        
        # Check for analysis keywords
        analysis_keywords = ['analyze', 'analysis', 'insights', 'patterns', 'explore']
        if any(keyword in query_lower for keyword in analysis_keywords):
            intent_analysis["primary_intent"] = "data_analysis"
            if not intent_analysis["is_modification"]:
                intent_analysis["requires_new_analysis"] = True
        
        # Check for ML keywords
        ml_keywords = ['model', 'predict', 'classification', 'regression', 'machine learning']
        if any(keyword in query_lower for keyword in ml_keywords):
            intent_analysis["primary_intent"] = "machine_learning"
            if not intent_analysis["is_modification"]:
                intent_analysis["requires_new_analysis"] = True
        
        # Check conversation history for context
        if history and len(history) > 0:
            intent_analysis["is_new_request"] = False
        
        return intent_analysis


# Maintain backward compatibility
IntelligentReportOrchestrator = ConversationalReportOrchestrator
ReportOrchestratorAgent = ConversationalReportOrchestrator