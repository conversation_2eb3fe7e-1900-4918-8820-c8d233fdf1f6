# Core dependencies (always include these)
fastapi==0.110.0
uvicorn==0.27.1
pydantic[email]==2.6.1
python-dotenv==1.0.1
authlib==1.3.0
python-jose[cryptography]==3.3.0
passlib==1.7.4
bcrypt==4.1.2
cryptography>=41.0.0
httpx==0.27.0
python-multipart==0.0.9

# AWS services (keep these if you're using AWS)
boto3==1.34.59
aiobotocore~=2.12.3

# Data processing (large dependency - remove if not needed in production)
pandas==2.2.0
numpy==1.26.4
# openpyxl==3.1.2  # Excel support

# Phase 3: Intelligent Data Analysis & Insights dependencies
scipy==1.12.0
scikit-learn==1.4.1.post1

# Database connectors (choose ONLY the one you need for production)
sqlalchemy==2.0.25
# Choose ONE of these based on your production database:
pg8000==1.30.3  # PostgreSQL (serverless-friendly)
# pymysql==1.1.0  # MySQL
# pyodbc==5.0.1  # SQL Server
# oracledb==1.4.2  # Oracle
# pymongo==4.6.1  # MongoDB 