"""
Database Query Optimization Service

This service provides comprehensive optimization strategies for the database query pipeline,
including SQL query optimization, connection management, caching strategies, and async processing.
"""

import asyncio
import logging
import time
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict

from app.utils.performance_monitor import performance_monitor, BottleneckAnalysis
from app.services.database_service import DatabaseService

logger = logging.getLogger(__name__)


@dataclass
class OptimizationRecommendation:
    """Optimization recommendation with implementation details."""
    category: str  # "sql", "caching", "connection", "async", "indexing"
    priority: str  # "critical", "high", "medium", "low"
    title: str
    description: str
    implementation_steps: List[str]
    expected_improvement: str
    effort_level: str  # "low", "medium", "high"


@dataclass
class QueryPattern:
    """Identified query pattern for optimization."""
    pattern_type: str
    frequency: int
    avg_execution_time: float
    tables_involved: List[str]
    optimization_potential: str


class QueryOptimizationService:
    """Service for analyzing and optimizing database query performance."""
    
    def __init__(self, database_service: DatabaseService):
        """Initialize the optimization service.
        
        Args:
            database_service: Database service instance
        """
        self.database_service = database_service
        self.query_patterns: Dict[str, QueryPattern] = {}
        self.optimization_history: List[OptimizationRecommendation] = []
        
        # SQL optimization patterns
        self.sql_antipatterns = {
            "SELECT *": "Avoid SELECT * - specify only needed columns",
            "WHERE column LIKE '%value%'": "Leading wildcards prevent index usage",
            "ORDER BY RAND()": "Random ordering is expensive - consider alternatives",
            "NOT IN": "NOT IN with NULL values can cause issues - use NOT EXISTS",
            "DISTINCT": "DISTINCT can be expensive - ensure it's necessary",
        }
        
        # Index recommendations based on query patterns
        self.index_recommendations = {
            "frequent_where_clauses": "Create indexes on frequently filtered columns",
            "join_columns": "Ensure foreign key columns have indexes",
            "order_by_columns": "Consider indexes on frequently sorted columns",
            "group_by_columns": "Indexes can speed up GROUP BY operations",
        }
    
    async def analyze_query_performance(self, time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """Analyze current query performance and identify optimization opportunities.
        
        Args:
            time_window: Time window for analysis
            
        Returns:
            Comprehensive performance analysis
        """
        # Get performance metrics
        bottlenecks = performance_monitor.detect_bottlenecks(time_window)
        pipeline_analysis = performance_monitor.analyze_pipeline_stages(time_window)
        
        # Analyze SQL query patterns
        query_patterns = await self._analyze_query_patterns()
        
        # Generate optimization recommendations
        recommendations = await self._generate_optimization_recommendations(
            bottlenecks, pipeline_analysis, query_patterns
        )
        
        analysis = {
            "timestamp": datetime.utcnow().isoformat(),
            "time_window": str(time_window) if time_window else "all_time",
            "bottlenecks": [
                {
                    "type": b.bottleneck_type,
                    "severity": b.severity,
                    "metric": b.metric_name,
                    "current_value": b.current_value,
                    "threshold": b.threshold,
                    "description": b.impact_description,
                    "recommendations": b.recommendations
                }
                for b in bottlenecks
            ],
            "pipeline_stages": [
                {
                    "stage": s.stage_name,
                    "avg_duration": s.avg_duration,
                    "percentage": s.percentage_of_total,
                    "bottleneck_score": s.bottleneck_score
                }
                for s in pipeline_analysis
            ],
            "query_patterns": [
                {
                    "type": p.pattern_type,
                    "frequency": p.frequency,
                    "avg_time": p.avg_execution_time,
                    "tables": p.tables_involved,
                    "optimization_potential": p.optimization_potential
                }
                for p in query_patterns
            ],
            "optimization_recommendations": [
                {
                    "category": r.category,
                    "priority": r.priority,
                    "title": r.title,
                    "description": r.description,
                    "steps": r.implementation_steps,
                    "expected_improvement": r.expected_improvement,
                    "effort": r.effort_level
                }
                for r in recommendations
            ]
        }
        
        return analysis
    
    async def _analyze_query_patterns(self) -> List[QueryPattern]:
        """Analyze SQL query patterns from performance metrics."""
        patterns = []
        
        # Get SQL execution metrics
        sql_metrics = performance_monitor.get_all_metrics().get("sql_execution", [])
        
        if not sql_metrics:
            return patterns
        
        # Group by database and analyze patterns
        db_patterns = defaultdict(list)
        for metric in sql_metrics:
            db_id = metric.metadata.get("database_id", "unknown")
            db_patterns[db_id].append(metric)
        
        for db_id, metrics in db_patterns.items():
            if len(metrics) < 5:  # Need sufficient data
                continue
                
            avg_time = sum(m.value for m in metrics) / len(metrics)
            
            # Determine optimization potential
            if avg_time > 10.0:
                potential = "high"
            elif avg_time > 5.0:
                potential = "medium"
            else:
                potential = "low"
            
            pattern = QueryPattern(
                pattern_type=f"database_{db_id}_queries",
                frequency=len(metrics),
                avg_execution_time=avg_time,
                tables_involved=[],  # Would need query analysis to populate
                optimization_potential=potential
            )
            patterns.append(pattern)
        
        return patterns
    
    async def _generate_optimization_recommendations(
        self,
        bottlenecks: List[BottleneckAnalysis],
        pipeline_analysis: List[Any],
        query_patterns: List[QueryPattern]
    ) -> List[OptimizationRecommendation]:
        """Generate specific optimization recommendations."""
        recommendations = []
        
        # SQL Query Optimization Recommendations
        for pattern in query_patterns:
            if pattern.optimization_potential == "high":
                rec = OptimizationRecommendation(
                    category="sql",
                    priority="high",
                    title=f"Optimize {pattern.pattern_type} queries",
                    description=f"Queries averaging {pattern.avg_execution_time:.2f}s need optimization",
                    implementation_steps=[
                        "Analyze slow query logs",
                        "Review query execution plans",
                        "Add appropriate indexes",
                        "Consider query rewriting"
                    ],
                    expected_improvement="50-80% reduction in query time",
                    effort_level="medium"
                )
                recommendations.append(rec)
        
        # Connection Pool Optimization
        connection_bottlenecks = [b for b in bottlenecks if "connection" in b.metric_name]
        if connection_bottlenecks:
            rec = OptimizationRecommendation(
                category="connection",
                priority="high",
                title="Optimize database connection pooling",
                description="Connection establishment is taking too long",
                implementation_steps=[
                    "Increase connection pool size",
                    "Implement connection pre-warming",
                    "Add connection health checks",
                    "Consider connection multiplexing"
                ],
                expected_improvement="30-50% reduction in connection time",
                effort_level="low"
            )
            recommendations.append(rec)
        
        # Caching Recommendations
        if any("sql_generation" in b.metric_name for b in bottlenecks):
            rec = OptimizationRecommendation(
                category="caching",
                priority="medium",
                title="Implement advanced SQL query caching",
                description="SQL generation is slow and could benefit from caching",
                implementation_steps=[
                    "Implement semantic query caching",
                    "Cache query templates by pattern",
                    "Add result set caching for common queries",
                    "Implement cache invalidation strategies"
                ],
                expected_improvement="60-90% reduction in generation time for cached queries",
                effort_level="medium"
            )
            recommendations.append(rec)
        
        # Async Processing Recommendations
        slow_stages = [s for s in pipeline_analysis if s.avg_duration > 10.0]
        if slow_stages:
            rec = OptimizationRecommendation(
                category="async",
                priority="medium",
                title="Implement parallel processing for pipeline stages",
                description="Some pipeline stages are slow and could be parallelized",
                implementation_steps=[
                    "Identify independent operations",
                    "Implement async/await patterns",
                    "Add concurrent processing where possible",
                    "Optimize task scheduling"
                ],
                expected_improvement="20-40% reduction in total pipeline time",
                effort_level="high"
            )
            recommendations.append(rec)
        
        # Memory Management Recommendations
        if any(b.bottleneck_type == "inconsistent_performance" for b in bottlenecks):
            rec = OptimizationRecommendation(
                category="memory",
                priority="medium",
                title="Optimize memory management and garbage collection",
                description="Inconsistent performance suggests memory management issues",
                implementation_steps=[
                    "Implement object pooling for frequently created objects",
                    "Add memory usage monitoring",
                    "Optimize data structure usage",
                    "Implement proper cleanup in async operations"
                ],
                expected_improvement="More consistent performance, reduced latency spikes",
                effort_level="medium"
            )
            recommendations.append(rec)
        
        return sorted(recommendations, key=lambda x: self._priority_score(x.priority), reverse=True)
    
    def _priority_score(self, priority: str) -> int:
        """Convert priority to numeric score for sorting."""
        scores = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        return scores.get(priority, 0)
    
    async def generate_optimization_report(self, time_window: Optional[timedelta] = None) -> str:
        """Generate a comprehensive optimization report.
        
        Args:
            time_window: Time window for analysis
            
        Returns:
            Formatted optimization report
        """
        analysis = await self.analyze_query_performance(time_window)
        
        report = []
        report.append("# Database Query Pipeline Optimization Report")
        report.append(f"Generated: {analysis['timestamp']}")
        report.append(f"Analysis Period: {analysis['time_window']}")
        report.append("")
        
        # Executive Summary
        report.append("## Executive Summary")
        bottleneck_count = len(analysis['bottlenecks'])
        critical_count = len([b for b in analysis['bottlenecks'] if b['severity'] == 'critical'])
        
        if critical_count > 0:
            report.append(f"⚠️  **{critical_count} critical bottlenecks** detected requiring immediate attention")
        elif bottleneck_count > 0:
            report.append(f"📊 {bottleneck_count} performance bottlenecks identified with optimization opportunities")
        else:
            report.append("✅ No significant performance bottlenecks detected")
        
        report.append("")
        
        # Bottleneck Analysis
        if analysis['bottlenecks']:
            report.append("## Performance Bottlenecks")
            for bottleneck in analysis['bottlenecks']:
                severity_emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
                emoji = severity_emoji.get(bottleneck['severity'], "⚪")
                
                report.append(f"### {emoji} {bottleneck['metric']} ({bottleneck['severity'].upper()})")
                report.append(f"**Issue:** {bottleneck['description']}")
                report.append("**Recommendations:**")
                for rec in bottleneck['recommendations'][:3]:  # Top 3 recommendations
                    report.append(f"- {rec}")
                report.append("")
        
        # Pipeline Stage Analysis
        if analysis['pipeline_stages']:
            report.append("## Pipeline Stage Performance")
            for stage in analysis['pipeline_stages']:
                bottleneck_indicator = "🔴" if stage['bottleneck_score'] > 70 else "🟡" if stage['bottleneck_score'] > 40 else "🟢"
                report.append(f"- **{stage['stage']}**: {stage['avg_duration']:.2f}s ({stage['percentage']:.1f}% of total) {bottleneck_indicator}")
            report.append("")
        
        # Optimization Recommendations
        if analysis['optimization_recommendations']:
            report.append("## Optimization Recommendations")
            for rec in analysis['optimization_recommendations']:
                priority_emoji = {"critical": "🔴", "high": "🟠", "medium": "🟡", "low": "🟢"}
                emoji = priority_emoji.get(rec['priority'], "⚪")
                
                report.append(f"### {emoji} {rec['title']} ({rec['priority'].upper()} Priority)")
                report.append(f"**Category:** {rec['category'].title()}")
                report.append(f"**Description:** {rec['description']}")
                report.append(f"**Expected Improvement:** {rec['expected_improvement']}")
                report.append(f"**Effort Level:** {rec['effort'].title()}")
                report.append("**Implementation Steps:**")
                for step in rec['steps']:
                    report.append(f"1. {step}")
                report.append("")
        
        return "\n".join(report)


# Global optimization service instance
query_optimization_service = None

def get_query_optimization_service(database_service: DatabaseService) -> QueryOptimizationService:
    """Get or create the global query optimization service instance."""
    global query_optimization_service
    if query_optimization_service is None:
        query_optimization_service = QueryOptimizationService(database_service)
    return query_optimization_service
