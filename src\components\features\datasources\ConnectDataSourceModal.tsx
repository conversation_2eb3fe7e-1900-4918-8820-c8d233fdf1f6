"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { DatabaseConnectionRequestParams } from '@/providers/ApiContext';
import { AlertTriangle, Loader2, X } from 'lucide-react';

// Types
interface DataSourceField {
  label: string;
  name: keyof DatabaseConnectionRequestParams;
  type: 'text' | 'number' | 'password' | 'checkbox';
  required?: boolean;
  placeholder?: string;
}

interface DataSourceType {
  id: string;
  name: string;
  connectionFields: DataSourceField[];
}

interface ConnectDataSourceModalProps {
  isOpen: boolean;
  onClose: () => void;
  dataSourceType: DataSourceType | null;
  onConnect: (params: DatabaseConnectionRequestParams) => Promise<boolean>;
  apiError?: string | null; 
}

// Field components optimized for performance
const CheckboxField = React.memo(({ 
  field, 
  value, 
  onChange, 
  disabled 
}: { 
  field: DataSourceField; 
  value: boolean; 
  onChange: (name: string, value: string | number | boolean) => void; 
  disabled: boolean 
}) => {
  return (
    <div className="flex items-center space-x-3 py-2">
      <Checkbox
        id={field.name as string}
        name={field.name as string}
        checked={value}
        onCheckedChange={(checked) => {
          onChange(field.name as string, checked === true);
        }}
        disabled={disabled}
        className="border-sidebar-border data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
      />
      <label
        htmlFor={field.name as string}
        className="text-sm cursor-pointer"
        style={{ color: 'var(--sidebar-text-primary)' }}
      >
        {field.label}
      </label>
    </div>
  );
});

CheckboxField.displayName = 'CheckboxField';

const InputFieldBasic = React.memo(({ 
  field, 
  value, 
  onChange, 
  disabled 
}: { 
  field: DataSourceField; 
  value: string | number; 
  onChange: (name: string, value: string | number | boolean) => void; 
  disabled: boolean 
}) => {
  return (
    <Input
      id={field.name as string}
      name={field.name as string}
      type={field.type}
      placeholder={field.placeholder}
      value={String(value || '')}
      onChange={(e) => onChange(field.name as string, e.target.value)}
      disabled={disabled}
      className="border-sidebar-border transition-all duration-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 h-9 text-sm"
      style={{
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        color: 'var(--sidebar-text-primary)',
      }}
    />
  );
});

InputFieldBasic.displayName = 'InputFieldBasic';

const ConnectDataSourceModal = React.memo(function ConnectDataSourceModal({
  isOpen,
  onClose,
  dataSourceType,
  onConnect,
  apiError: parentApiError
}: ConnectDataSourceModalProps) {
  // Modal state
  const [formData, setFormData] = useState<Partial<DatabaseConnectionRequestParams>>({});
  const [isConnecting, setIsConnecting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [currentApiError, setCurrentApiError] = useState<string | null>(null);

  // Handle field changes without re-rendering the entire form
  const handleFieldChange = useCallback((name: string, value: string | number | boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear errors when user types
    setValidationError(null);
    setCurrentApiError(null);
  }, []);

  // Initialize form data when modal opens or dataSourceType changes
  useEffect(() => {
    if (dataSourceType && isOpen) {
      const initialData: Partial<DatabaseConnectionRequestParams> = {};
      dataSourceType.connectionFields.forEach(field => {
        if (field.type === 'checkbox') {
          if (field.name === 'ssl_enabled') {
            (initialData as Record<string, boolean | string | number | undefined>)[field.name] = false;
          }
        } else {
          initialData[field.name] = undefined;
        }
      });
      setFormData(initialData);
      setValidationError(null);
      setCurrentApiError(null);
    }
  }, [dataSourceType?.id, dataSourceType, isOpen]);

  // Update API error separately
  useEffect(() => {
    setCurrentApiError(parentApiError || null);
  }, [parentApiError]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!dataSourceType) return;

    // Validation
    const missingFields: string[] = [];
    for (const field of dataSourceType.connectionFields) {
      if (field.required && (formData[field.name] === undefined || formData[field.name] === '')) {
        missingFields.push(field.label);
      }
    }
    
    if (missingFields.length > 0) {
      if (missingFields.length === 1) {
        setValidationError(`Please fill in the ${missingFields[0]} field.`);
      } else {
        setValidationError(`Please fill in the following required fields: ${missingFields.join(', ')}.`);
      }
        return;
    }

    setValidationError(null);
    setIsConnecting(true);
    setCurrentApiError(null);

    // Prepare submission data - clean up undefined values
    const paramsToSubmit: Record<string, string | number | boolean> = {};

    dataSourceType.connectionFields.forEach(field => {
      const value = formData[field.name];
      if (value !== undefined && value !== '') {
        if (field.name === 'port') {
          paramsToSubmit.port = Number(value);
        } else if (field.name === 'ssl_enabled') {
          paramsToSubmit.ssl_enabled = Boolean(value);
        } else {
          paramsToSubmit[field.name] = value;
        }
      }
    });
    
    // Ensure required fields are set
    paramsToSubmit.name = formData.name || dataSourceType.name;
    paramsToSubmit.type = dataSourceType.id.toUpperCase(); // Ensure uppercase for backend
    
    // Set default values for optional fields if not provided
    if (dataSourceType.id === 'postgresql' || dataSourceType.id === 'mysql') {
      if (!paramsToSubmit.ssl_enabled) {
        paramsToSubmit.ssl_enabled = false;
      }
    }

    console.log('ConnectDataSourceModal: Submitting params:', paramsToSubmit);
    
    try {
      const success = await onConnect(paramsToSubmit as unknown as DatabaseConnectionRequestParams);
      if (success) {
        onClose();
      }
    } catch (error) {
      console.error('Connection error:', error);
    } finally {
      setIsConnecting(false);
    }
  }, [dataSourceType, formData, onConnect, onClose]);

  // Prevent scrolling of body when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Close on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen && !isConnecting) {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscape);
    return () => window.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose, isConnecting]);

  if (!isOpen) return null;
  
  // Modern modal design inspired by Linear/Stripe/Vercel
  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-6"
      style={{ backgroundColor: 'transparent' }}
      onClick={() => !isConnecting && onClose()}
    >
      <div 
        className="w-full max-w-md flex flex-col max-h-[85vh] overflow-hidden transform-gpu border border-sidebar-border rounded-2xl shadow-2xl"
        style={{
          backgroundColor: '#323232',
          willChange: 'transform'
        }}
        onClick={e => e.stopPropagation()}
      >
        {/* Close Button */}
        <div className="absolute top-4 right-4 z-10">
          <Button 
            type="button" 
            variant="ghost"
            size="icon"
            onClick={() => !isConnecting && onClose()}
            disabled={isConnecting}
            className="w-8 h-8 rounded-lg border-0 transition-all duration-200"
            style={{
              color: 'var(--sidebar-text-secondary)',
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              if (!isConnecting) {
                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                e.currentTarget.style.setProperty('color', 'var(--sidebar-text-primary)', 'important');
              }
            }}
            onMouseLeave={(e) => {
              if (!isConnecting) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');
              }
            }}
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
        
        <form onSubmit={handleSubmit} className="flex flex-col flex-grow overflow-hidden">
          {/* Content */}
          <div 
            className="flex-grow p-6 space-y-4 overflow-y-auto" 
            style={{ 
              overscrollBehavior: 'contain',
              WebkitOverflowScrolling: 'touch' 
            }}
          >
            {dataSourceType?.connectionFields.map((field) => {
              // Intelligent field grouping and layout
              const isShortField = ['port'].includes(field.name as string);
              
              return (
                <div key={field.name} className="space-y-2">
                <Label 
                  htmlFor={field.name as string}
                    className="text-sm font-medium"
                    style={{ color: 'var(--sidebar-text-primary)' }}
                >
                    {field.label}
                </Label>
                
                {field.type === 'checkbox' ? (
                    <div className="pt-1">
                  <CheckboxField
                    field={field}
                    value={Boolean(formData[field.name])}
                    onChange={handleFieldChange}
                    disabled={isConnecting}
                  />
                    </div>
                ) : (
                    <div className={`${isShortField ? 'max-w-[140px]' : ''}`}>
                  <InputFieldBasic
                    field={field}
                    value={typeof formData[field.name] === 'boolean' ? '' : String(formData[field.name] || '')}
                    onChange={handleFieldChange}
                    disabled={isConnecting}
                  />
                    </div>
                )}
              </div>
              );
            })}
            
            {/* Error messages */}
            {(validationError || currentApiError) && (
              <div className="p-3 border border-red-200 rounded-lg mt-4" style={{ backgroundColor: 'rgba(239, 68, 68, 0.05)' }}>
                <div className="flex items-start gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500 flex-shrink-0 mt-0.5" />
                  <div className="text-red-600 text-sm">
                    {validationError && (
                        <div>{validationError}</div>
                    )}
                    {currentApiError && (
                        <div>{currentApiError}</div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Footer - Optimized for user flow */}
          <div className="p-6 pt-4">
            <div className="flex gap-3">
            <Button 
              type="button" 
              variant="outline" 
                onClick={() => !isConnecting && onClose()}
              disabled={isConnecting}
                className="flex-1 border border-sidebar-border rounded-lg transition-all duration-200 h-9"
                style={{
                  color: 'var(--sidebar-text-secondary)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isConnecting) {
                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                    e.currentTarget.style.setProperty('color', 'var(--sidebar-text-primary)', 'important');
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isConnecting) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');
                  }
                }}
              >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isConnecting}
                variant="outline"
                className="flex-1 border border-sidebar-border rounded-lg transition-all duration-200 h-9 font-medium"
                style={{
                  color: 'var(--sidebar-text-secondary)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (!isConnecting) {
                    e.currentTarget.style.backgroundColor = 'var(--sidebar-text-primary)';
                    e.currentTarget.style.setProperty('color', 'var(--sidebar-bg)', 'important');
                    e.currentTarget.style.transform = 'scale(1.02)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isConnecting) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');
                    e.currentTarget.style.transform = 'scale(1)';
                    e.currentTarget.style.boxShadow = 'none';
                  }
                }}
            >
              {isConnecting ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Connecting...
                </>
              ) : (
                'Connect'
              )}
            </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
});

ConnectDataSourceModal.displayName = 'ConnectDataSourceModal';

export default ConnectDataSourceModal;