// Onboarding state management utilities

import { OnboardingProgress, OnboardingState, OnboardingStep } from '@/types/auth';

const ONBOARDING_STORAGE_KEY = 'onboarding_progress';

/**
 * Save onboarding progress to localStorage (as backup)
 */
export function saveOnboardingProgress(progress: OnboardingProgress): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.setItem(ONBOARDING_STORAGE_KEY, JSON.stringify(progress));
  } catch (error) {
    console.warn('Failed to save onboarding progress to localStorage:', error);
  }
}

/**
 * Load onboarding progress from localStorage
 */
export function loadOnboardingProgress(): OnboardingProgress | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const stored = localStorage.getItem(ONBOARDING_STORAGE_KEY);
    if (!stored) return null;
    
    const progress = JSON.parse(stored) as OnboardingProgress;
    
    // Validate the structure
    if (!progress.currentStep || !Array.isArray(progress.completedSteps)) {
      return null;
    }
    
    return progress;
  } catch (error) {
    console.warn('Failed to load onboarding progress from localStorage:', error);
    return null;
  }
}

/**
 * Clear onboarding progress from localStorage
 */
export function clearOnboardingProgress(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(ONBOARDING_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear onboarding progress from localStorage:', error);
  }
}

/**
 * Calculate onboarding progress from steps
 */
export function calculateProgress(steps: OnboardingStep[], currentStepIndex: number): OnboardingProgress {
  const completedSteps = steps
    .slice(0, currentStepIndex)
    .filter(step => step.isCompleted)
    .map(step => step.id);
  
  return {
    currentStep: steps[currentStepIndex]?.id || 'welcome',
    completedSteps,
    lastUpdated: new Date().toISOString(),
    canResume: completedSteps.length > 0,
    totalSteps: steps.length,
  };
}

/**
 * Find step index from step ID
 */
export function findStepIndex(steps: OnboardingStep[], stepId: string): number {
  const index = steps.findIndex(step => step.id === stepId);
  return index >= 0 ? index : 0;
}

/**
 * Validate onboarding progress data
 */
export function validateOnboardingProgress(progress: any): progress is OnboardingProgress {
  if (!progress || typeof progress !== 'object') {
    return false;
  }
  
  return (
    typeof progress.currentStep === 'string' &&
    Array.isArray(progress.completedSteps) &&
    typeof progress.lastUpdated === 'string' &&
    typeof progress.canResume === 'boolean' &&
    typeof progress.totalSteps === 'number'
  );
}

/**
 * Merge local progress with server progress (server takes precedence)
 */
export function mergeOnboardingProgress(
  localProgress: OnboardingProgress | null,
  serverProgress: OnboardingProgress | null
): OnboardingProgress | null {
  // Server progress always takes precedence
  if (serverProgress && validateOnboardingProgress(serverProgress)) {
    return serverProgress;
  }
  
  // Fall back to local progress if server doesn't have any
  if (localProgress && validateOnboardingProgress(localProgress)) {
    return localProgress;
  }
  
  return null;
}

/**
 * Create initial onboarding state
 */
export function createInitialOnboardingState(isNewUser: boolean): OnboardingState {
  return {
    isNewUser,
    progress: undefined,
    isCompleting: false,
    hasError: false,
    errorMessage: undefined,
    canRetry: true,
  };
}

/**
 * Update onboarding state with progress
 */
export function updateOnboardingState(
  currentState: OnboardingState,
  updates: Partial<OnboardingState>
): OnboardingState {
  return {
    ...currentState,
    ...updates,
    // Always update lastUpdated when progress changes
    progress: updates.progress ? {
      ...updates.progress,
      lastUpdated: new Date().toISOString(),
    } : currentState.progress,
  };
}

/**
 * Check if user can resume onboarding
 */
export function canResumeOnboarding(progress: OnboardingProgress | null): boolean {
  if (!progress) return false;
  
  return progress.canResume && progress.completedSteps.length > 0;
}

/**
 * Get next step in onboarding flow
 */
export function getNextStep(steps: OnboardingStep[], currentStepId: string): OnboardingStep | null {
  const currentIndex = findStepIndex(steps, currentStepId);
  const nextIndex = currentIndex + 1;
  
  return nextIndex < steps.length ? steps[nextIndex] : null;
}

/**
 * Get previous step in onboarding flow
 */
export function getPreviousStep(steps: OnboardingStep[], currentStepId: string): OnboardingStep | null {
  const currentIndex = findStepIndex(steps, currentStepId);
  const previousIndex = currentIndex - 1;
  
  return previousIndex >= 0 ? steps[previousIndex] : null;
}

/**
 * Check if onboarding is complete
 */
export function isOnboardingComplete(steps: OnboardingStep[]): boolean {
  const requiredSteps = steps.filter(step => !step.isOptional);
  return requiredSteps.every(step => step.isCompleted);
}

/**
 * Get completion percentage
 */
export function getCompletionPercentage(steps: OnboardingStep[]): number {
  const totalSteps = steps.length;
  const completedSteps = steps.filter(step => step.isCompleted).length;
  
  return totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
}

/**
 * Reset onboarding state
 */
export function resetOnboardingState(steps: OnboardingStep[]): OnboardingStep[] {
  return steps.map(step => ({
    ...step,
    isCompleted: false,
  }));
}

/**
 * Handle onboarding interruption (user leaves mid-flow)
 */
export function handleOnboardingInterruption(
  steps: OnboardingStep[],
  currentStepIndex: number
): OnboardingProgress {
  const progress = calculateProgress(steps, currentStepIndex);
  
  // Save to localStorage as backup
  saveOnboardingProgress(progress);
  
  return progress;
}

/**
 * Restore onboarding state from interruption
 */
export function restoreOnboardingState(
  steps: OnboardingStep[],
  progress: OnboardingProgress | null
): { steps: OnboardingStep[]; currentStepIndex: number } {
  if (!progress || !validateOnboardingProgress(progress)) {
    return { steps, currentStepIndex: 0 };
  }
  
  // Mark completed steps
  const restoredSteps = steps.map(step => ({
    ...step,
    isCompleted: progress.completedSteps.includes(step.id),
  }));
  
  // Find current step index
  const currentStepIndex = findStepIndex(restoredSteps, progress.currentStep);
  
  return { steps: restoredSteps, currentStepIndex };
}

/**
 * Validate onboarding completion requirements
 */
export function validateOnboardingCompletion(steps: OnboardingStep[]): {
  canComplete: boolean;
  missingSteps: string[];
  errors: string[];
} {
  const errors: string[] = [];
  const missingSteps: string[] = [];

  // Check required steps (excluding completion step itself)
  const completionStepIds = ['completion', 'complete', 'finish', 'final'];
  const requiredSteps = steps.filter(step =>
    !step.isOptional &&
    !completionStepIds.includes(step.id.toLowerCase())
  );

  console.log('🔍 Validating completion requirements:');
  console.log('All steps:', steps.map(s => ({ id: s.id, title: s.title, completed: s.isCompleted, optional: s.isOptional })));
  console.log('Required steps:', requiredSteps.map(s => ({ id: s.id, title: s.title, completed: s.isCompleted })));

  for (const step of requiredSteps) {
    if (!step.isCompleted) {
      missingSteps.push(step.title);
    }
  }

  console.log('Missing steps:', missingSteps);

  if (missingSteps.length > 0) {
    errors.push(`Please complete the following required steps: ${missingSteps.join(', ')}`);
  }

  const result = {
    canComplete: errors.length === 0,
    missingSteps,
    errors,
  };

  console.log('Validation result:', result);
  return result;
}
