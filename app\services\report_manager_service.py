"""
Report-Manager Service
––––––––––––––––––––––
Coordinates the full report pipeline and supports on-the-fly user
file uploads supplied with the /report request.
"""

from __future__ import annotations

import logging, uuid, asyncio, json
from typing import Dict, List, Any, Optional, AsyncGenerator
from datetime import datetime
from decimal import Decimal

from fastapi import HTTPException, status, UploadFile
from fastapi.responses import StreamingResponse

from app.services.conversation_service import ConversationService
from app.services.file_ingestion_service import FileIngestionService
from app.agents.report_orchestrator_agent import ConversationalReportOrchestrator

logger = logging.getLogger(__name__)


class ReportManagerService:
    def __init__(self) -> None:
        self.conversation_service = ConversationService()
        self.file_ingestor = FileIngestionService()
        self._orch_cache: dict[str, ConversationalReportOrchestrator] = {}

    def _convert_decimals(self, obj: Any) -> Any:
        """Convert Decimal objects to regular Python numbers for JSON serialization."""
        if isinstance(obj, Decimal):
            if obj == int(obj):
                return int(obj)
            else:
                return float(obj)
        elif isinstance(obj, dict):
            return {key: self._convert_decimals(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_decimals(item) for item in obj]
        else:
            return obj

    # ────────────────────────────────────────────────────────────
    # main streaming entry point
    # ────────────────────────────────────────────────────────────
    async def stream_reports(
        self,
        *,
        query: str,
        output_format: str,
        user_id: str,
        session_id: str | None,
        target_datasets: Optional[str],
    ) -> AsyncGenerator[str, None]:
        """
        Streaming version that yields Server-Sent Events (SSE) formatted strings.
        
        Works only with explicitly provided datasets - no automatic dataset discovery.
        Requires target_datasets to work with (including any uploaded files converted to dataset IDs).
        """
        # Validate that we have target datasets (handle both None and empty string)
        if not target_datasets or not target_datasets.strip():
            error_event = {
                "type": "error",
                "agent": "system",
                "data": {"message": "❌ No datasets provided. Please either upload a file or specify target_datasets. Use multipart/form-data format for file uploads.", "status": "failed"},
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
            error_event = self._convert_decimals(error_event)
            yield f"data: {json.dumps(error_event)}\n\n"
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No datasets provided. Please either upload a file or specify target_datasets. Use multipart/form-data format for file uploads.",
            )
        
        def create_event(event_type: str, agent: str, data: Any, step: Optional[int] = None) -> str:
            event = {
                "type": event_type,
                "agent": agent,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z",
                "step": step
            }
            # Convert any Decimal objects before JSON serialization
            event = self._convert_decimals(event)
            return f"data: {json.dumps(event)}\n\n"
        
        # Process target_datasets from comma-separated string to list
        target_datasets_list: List[str] = [s.strip() for s in target_datasets.split(",")]
        
        logger.info(f"🎯 Processing {len(target_datasets_list)} datasets: {target_datasets_list}")
        
        try:
            
            # 1 ▸ session bookkeeping
            history = []
            if not session_id:
                session_id = f"report_sess_{uuid.uuid4().hex[:8]}"
                logger.info("Started new report chat %s for user %s", session_id, user_id)
            else:
                history = await self.conversation_service.get_history(user_id, session_id, limit=20)

            # 3 ▸ orchestrate with streaming
            yield create_event("agent_status", "orchestrator", {"message": "🎯 Starting analysis orchestration...", "status": "starting"})
            
            try:
                orch = await self._get_orchestrator(user_id)
                orch_msg = {
                    "query": query,
                    "output_format": output_format,
                    "session_id": session_id,
                    "datasets": target_datasets_list,  # Fix field name to match orchestrator expectation
                    "conversation_history": history,
                }
                
                # Stream orchestrator events and format as SSE
                async for event in orch.process(orch_msg):
                    # Convert any Decimal objects before JSON serialization
                    event = self._convert_decimals(event)
                    yield f"data: {json.dumps(event)}\n\n"
                
                # Save conversation after completion
                # Extract final result from the last event (which should be the completion event)
                # For conversation saving, we'll use a simple summary message
                assistant_msg = "Report completed with streaming updates"
                await self.conversation_service.append_pair(
                    user_id=user_id,
                    session_id=session_id,
                    user_msg=query,
                    assistant_msg=assistant_msg,
                )
                
            except HTTPException:
                raise
            except Exception as exc:
                logger.exception("Report orchestrator failed")
                yield create_event("error", "orchestrator", {"message": f"❌ Report pipeline error: {exc}", "status": "failed"})
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Report pipeline error: {exc}",
                ) from exc

        except Exception as e:
            # Send error event in SSE format
            error_event = {
                "type": "error",
                "agent": "system",
                "data": {"message": f"❌ Unexpected error: {str(e)}", "status": "failed"},
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
            # Convert any Decimal objects before JSON serialization
            error_event = self._convert_decimals(error_event)
            yield f"data: {json.dumps(error_event)}\n\n"
            raise

    # ────────────────────────────────────────────────────────────
    # helpers
    # ────────────────────────────────────────────────────────────
    async def _get_orchestrator(self, user_id: str) -> ConversationalReportOrchestrator:
        if user_id in self._orch_cache:
            return self._orch_cache[user_id]
        orch = ConversationalReportOrchestrator(user_id=user_id)
        await orch.initialize()
        self._orch_cache[user_id] = orch
        return orch

    async def clear_user_cache(self, user_id: str) -> None:
        self._orch_cache.pop(user_id, None)
        logger.info("Cleared report-orchestrator cache for user %s", user_id)

    # ────────────────────────────────────────────────────────────
    # main entry point for controller
    # ────────────────────────────────────────────────────────────
    async def handle_report_request(
        self,
        *,
        query: str,
        output_format: str,
        user_id: str,
        session_id: str | None,
        target_datasets: Optional[str],
        file: Optional[UploadFile],
    ) -> StreamingResponse:
        """
        Main entry point for report generation that handles file processing
        and returns a StreamingResponse. Keeps controller logic minimal.
        """
        # Process file upload and add to target_datasets if provided
        if file:
            try:
                # Process file immediately while request context is still active
                meta = await self.file_ingestor.ingest_file(upload=file, user_id=user_id)
                uploaded_dataset_id = meta["dataset_id"]
                
                # Add uploaded dataset to target_datasets (treat uploaded files as regular datasets)
                # Handle both None and empty string cases
                if target_datasets and target_datasets.strip():
                    target_datasets = f"{target_datasets},{uploaded_dataset_id}"
                else:
                    target_datasets = uploaded_dataset_id
                
                logger.info(f"📁 File uploaded successfully as dataset {uploaded_dataset_id}")
                logger.info(f"🎯 Updated target_datasets: '{target_datasets}'")
                
                # Clear orchestrator cache so new dataset is visible
                await self.clear_user_cache(user_id)
            except Exception as exc:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"File ingestion failed: {exc}",
                ) from exc
        
        return StreamingResponse(
            self.stream_reports(
                query=query,
                output_format=output_format,
                user_id=user_id,
                session_id=session_id,
                target_datasets=target_datasets,
            ),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
            }
        )