"""
SQL Agent
---------

• Generates SQL with Bedrock
• Executes via DatabaseService
• Streams result file to S3
• Returns a pre-signed URL in `file_path`
"""

from __future__ import annotations

import io
import logging
from typing import Any, Dict, List, Optional, Union

import pandas as pd

from app.agents.base import Agent, AgentResponse
from app.services.database_service import DatabaseService
from app.services.report_storage_service import ReportStorageService
from app.utils.bedrock_client import BedrockClient
from app.prompts.sql import get_sql_generation_prompt
from app.services.intelligent_cache_service import intelligent_cache_service
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

INLINE_PREVIEW_MAX_BYTES = 1 * 1024 * 1024   # 1 MB
MAX_PREVIEW_ROWS         = 200


class SQLAgent(Agent):
    """Generates SQL, executes it, uploads the result to S3."""

    def __init__(
        self,
        database_service: DatabaseService,
        *,
        report_store: ReportStorageService | None = None,
        agent_id: Optional[str] = None,
    ):
        self.database_service   = database_service
        self.report_store       = report_store or ReportStorageService()
        self.agent_id           = agent_id or "sql_agent"
        self.bedrock_client     = BedrockClient()
        self.initialized        = False

        # Performance optimization: Add SQL query caching
        self.sql_cache: Dict[str, str] = {}
        self.cache_ttl: Dict[str, float] = {}
        self.CACHE_DURATION = 600  # 10 minutes for SQL queries

    async def initialize(self) -> None:
        """Initialize the agent with any required setup."""
        # No async initialization needed for SQL agent
        # All required services are injected via constructor
        self.initialized = True

    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Expected payload
        ----------------
        {
          "query"          : "<NL question>",
          "database_infos" : [ … ],
          "output_format"  : "csv" | "excel",
          "user_id"        : "u_123",
          "session_id"     : "sess_a1b2"
        }
        """
        async with performance_monitor.timer("sql_agent_total"):
            user_query     = message.get("query", "").strip()
            db_infos       = message.get("database_infos", [])
            output_format  = (message.get("output_format") or "csv").lower()
            user_id        = message["user_id"]
            session_id     = message["session_id"]

            if not user_query:
                return AgentResponse(self.agent_id, False, error="No query").to_dict()
            if not db_infos:
                return AgentResponse(self.agent_id, False, error="No database infos").to_dict()

            performance_monitor.increment_counter("sql_agent_requests")
            results: List[Dict[str, Any]] = []

        for db in db_infos:
            if not db.get("tables"):
                continue

            db_id, db_name, db_type = db["database_id"], db["database_name"], db["database_type"]

            # 1 ▸ generate SQL via Bedrock
            sql = await self._generate_sql_query(user_query, db_type, db["tables"])
            if not sql:
                logger.warning("LLM returned empty SQL for %s", db_id)
                continue

            # 2 ▸ execute with connection recovery, timeout handling, and performance monitoring
            try:
                async with performance_monitor.timer("sql_execution", {"database_id": db_id}):
                    # Add query timeout protection at agent level
                    import asyncio
                    raw = await asyncio.wait_for(
                        self.database_service.execute_query(db_id, sql),
                        timeout=180.0  # 3 minutes total timeout at agent level
                    )
                performance_monitor.increment_counter("sql_executions_success")
            except asyncio.TimeoutError:
                performance_monitor.increment_counter("sql_executions_timeout")
                logger.error(f"Query execution timed out after 3 minutes for database {db_id}")
                error_msg = "Query execution timed out. Please try a simpler query or add more specific filters to reduce the data being processed."
                results.append({
                    "database_name": db_name,
                    "query": sql,
                    "error": error_msg,
                    "error_type": "timeout"
                })
                continue
            except Exception as exc:
                performance_monitor.increment_counter("sql_executions_failed")
                logger.exception("Query failed on %s", db_id)
                error_msg = str(exc)

                # Check if it's a connection-related error and attempt recovery
                if ("PendingRollbackError" in error_msg or
                    "invalid transaction" in error_msg.lower() or
                    "connection" in error_msg.lower() or
                    "not connected" in error_msg.lower()):
                    logger.info(f"Attempting connection recovery for database {db_id}")
                    try:
                        # Force connection recreation - this will remove the invalid connection
                        await self.database_service._recreate_connection(db_id)
                        logger.info(f"Connection recreated for database {db_id}")

                        # Note: We cannot retry the query here because the SQL agent doesn't have
                        # access to the Database object needed to reconnect. The connection will
                        # be reestablished on the next request by the orchestrator.
                        results.append({
                            "database_name": db_name,
                            "query": sql,
                            "error": "Database connection was reset due to transaction error. Please retry your query."
                        })
                        continue
                    except Exception as retry_exc:
                        logger.error(f"Connection recovery failed for {db_id}: {retry_exc}")
                        results.append({
                            "database_name": db_name,
                            "query": sql,
                            "error": f"Database connection error: {str(retry_exc)}"
                        })
                        continue
                else:
                    results.append({"database_name": db_name, "query": sql, "error": error_msg})
                    continue

            # 3 ▸ format & upload
            formatted = self._format_result(
                raw,
                output_format=output_format,
                user_id=user_id,
                session_id=session_id,
            )

            results.append(
                {
                    "database_name": db_name,
                    "query": sql,
                    **formatted,
                }
            )

        if not results:
            return AgentResponse(self.agent_id, False, data={"message": "No results"}).to_dict()

        return AgentResponse(self.agent_id, True, data={"results": results, "query": user_query}).to_dict()

    # ──────────────────────────────────────────────────────────
    # helpers
    # ──────────────────────────────────────────────────────────
    async def _generate_sql_query(
        self,
        user_query: str,
        database_type: str,
        tables: List[Dict[str, Any]],
    ) -> str:
        # Enhanced caching: Check intelligent cache first
        query_signature = self._generate_sql_cache_key(user_query, database_type, tables)

        # Try intelligent cache service first
        cached_sql = intelligent_cache_service.get_sql_query(query_signature)
        if cached_sql:
            logger.debug(f"Using intelligent cached SQL for query: {user_query[:50]}...")
            return cached_sql

        # Fallback to legacy cache
        if self._is_sql_cache_valid(query_signature):
            logger.debug(f"Using legacy cached SQL for query: {user_query[:50]}...")
            return self.sql_cache[query_signature]

        schema_txt   = self._schema_snippet(tables)
        system_prompt = get_sql_generation_prompt(database_type)

        user_prompt = (
            f"Database schema:\n{schema_txt}\n\n"
            f"User question: {user_query}\n\n"
            f"Write a {database_type} SQL query that answers the question. "
            "Return ONLY the SQL."
        )

        # Add performance monitoring
        async with performance_monitor.timer("sql_generation", {"database_type": database_type}):
            sql = (await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.2,
            )).strip()

        if sql.startswith("```"):
            sql = sql.lstrip("`sql").strip("`")

        cleaned_sql = sql.strip()

        # Cache the generated SQL in both intelligent and legacy caches
        intelligent_cache_service.cache_sql_query(query_signature, cleaned_sql, ttl=1800)  # 30 minutes
        self._cache_sql(query_signature, cleaned_sql)

        return cleaned_sql

    def _schema_snippet(self, tables: List[Any]) -> str:
        lines: List[str] = []
        for t in tables:
            # Handle both dictionary and DatabaseTable object formats
            if hasattr(t, 'name'):  # DatabaseTable object
                table_name = t.name
                columns = t.columns if hasattr(t, 'columns') else []
            else:  # Dictionary format
                table_name = t.get('name', 'Unknown')
                columns = t.get('columns', [])

            lines.append(f"Table: {table_name}")

            for c in columns:
                # Handle both dictionary and DatabaseColumn object formats
                if hasattr(c, 'name'):  # DatabaseColumn object
                    col_name = c.name
                    col_type = getattr(c, 'data_type', 'unknown')
                    is_pk = getattr(c, 'is_primary_key', False)
                    is_fk = getattr(c, 'is_foreign_key', False)
                    is_nullable = getattr(c, 'is_nullable', True)
                else:  # Dictionary format
                    col_name = c.get('name', 'unknown')
                    col_type = c.get('data_type', 'unknown')
                    is_pk = c.get('is_primary_key', False)
                    is_fk = c.get('is_foreign_key', False)
                    is_nullable = c.get('is_nullable', True)

                attrs = []
                if is_pk: attrs.append("PK")
                if is_fk: attrs.append("FK")
                if not is_nullable: attrs.append("NOT NULL")
                attr = f" [{' '.join(attrs)}]" if attrs else ""
                lines.append(f"  - {col_name} ({col_type}){attr}")
            lines.append("")
        return "\n".join(lines)

    # ------------------------------------------------------------------ #
    # S3 export + optional inline preview
    # ------------------------------------------------------------------ #
    def _format_result(
        self,
        result: Union[pd.DataFrame, List[Dict[str, Any]]],
        *,
        output_format: str,
        user_id: str,
        session_id: str,
    ) -> Dict[str, Any]:
        # ── non-tabular (Mongo) → inline JSON only
        if isinstance(result, list):
            return {
                "preview"   : result,
                "columns"   : sorted({k for row in result for k in row}),
                "file_path" : None,
                "format"    : "json",
                "size_bytes": None,
            }

        # ── pandas ▸ serialise
        fmt = "csv" if output_format not in ("excel", "xlsx") else "excel"

        if fmt == "csv":
            blob = result.to_csv(index=False).encode()
            ext, ctype = "csv", "text/csv"
        else:
            try:
                buf = io.BytesIO()
                with pd.ExcelWriter(buf, engine="openpyxl") as w:
                    result.to_excel(w, index=False)
                blob = buf.getvalue()
                ext, ctype = "xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            except Exception:
                # fallback to csv
                blob = result.to_csv(index=False).encode()
                ext, ctype = "csv", "text/csv"
                fmt = "csv"

        # ── upload to S3 using new unified structure
        url = self.report_store.upload_report(
            blob=blob, 
            user_id=user_id, 
            session_id=session_id,
            report_type="sql_result",
            file_extension=ext,
            content_type=ctype
        )
        size = len(blob)

        # ── inline preview if small
        preview = None
        if size <= INLINE_PREVIEW_MAX_BYTES:
            preview = result.head(MAX_PREVIEW_ROWS).astype(str).to_dict("records")

        return {
            "file_path" : url,
            "format"    : fmt,
            "size_bytes": size,
            "preview"   : preview,
            "columns"   : result.columns.tolist(),
        }

    # ──────────────────────────────────────────────────────────
    # Performance optimization: SQL caching methods
    # ──────────────────────────────────────────────────────────
    def _generate_sql_cache_key(self, user_query: str, database_type: str, tables: List[Any]) -> str:
        """Generate a cache key for SQL queries."""
        import hashlib

        # Handle both dictionary and DatabaseTable object formats
        table_names = []
        for t in tables:
            if hasattr(t, 'name'):  # DatabaseTable object
                table_names.append(t.name)
            elif isinstance(t, dict):  # Dictionary format
                table_names.append(t.get("name", ""))
            else:
                table_names.append(str(t))

        # Create a deterministic representation
        cache_data = {
            "query": user_query.lower().strip(),
            "db_type": database_type,
            "tables": sorted(table_names)
        }

        cache_string = str(cache_data)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _is_sql_cache_valid(self, cache_key: str) -> bool:
        """Check if cached SQL is still valid."""
        if cache_key not in self.sql_cache:
            return False

        import time
        cache_time = self.cache_ttl.get(cache_key, 0)
        return time.time() - cache_time < self.CACHE_DURATION

    def _cache_sql(self, cache_key: str, sql: str) -> None:
        """Cache generated SQL with timestamp."""
        import time
        self.sql_cache[cache_key] = sql
        self.cache_ttl[cache_key] = time.time()

        # Limit cache size to prevent memory issues
        if len(self.sql_cache) > 200:
            # Remove oldest entries
            oldest_keys = sorted(self.cache_ttl.keys(), key=lambda k: self.cache_ttl[k])[:50]
            for key in oldest_keys:
                self.sql_cache.pop(key, None)
                self.cache_ttl.pop(key, None)

    def clear_sql_cache(self) -> None:
        """Clear SQL cache."""
        self.sql_cache.clear()
        self.cache_ttl.clear()
