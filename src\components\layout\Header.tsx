"use client";
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, LayoutDashboard, ChevronRight, Plus, Brain } from 'lucide-react';
import { useAuth } from '@/providers/AuthContext';
import { useTheme } from "@/providers/theme-provider";
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { usePageHeader } from '@/providers/PageHeaderContext';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

const Header = () => {
  const { isAuthenticated } = useAuth();
  const { theme, setTheme } = useTheme();
  const { pageInfo, actions } = usePageHeader();
  const pathname = usePathname();
  
  // Check if we have breadcrumbs to show
  const hasBreadcrumbs = pageInfo.breadcrumbs && pageInfo.breadcrumbs.length > 1;
  
  // Get chart-related state from context actions
  const { onCreateChart, chartCount = 0, maxCharts = 12, onCreateAnalysis, isCreatingAnalysis = false } = actions;
  const canCreateChart = chartCount < maxCharts;
  
  // Show Create Chart Button on dashboard pages when the callback is provided
  const isDashboardPage = pathname === '/dashboard';
  const isAIWorkflowsPage = pathname === '/ai-workflows';
  const showCreateChartButton = isDashboardPage && onCreateChart;
  const showCreateAnalysisButton = isAIWorkflowsPage && onCreateAnalysis;

  // Get the appropriate icon for breadcrumbs
  const getBreadcrumbIcon = () => {
    if (pathname === '/dashboard') return LayoutDashboard;
    if (pathname === '/ai-workflows') return Brain;
    return pageInfo.icon || LayoutDashboard;
  };

  const BreadcrumbIcon = getBreadcrumbIcon();

  return (
    <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-4 py-2 h-12">
      <div className="flex items-center gap-3 text-sidebar-text-primary">
        {isAuthenticated && (
          <div className="lg:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon" className="h-7 w-7">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
            </Sheet>
          </div>
        )}
        
        {/* Dynamic Breadcrumb Navigation or Page Title */}
        {hasBreadcrumbs ? (
          <Breadcrumb>
            <BreadcrumbList>
              {pageInfo.breadcrumbs!.map((breadcrumb, index) => (
                <React.Fragment key={breadcrumb.label}>
                  <BreadcrumbItem>
                    {index === 0 ? (
                      // First breadcrumb item (with icon)
                      breadcrumb.onClick || breadcrumb.href ? (
                        <BreadcrumbLink 
                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}
                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}
                          className="flex items-center space-x-1.5 cursor-pointer hover:text-primary text-sm font-medium"
                        >
                          <BreadcrumbIcon className="h-4 w-4" />
                          <span>{breadcrumb.label}</span>
                        </BreadcrumbLink>
                      ) : (
                        <div className="flex items-center space-x-1.5 text-sm font-medium">
                          <BreadcrumbIcon className="h-4 w-4" />
                          <span>{breadcrumb.label}</span>
                        </div>
                      )
                    ) : (
                      // Subsequent breadcrumb items
                      index === pageInfo.breadcrumbs!.length - 1 ? (
                        <BreadcrumbPage className="font-medium text-sm">
                          {breadcrumb.label}
                        </BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink 
                          {...(breadcrumb.href ? { href: breadcrumb.href } : {})}
                          {...(breadcrumb.onClick ? { onClick: breadcrumb.onClick } : {})}
                          className="cursor-pointer hover:text-primary text-sm font-medium"
                        >
                          {breadcrumb.label}
                        </BreadcrumbLink>
                      )
                    )}
                  </BreadcrumbItem>
                  
                  {/* Separator */}
                  {index < pageInfo.breadcrumbs!.length - 1 && (
                    <BreadcrumbSeparator>
                      <ChevronRight className="h-3 w-3" />
                    </BreadcrumbSeparator>
                  )}
                </React.Fragment>
              ))}
            </BreadcrumbList>
          </Breadcrumb>
        ) : (
          <h1 className="text-sm font-medium leading-tight tracking-[-0.015em] text-sidebar-text-primary">
            {pageInfo.title}
          </h1>
        )}
      </div>

      {/* Right side actions */}
      <div className="flex items-center gap-2">
        {showCreateChartButton && (
          <Button
            onClick={onCreateChart}
            disabled={!canCreateChart}
            className="border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs"
            style={{
              backgroundColor: 'var(--surface-selected)',
              color: 'var(--sidebar-text-primary)'
            }}
            title={!canCreateChart ? `Maximum of ${maxCharts} charts allowed` : undefined}
            onMouseEnter={(e) => {
              if (canCreateChart) {
                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
              }
            }}
            onMouseLeave={(e) => {
              if (canCreateChart) {
                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';
              }
            }}
          >
            <Plus className="h-3 w-3 mr-1.5" />
            Chart
          </Button>
        )}
        {showCreateAnalysisButton && (
          <Button
            onClick={onCreateAnalysis}
            disabled={isCreatingAnalysis}
            className="border-0 rounded-md transition-all duration-200 h-8 px-3 text-xs"
            style={{
              backgroundColor: 'var(--surface-selected)',
              color: 'var(--sidebar-text-primary)'
            }}
            title={isCreatingAnalysis ? "Analysis is being created" : undefined}
            onMouseEnter={(e) => {
              if (!isCreatingAnalysis) {
                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isCreatingAnalysis) {
                e.currentTarget.style.backgroundColor = 'var(--surface-selected)';
              }
            }}
          >
            <Plus className="h-3 w-3 mr-1.5" />
            {isCreatingAnalysis ? 'Creating...' : 'Analysis'}
          </Button>
        )}
      </div>
    </header>
  );
};

export default Header;