"use client";
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useApi, ConnectedDatabase, DatabaseConnectionRequestParams } from '@/providers/ApiContext';
import { useAuth } from '@/providers/AuthContext';
import { usePageTitle } from '@/hooks/usePageTitle';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import {
  Database,
  RefreshCw,
  AlertTriangle,
  Search,
  Filter,
  MoreVertical
} from 'lucide-react';
import ConnectDataSourceModal from './ConnectDataSourceModal';
// import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";

// Custom database icon components using the provided SVGs
const SupabaseIcon = ({ className }: { className?: string }) => (
  <img 
    src="/icons/supabase-logo-icon.svg" 
    alt="Supabase" 
    className={className}
  />
);

const MySQLIcon = ({ className }: { className?: string }) => (
  <img 
    src="/icons/mysql.svg" 
    alt="MySQL" 
    className={className}
  />
);

const MongoDBIcon = ({ className }: { className?: string }) => (
  <img 
    src="/icons/mongoDB.svg" 
    alt="MongoDB" 
    className={className}
  />
);

const PostgreSQLIcon = ({ className }: { className?: string }) => (
  <img 
    src="/icons/postgres.svg" 
    alt="PostgreSQL" 
    className={className}
  />
);

// Interface for the static list of available data source types
interface DataSourceField {
  label: string;
  name: keyof DatabaseConnectionRequestParams;
  type: 'text' | 'number' | 'password' | 'checkbox';
  required?: boolean;
  placeholder?: string;
}

interface DataSourceType {
  id: string;
  name: string;
  typeCategory: 'database' | 'file' | 'cloud' | 'other';
  description: string;
  icon: React.ElementType;
  connectionFields: DataSourceField[];
}

// Static list of available data source types
const AVAILABLE_DATA_SOURCE_TYPES: DataSourceType[] = [
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    typeCategory: 'database',
    description: 'Connect to your PostgreSQL relational database for powerful queries and analytics.',
    icon: PostgreSQLIcon,
    connectionFields: [
      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My PostgreSQL DB' },
      { label: 'Host', name: 'host', type: 'text', required: true, placeholder: 'localhost' },
      { label: 'Port', name: 'port', type: 'number', required: true, placeholder: '5432' },
      { label: 'Username', name: 'username', type: 'text', required: true, placeholder: 'postgres' },
      { label: 'Password', name: 'password', type: 'password', required: true },
      { label: 'Database Name', name: 'database', type: 'text', required: true, placeholder: 'mydatabase' },
      { label: 'Schema (optional)', name: 'db_schema', type: 'text', placeholder: 'public' },
      { label: 'Enable SSL', name: 'ssl_enabled', type: 'checkbox' },
    ],
  },
  {
    id: 'supabase',
    name: 'Supabase',
    typeCategory: 'database',
    description: 'Connect to your Supabase database for real-time data access.',
    icon: SupabaseIcon,
    connectionFields: [
      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My Supabase DB' },
      { label: 'Host', name: 'host', type: 'text', required: true, placeholder: 'aws-0-us-east-2.pooler.supabase.com' },
      { label: 'Port', name: 'port', type: 'number', required: true, placeholder: '6543' },
      { label: 'Username', name: 'username', type: 'text', required: true, placeholder: 'postgres.fnwrnsojpsmleatmvmjw' },
      { label: 'Password', name: 'password', type: 'password', required: true },
      { label: 'Database Name', name: 'database', type: 'text', required: true, placeholder: 'postgres' },
      { label: 'Enable SSL', name: 'ssl_enabled', type: 'checkbox', required: true },
    ],
  },
  {
    id: 'mongodb',
    name: 'MongoDB',
    typeCategory: 'database',
    description: 'Connect to your MongoDB NoSQL document database for flexible data operations.',
    icon: MongoDBIcon,
    connectionFields: [
      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My MongoDB Atlas' },
      { label: 'Connection String', name: 'connection_string', type: 'text', required: true, placeholder: 'mongodb+srv://user:<EMAIL>/dbname' },
    ],
  },
  {
    id: 'mysql',
    name: 'MySQL',
    typeCategory: 'database',
    description: 'Connect to your MySQL open-source relational database for reliable data access.',
    icon: MySQLIcon,
    connectionFields: [
      { label: 'Connection Name', name: 'name', type: 'text', required: true, placeholder: 'My MySQL DB' },
      { label: 'Host', name: 'host', type: 'text', required: true, placeholder: 'localhost' },
      { label: 'Port', name: 'port', type: 'number', required: true, placeholder: '3306' },
      { label: 'Username', name: 'username', type: 'text', required: true, placeholder: 'root' },
      { label: 'Password', name: 'password', type: 'password', required: true },
      { label: 'Database Name', name: 'database', type: 'text', required: true, placeholder: 'mydatabase' },
    ],
      },
  ];



// Helper component for connection status badge (unused)
// const ConnectionStatusBadge = ({ isConnected }: { isConnected: boolean }) => {
//   if (isConnected) {
//     return (
//       <Badge variant="secondary" className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">
//         <Wifi className="w-3 h-3 mr-1" />
//         Connected
//       </Badge>
//     );
//   }

//   return (
//     <Badge variant="outline" className="text-slate-500 dark:text-slate-400">
//       <WifiOff className="w-3 h-3 mr-1" />
//       Disconnected
//     </Badge>
//   );
// };

const DataSourcesPageContent = () => {
  const { listDatabases, connectNewDatabase, disconnectExistingDatabase } = useApi();
  const { isAuthenticated } = useAuth();

  // Set page title - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: 'Data Sources',
    icon: Database
  }), []);

  usePageTitle(pageConfig);

  const [connectedSources, setConnectedSources] = useState<ConnectedDatabase[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [dataSourceToConnect, setDataSourceToConnect] = useState<DataSourceType | null>(null);
  
  // State for managing dropdown menus
  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);

  // New state for enhanced features
  const [searchQuery] = useState('');
  const [selectedCategory] = useState<string>('all');

  console.log('DataSourcesPageContent: Component render');

  // Load data when authenticated - handles page refresh and auth state changes
  useEffect(() => {
    console.log('DataSourcesPageContent: Auth state changed, isAuthenticated:', isAuthenticated);

    if (isAuthenticated) {
      const fetchInitialData = async () => {
        console.log('DataSourcesPageContent: Starting initial fetch');
        setIsLoading(true);
        setApiError(null);
        try {
          const sources = await listDatabases();
          console.log('DataSourcesPageContent: Initial fetch completed, sources:', sources.length);
          setConnectedSources(sources);
        } catch (err) {
          console.error('Failed to fetch connected data sources', err);
          setApiError('Could not load connected data sources. Please try again.');
          setConnectedSources([]);
        } finally {
          setIsLoading(false);
        }
      };

      fetchInitialData();
    } else {
      // Clear data when not authenticated
      setConnectedSources([]);
      setApiError(null);
      setIsLoading(false);
    }
  }, [isAuthenticated, listDatabases]); // Depend on auth state

  // Separate refresh function for manual refresh button
  const handleRefresh = useCallback(async () => {
    if (!isAuthenticated) {
      setApiError('Please log in to refresh data sources.');
      return;
    }

    setIsRefreshing(true);
    setApiError(null);
    try {
      const sources = await listDatabases();
      setConnectedSources(sources);
    } catch (err) {
      console.error('Failed to refresh connected data sources', err);
      setApiError('Could not refresh data sources. Please try again.');
    } finally {
      setIsRefreshing(false);
    }
  }, [isAuthenticated, listDatabases]);

  // Retry function for error recovery
  const retryLoadDataSources = useCallback(async () => {
    if (!isAuthenticated) return;

    setApiError(null);
    setIsLoading(true);
    try {
      const sources = await listDatabases();
      setConnectedSources(sources);
    } catch (err) {
      console.error('Failed to retry loading data sources', err);
      setApiError('Could not load data sources. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, listDatabases]);

  // Filter data sources based on search and category
  const filteredAvailableDataSources = AVAILABLE_DATA_SOURCE_TYPES.filter(source => {
    const matchesSearch = source.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      source.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || source.typeCategory === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const filteredConnectedSources = connectedSources.filter(source => {
    const matchesSearch = source.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      source.type.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  // Helper function to refresh after successful connect/disconnect operations
  const refreshAfterChange = useCallback(async () => {
    setApiError(null);
    try {
      const sources = await listDatabases();
      setConnectedSources(sources);
    } catch (err) {
      console.error('Failed to refresh after change', err);
      setApiError('Could not refresh data sources after the operation.');
    }
  }, [listDatabases]);

  const handleOpenConnectModal = useCallback((sourceType: DataSourceType) => {
    setDataSourceToConnect(sourceType);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setDataSourceToConnect(null);
  }, []);

  const handleEditConnection = useCallback((connection: ConnectedDatabase) => {
    console.log('Edit functionality not yet implemented for connection:', connection.name);
    setMenuOpenId(null);
    // TODO: Implement edit functionality when API supports it
  }, []);

  const handleDeleteConnection = useCallback(async (connectionId: string, connectionName: string) => {
    console.log('Deleting connection:', connectionName);
    setMenuOpenId(null);
    
    // Show confirmation dialog
    const confirmed = window.confirm(`Are you sure you want to delete the connection "${connectionName}"? This action cannot be undone.`);
    if (!confirmed) return;

    setApiError(null);
    try {
      console.log('Disconnecting database with ID:', connectionId);
      const result = await disconnectExistingDatabase(connectionId);
      console.log('Successfully disconnected database:', result.message);
      
      // Refresh the list after successful deletion
      await refreshAfterChange();
    } catch (err: unknown) {
      console.error('Failed to delete connection', err);
      let errorMessage = 'Failed to delete connection. Please try again.';
      
      if (err && typeof err === 'object' && 'response' in err) {
        const axiosErr = err as { response?: { status?: number; data?: { detail?: unknown; message?: string } } };
        console.log('Error response:', axiosErr.response?.data);

        if (axiosErr.response?.status === 422) {
          // Handle FastAPI validation errors
          const detail = axiosErr.response.data?.detail;
          if (Array.isArray(detail)) {
            // Pydantic validation errors are arrays
            const validationErrors = detail.map((error: unknown) => {
              if (error && typeof error === 'object' && 'loc' in error && 'msg' in error) {
                const field = Array.isArray((error as { loc: unknown }).loc) 
                  ? ((error as { loc: string[] }).loc).join('.') 
                  : 'unknown field';
                return `${field}: ${(error as { msg: string }).msg}`;
              }
              return 'validation error';
            }).join(', ');
            errorMessage = `Validation error: ${validationErrors}`;
          } else if (typeof detail === 'string') {
            errorMessage = detail;
          } else {
            errorMessage = 'Validation failed. Please check your input.';
          }
        } else if (axiosErr.response?.data?.detail && typeof axiosErr.response.data.detail === 'string') {
          errorMessage = axiosErr.response.data.detail;
        } else if (axiosErr.response?.data?.message) {
          errorMessage = axiosErr.response.data.message;
        }
      } else if (err && typeof err === 'object' && 'message' in err && typeof (err as { message: unknown }).message === 'string') {
        errorMessage = (err as { message: string }).message;
      }

      setApiError(errorMessage);
    }
  }, [disconnectExistingDatabase, refreshAfterChange]);

  const handleConnect = useCallback(async (params: DatabaseConnectionRequestParams) => {
    if (!dataSourceToConnect) return false;

    setApiError(null);
    try {
      const fullParams = { ...params, type: dataSourceToConnect.id };
      console.log('DataSourcesPageContent: Sending connect request with params:', fullParams);
      await connectNewDatabase(fullParams);
      await refreshAfterChange(); // Use the helper function
      handleCloseModal();
      return true;
    } catch (err: unknown) {
      console.error('Failed to connect data source', err);

      // Handle different types of errors
      let errorMessage = 'Failed to connect. Please check details and try again.';

      if (err && typeof err === 'object' && 'response' in err) {
        const axiosErr = err as { response?: { status?: number; data?: { detail?: unknown; message?: string } } };
        console.log('Error response:', axiosErr.response?.data);

        if (axiosErr.response?.status === 422) {
          // Handle FastAPI validation errors
          const detail = axiosErr.response.data?.detail;
          if (Array.isArray(detail)) {
            // Pydantic validation errors are arrays
            const validationErrors = detail.map((error: unknown) => {
              if (error && typeof error === 'object' && 'loc' in error && 'msg' in error) {
                const field = Array.isArray((error as { loc: unknown }).loc) 
                  ? ((error as { loc: string[] }).loc).join('.') 
                  : 'unknown field';
                return `${field}: ${(error as { msg: string }).msg}`;
              }
              return 'validation error';
            }).join(', ');
            errorMessage = `Validation error: ${validationErrors}`;
          } else if (typeof detail === 'string') {
            errorMessage = detail;
          } else {
            errorMessage = 'Validation failed. Please check your input.';
          }
        } else if (axiosErr.response?.data?.detail && typeof axiosErr.response.data.detail === 'string') {
          errorMessage = axiosErr.response.data.detail;
        } else if (axiosErr.response?.data?.message) {
          errorMessage = axiosErr.response.data.message;
        }
      } else if (err && typeof err === 'object' && 'message' in err && typeof (err as { message: unknown }).message === 'string') {
        errorMessage = (err as { message: string }).message;
      }

      setApiError(errorMessage);
      return false;
    }
  }, [dataSourceToConnect, connectNewDatabase, refreshAfterChange, handleCloseModal]);

  // const handleDisconnect = useCallback(async (dbId: string) => {
  //   setApiError(null);
  //   try {
  //     await disconnectExistingDatabase(dbId);
  //     await refreshAfterChange(); // Use the helper function
  //   } catch (err: unknown) {
  //     console.error('Failed to disconnect data source', err);
  //     let errorMessage = 'Failed to disconnect. Please try again.';
  //     if (err && typeof err === 'object' && 'response' in err) {
  //       const axiosErr = err as { response?: { data?: { detail?: string } } };
  //       errorMessage = axiosErr.response?.data?.detail || errorMessage;
  //     }
  //     setApiError(errorMessage);
  //   }
  // }, [disconnectExistingDatabase, refreshAfterChange]);

  const renderDataSourceTypeCard = (sourceType: DataSourceType) => {
    const Icon = sourceType.icon;

    return (
      <Card
        key={sourceType.id}
        className="group h-full border border-sidebar-border bg-sidebar-bg"
        style={{
          backgroundColor: 'var(--interactive-bg-secondary-hover)'
        }}
      >
        <CardContent className="flex flex-col items-center justify-center p-6 text-center space-y-4">
          {/* Logo */}
          <div className="w-12 h-12 flex items-center justify-center">
            <Icon className="h-10 w-10" />
          </div>
          
          {/* Database Name */}
          <h3 
            className="text-lg font-medium"
            style={{ color: 'var(--sidebar-text-primary)' }}
          >
            {sourceType.name}
          </h3>
          
          {/* Connect Button */}
          <Button
            onClick={() => handleOpenConnectModal(sourceType)}
            variant="outline"
            className="w-full justify-center text-sm font-normal border border-sidebar-border rounded-lg transition-all duration-200 h-9"
            size="sm"
            disabled={isLoading || isRefreshing}
            aria-label={`Connect to ${sourceType.name}`}
            style={{
              color: 'var(--sidebar-text-secondary)',
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              if (!isLoading && !isRefreshing) {
                e.currentTarget.style.backgroundColor = 'var(--sidebar-text-primary)';
                e.currentTarget.style.setProperty('color', 'var(--sidebar-bg)', 'important');
                e.currentTarget.style.transform = 'scale(1.02)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isLoading && !isRefreshing) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');
                e.currentTarget.style.transform = 'scale(1)';
                e.currentTarget.style.boxShadow = 'none';
              }
            }}
          >
            Connect
          </Button>
        </CardContent>
      </Card>
    );
  };

  const renderConnectedDataSourceCard = (connectedDb: ConnectedDatabase) => {
    const dsType = AVAILABLE_DATA_SOURCE_TYPES.find(type => type.id.toUpperCase() === connectedDb.type.toUpperCase());
    const Icon = dsType?.icon || Database;

    return (
      <div
        key={connectedDb.id}
        className="group flex items-center gap-3 p-3 rounded-lg transition-all duration-200 border border-sidebar-border min-w-fit"
        style={{
          backgroundColor: 'var(--sidebar-bg)'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--sidebar-bg)';
        }}
      >
        {/* Icon Container */}
        <div className="flex-shrink-0 w-8 h-8 rounded flex items-center justify-center">
          <Icon className="h-5 w-5" />
        </div>
        
        {/* Database Name */}
        <div 
          className="cursor-pointer"
          onClick={() => {
            // Add click handler for navigation/configuration if needed
            console.log('Clicked on database:', connectedDb.name);
          }}
        >
          <span 
            className="text-sm font-medium whitespace-nowrap block"
            style={{ color: 'var(--sidebar-text-primary)' }}
          >
            {connectedDb.name}
          </span>
        </div>

        {/* Three-dots Menu */}
        <div className="relative ml-2 flex-shrink-0" onClick={e => e.stopPropagation()}>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                type="button"
                size="icon"
                variant="ghost"
                className="w-7 h-7 p-0 transition-all duration-200 rounded border-0"
                style={{ 
                  color: 'var(--sidebar-text-secondary)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');
                  e.currentTarget.style.setProperty('color', 'var(--sidebar-text-primary)', 'important');
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
                  e.currentTarget.style.setProperty('color', 'var(--sidebar-text-secondary)', 'important');
                }}
                aria-label={`More actions for ${connectedDb.name}`}
              >
                <MoreVertical className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent 
              align="start" 
              side="bottom" 
              sideOffset={8} 
              className="border-none shadow-xl rounded-xl p-2"
              style={{
                backgroundColor: 'var(--sidebar-surface-secondary)',
                color: 'var(--sidebar-text-primary)'
              }}
            >
              <DropdownMenuItem
                onClick={() => handleEditConnection(connectedDb)}
                className="rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200"
                style={{ 
                  color: 'var(--sidebar-text-primary)',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Edit Connection
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => handleDeleteConnection(connectedDb.id, connectedDb.name)}
                className="rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200"
                style={{ 
                  color: '#ff8583',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }}
              >
                Delete Connection
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    );
  };



  return (
    <TooltipProvider>
      <div
        className="min-h-screen bg-sidebar-bg"
        role="main"
        aria-label="Data Sources Management"
      >
        <div className="container mx-auto space-y-6 sm:space-y-8 p-4 sm:p-6 lg:p-8">

          {/* Error Alert */}
          {apiError && (
            <div className="p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
              <div className="flex items-start justify-between gap-3">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
                  <div className="text-red-800 dark:text-red-200">
                    <div className="font-medium">Error</div>
                    <div className="text-sm">{apiError}</div>
                  </div>
                </div>
                <Button
                  onClick={retryLoadDataSources}
                  disabled={isLoading || isRefreshing}
                  variant="outline"
                  size="sm"
                  className="ml-4 border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-900"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {/* Connected Data Sources Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-sidebar-text-primary flex items-center gap-2">
                Connected Sources
              </h2>
              <p className="text-sm sm:text-base text-sidebar-text-secondary mt-1">
                {filteredConnectedSources.length} of {connectedSources.length} {connectedSources.length === 1 ? 'source' : 'sources'}
                {searchQuery && ' matching your search'}
              </p>
            </div>

            {isLoading ? (
              // Loading skeleton
              <div className="flex flex-wrap gap-4">
                {[...Array(4)].map((_, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 p-3 rounded-lg border border-sidebar-border animate-pulse min-w-fit"
                    style={{
                      backgroundColor: 'var(--sidebar-bg)'
                    }}
                  >
                    {/* Icon skeleton */}
                    <div className="flex-shrink-0 w-8 h-8 rounded bg-sidebar-text-secondary opacity-20"></div>
                    
                    {/* Text skeleton */}
                    <div className="min-w-0">
                      <div className="h-4 bg-sidebar-text-secondary opacity-20 rounded w-24"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredConnectedSources.length > 0 ? (
              <div className="flex flex-wrap gap-4">
                {filteredConnectedSources.map(renderConnectedDataSourceCard)}
              </div>
            ) : connectedSources.length > 0 ? (
              <Card className="text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg">
                <CardContent className="space-y-4">
                  <div className="p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto">
                    <Search className="h-8 w-8 text-sidebar-text-secondary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-sidebar-text-primary">
                      No sources match your search
                    </h3>
                    <p className="text-sidebar-text-secondary mt-2">
                      Try adjusting your search terms or filters
                    </p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg">
                <CardContent className="space-y-4">
                  <div className="p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto">
                    <Database className="h-8 w-8 text-sidebar-text-secondary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-sidebar-text-primary">
                      No data sources connected
                    </h3>
                    <p className="text-sidebar-text-secondary mt-2">
                      Connect your first data source to get started with AI-powered queries
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Separator */}
          <div className="border-t border-sidebar-border my-8"></div>

          {/* Available Data Source Types Section */}
          <div className="space-y-6">
            <div>
              <h2 className="text-xl sm:text-2xl font-semibold text-sidebar-text-primary flex items-center gap-2">
                Add New Connection
              </h2>
              <p className="text-sm sm:text-base text-sidebar-text-secondary mt-1">
                {filteredAvailableDataSources.length} available data source{filteredAvailableDataSources.length !== 1 ? 's' : ''}
                {searchQuery && ' matching your search'}
              </p>
            </div>

            {filteredAvailableDataSources.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                {filteredAvailableDataSources.map(renderDataSourceTypeCard)}
              </div>
            ) : (
              <Card className="text-center py-12 border-dashed border-2 border-sidebar-border bg-sidebar-bg">
                <CardContent className="space-y-4">
                  <div className="p-4 rounded-full bg-sidebar-text-secondary w-fit mx-auto">
                    <Filter className="h-8 w-8 text-sidebar-text-secondary" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-sidebar-text-primary">
                      No data sources match your filters
                    </h3>
                    <p className="text-sidebar-text-secondary mt-2">
                      Try adjusting your search terms or category filters
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Connection Modal */}
          {dataSourceToConnect && (
            <ConnectDataSourceModal
              isOpen={isModalOpen}
              onClose={handleCloseModal}
              dataSourceType={dataSourceToConnect}
              onConnect={handleConnect}
            />
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

export default DataSourcesPageContent; 