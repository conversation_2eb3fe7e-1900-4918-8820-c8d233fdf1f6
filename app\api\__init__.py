"""API Package

This package contains the API routers for the application.
"""

from fastapi import APIRouter

from app.api.authController import router as auth_router
from app.api.databaseController import router as database_router
from app.api.queryController import router as query_router
from app.api.chatController import router as chat_router
from app.api.reportController import router as report_router
from app.api.llm_controller import router as llm_router
from app.api.optimization_controller import router as optimization_router

api_router = APIRouter()

api_router.include_router(chat_router)
api_router.include_router(auth_router)
api_router.include_router(database_router)
api_router.include_router(query_router)
api_router.include_router(report_router)
api_router.include_router(llm_router)
api_router.include_router(optimization_router) 
