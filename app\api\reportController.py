"""Report API

This module provides the API endpoints for report management.
All methods are POST for security and consistency.
All logic and error handling is delegated to the service layer.
Controllers only handle routing and return data.
"""

import logging
from fastapi import APIRouter, Depends

from app.models.api_models import ListReportsRequest, ListReportsResponse
from app.services.report_service import ReportService
from app.utils.security import get_current_user

router = APIRouter(
    prefix="/api/reports",
    tags=["reports"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)
report_service = ReportService()


# ──────────────────────────────────────────
# helper to extract real user id from token
# ──────────────────────────────────────────
async def get_current_user_id(current_user=Depends(get_current_user)) -> str:
    return current_user.id


# ──────────────────────────────────────────
# Public endpoints
# ──────────────────────────────────────────
@router.post(
    "/listreports",
    summary="List all reports for the current user",
    response_model=ListReportsResponse,
)
async def list_user_reports(
    request: ListReportsRequest,
    user_id: str = Depends(get_current_user_id),
):
    """
    List all reports generated by the current user.
    
    Returns a list of reports with metadata including download URLs.
    Reports are sorted by creation date (newest first).
    """
    return await report_service.list_user_reports(
        user_id=user_id,
        max_reports=request.max_reports
    ) 
