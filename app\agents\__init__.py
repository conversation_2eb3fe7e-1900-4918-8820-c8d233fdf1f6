"""
Agents Package

This package contains all AI agents used in the system, organized by functionality:

- database_query/: Agents for /api/ask/question endpoint (SQL queries)
- Report generation agents: Located in root for /api/ask/report endpoint (data analysis)
- Shared components: base.py, toolkit.py
"""

# Import shared components
from .base import Agent, AgentResponse, StandardizedAgentOutputs
from .toolkit import *

# Import report generation agents (in root)
from .report_orchestrator_agent import ConversationalReportOrchestrator
from .data_analysis_agent import DataAnalysisAgent
from .code_generation_agent import CodeGenerationAgent
from .code_execution_agent import CodeExecutionAgent
from .dashboard_agent import DashboardAgent

# Import organized database query package
from . import database_query

__all__ = [
    "Agent",
    "AgentResponse",
    "StandardizedAgentOutputs",
    "ConversationalReportOrchestrator",
    "DataAnalysisAgent",
    "CodeGenerationAgent",
    "CodeExecutionAgent",
    "DashboardAgent",
    "database_query"
]
