"""
ReportStorageService
--------------------
Upgraded service that uses the unified storage structure for better file organization.

New structure:
users/{user_id}/sessions/{session_id}/reports/{timestamp}_{type}.{ext}

Legacy compatibility maintained for existing code.
"""

from __future__ import annotations

import uuid
import io
import datetime as dt
import logging
from typing import Tuple, List, Dict, Any, Optional

import boto3
from botocore.client import Config
from botocore.exceptions import ClientError, NoCredentialsError

from app.config.settings import AWS_REGION, REPORTS_BUCKET, PRESIGN_TTL_SECONDS
from app.services.unified_storage_service import UnifiedStorageService, FileType

logger = logging.getLogger(__name__)


class ReportStorageService:
    def __init__(self) -> None:
        self.bucket = REPORTS_BUCKET
        self.s3 = boto3.client("s3", region_name=AWS_REGION,
                               config=Config(s3={"addressing_style": "virtual"}))
        # Use the unified storage service for organized file handling
        self.unified_storage = UnifiedStorageService()

    # ───────────────────────────────────────────────────────────
    # Enhanced methods using unified storage
    # ───────────────────────────────────────────────────────────
    
    def upload_bytes(
        self,
        *,
        blob: bytes,
        key: str,
        content_type: str,
    ) -> str:
        """Put the object and return a presigned GET url. (Legacy method)"""
        self.s3.put_object(
            Bucket=self.bucket,
            Key=key,
            Body=blob,
            ContentType=content_type,
            ServerSideEncryption="AES256",
        )
        return self._presign(key)

    def upload_report(
        self,
        *,
        blob: bytes,
        user_id: str,
        session_id: str,
        report_type: str = "analysis",
        file_extension: str = "csv",
        content_type: Optional[str] = None
    ) -> str:
        """
        Upload a report using the new unified structure.
        Returns the presigned URL for immediate access.
        """
        try:
            result = self.unified_storage.upload_report(
                blob=blob,
                user_id=user_id,
                session_id=session_id,
                report_type=report_type,
                file_extension=file_extension,
                content_type=content_type
            )
            logger.info(f"📁 Report uploaded to: {result['key']}")
            return result['url']
        except Exception as e:
            logger.error(f"Failed to upload report: {e}")
            raise

    def upload_visualization(
        self,
        *,
        blob: bytes,
        user_id: str,
        session_id: str,
        chart_type: str = "chart",
        file_extension: str = "png"
    ) -> Dict[str, Any]:
        """Upload a visualization file using unified structure."""
        try:
            result = self.unified_storage.upload_visualization(
                blob=blob,
                user_id=user_id,
                session_id=session_id,
                chart_type=chart_type,
                file_extension=file_extension
            )
            logger.info(f"🎨 Visualization uploaded to: {result['key']}")
            return result
        except Exception as e:
            logger.error(f"Failed to upload visualization: {e}")
            raise

    def upload_artifact(
        self,
        *,
        blob: bytes,
        user_id: str,
        session_id: str,
        artifact_name: str
    ) -> Dict[str, Any]:
        """Upload an artifact file (JSON results, etc.)."""
        try:
            result = self.unified_storage.upload_artifact(
                blob=blob,
                user_id=user_id,
                session_id=session_id,
                artifact_name=artifact_name
            )
            logger.info(f"📄 Artifact uploaded to: {result['key']}")
            return result
        except Exception as e:
            logger.error(f"Failed to upload artifact: {e}")
            raise

    def download_bytes(self, key: str) -> bytes:
        """Download an object and return its bytes."""
        return self.unified_storage.download_bytes(key)

    def make_key(
        self,
        *,
        user_id: str,
        session_id: str,
        ext: str,
    ) -> str:
        """Enhanced key generation using unified structure."""
        return self.unified_storage.make_key(
            user_id=user_id, 
            session_id=session_id, 
            ext=ext
        )

    async def list_user_reports(
        self,
        *,
        user_id: str,
        max_reports: int = 100,
    ) -> List[Dict[str, Any]]:
        """
        List all reports for a user using both new and legacy structures.
        
        This method searches both the new unified structure and legacy locations
        to ensure backward compatibility during the transition period.
        """
        try:
            all_reports = []
            
            # Search new unified structure first
            prefix_new = f"users/{user_id}/sessions/"
            logger.info(f"🔍 Searching new structure with prefix: {prefix_new}")
            
            try:
                response_new = self.s3.list_objects_v2(
                    Bucket=self.bucket,
                    Prefix=prefix_new,
                    MaxKeys=max_reports
                )
                
                for obj in response_new.get('Contents', []):
                    try:
                        key = obj['Key']
                        # Only include files in the reports folder
                        if '/reports/' in key and not key.endswith('/'):
                            report_info = self._parse_unified_structure_file(obj)
                            if report_info:
                                all_reports.append(report_info)
                    except Exception as e:
                        logger.warning(f"Error parsing new structure file {obj.get('Key', 'unknown')}: {e}")
                        continue
                        
            except Exception as e:
                logger.warning(f"Error searching new structure: {e}")
            
            # Search legacy structure for backward compatibility
            prefix_legacy = f"u_{user_id}/"
            logger.info(f"🔍 Searching legacy structure with prefix: {prefix_legacy}")
            
            try:
                response_legacy = self.s3.list_objects_v2(
                    Bucket=self.bucket,
                    Prefix=prefix_legacy,
                    MaxKeys=max_reports
                )
                
                for obj in response_legacy.get('Contents', []):
                    try:
                        key = obj['Key']
                        report_info = self._parse_legacy_structure_file(obj)
                        if report_info:
                            all_reports.append(report_info)
                    except Exception as e:
                        logger.warning(f"Error parsing legacy file {obj.get('Key', 'unknown')}: {e}")
                        continue
                        
            except Exception as e:
                logger.warning(f"Error searching legacy structure: {e}")
            
            # Remove duplicates and sort by last_modified (newest first)
            unique_reports = {report['key']: report for report in all_reports}
            sorted_reports = list(unique_reports.values())
            sorted_reports.sort(key=lambda x: x['last_modified'], reverse=True)
            
            # Limit results
            final_reports = sorted_reports[:max_reports]
            
            logger.info(f"📊 Found {len(final_reports)} total reports for user {user_id}")
            logger.info(f"   - New structure: {len([r for r in final_reports if r['key'].startswith('users/')])}")
            logger.info(f"   - Legacy structure: {len([r for r in final_reports if r['key'].startswith('u_')])}")
            
            return final_reports
            
        except Exception as e:
            logger.error(f"Failed to list user reports: {e}")
            raise Exception(f"Failed to retrieve reports: {str(e)}")

    # ───────────────────────────────────────────────────────────
    # Helper methods
    # ───────────────────────────────────────────────────────────
    
    def _parse_unified_structure_file(self, obj: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse files from the new unified structure."""
        try:
            key = obj['Key']
            # Expected format: users/{user_id}/sessions/{session_id}/reports/{timestamp}_{type}.{ext}
            parts = key.split('/')
            
            if len(parts) >= 5 and parts[0] == 'users' and parts[2] == 'sessions' and parts[4] == 'reports':
                user_id = parts[1]
                session_id = parts[3]
                filename = parts[-1]
                
                # Extract file format
                file_format = filename.split('.')[-1].lower() if '.' in filename else 'unknown'
                
                # Get object metadata for content type
                try:
                    head_response = self.s3.head_object(Bucket=self.bucket, Key=key)
                    content_type = head_response.get('ContentType', 'application/octet-stream')
                except ClientError:
                    content_type = 'application/octet-stream'
                
                return {
                    'key': key,
                    'session_id': session_id,
                    'file_name': filename,
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'download_url': self._presign(key),
                    'content_type': content_type,
                    'format': file_format,
                    'file_type': parts[4] if len(parts) > 4 else 'unknown',  # Extract file type from path
                    'structure': 'unified'  # Mark as new structure
                }
            return None
        except Exception as e:
            logger.warning(f"Failed to parse unified structure file: {e}")
            return None

    def _parse_legacy_structure_file(self, obj: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Parse files from the legacy structure for backward compatibility."""
        try:
            key = obj['Key']
            # Expected format: u_{user_id}/{session_id}/{uuid}.{ext}
            parts = key.split('/')
            
            if len(parts) == 3 and parts[0].startswith('u_'):
                session_id = parts[1]
                filename = parts[2]
                
                # Extract file format
                file_format = filename.split('.')[-1].lower() if '.' in filename else 'unknown'
                
                # Get object metadata for content type
                try:
                    head_response = self.s3.head_object(Bucket=self.bucket, Key=key)
                    content_type = head_response.get('ContentType', 'application/octet-stream')
                except ClientError:
                    content_type = 'application/octet-stream'
                
                return {
                    'key': key,
                    'session_id': session_id,
                    'file_name': filename,
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat(),
                    'download_url': self._presign(key),
                    'content_type': content_type,
                    'format': file_format,
                    'file_type': 'reports',  # Legacy files are typically reports
                    'structure': 'legacy'  # Mark as legacy structure
                }
            return None
        except Exception as e:
            logger.warning(f"Failed to parse legacy structure file: {e}")
            return None

    def _presign(self, key: str) -> str:
        return self.s3.generate_presigned_url(
            ClientMethod="get_object",
            Params={"Bucket": self.bucket, "Key": key},
            ExpiresIn=PRESIGN_TTL_SECONDS,
        )
