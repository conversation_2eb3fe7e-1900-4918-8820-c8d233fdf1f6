"use client";

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, BarChart3 } from 'lucide-react';

interface CreateChartInputProps {
  onSubmit: (prompt: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

const CreateChartInput: React.FC<CreateChartInputProps> = ({
  onSubmit,
  isLoading = false,
  className = '',
}) => {
  const [prompt, setPrompt] = useState('');

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) return;

    try {
      await onSubmit(prompt.trim());
      setPrompt('');
    } catch (err) {
      console.error('Failed to create chart:', err);
    }
  }, [prompt, onSubmit]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  }, [handleSubmit]);

  return (
    <div className={`w-full h-full flex flex-col items-center justify-center ${className}`}>
      <form onSubmit={handleSubmit} className="w-full max-w-md">
        <Input
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={isLoading}
          className="w-full"
          maxLength={500}
        />
      </form>
    </div>
  );
};

export default CreateChartInput;
