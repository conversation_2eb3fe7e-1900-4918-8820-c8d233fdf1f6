from __future__ import annotations

"""Utility helper to wrap data into the new standard response schema.

This centralises the logic so the rest of the codebase can simply call
`build_success_response(...)` and obtain a dict that complies with
`app.models.api_models.SuccessResponse`.
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.models.api_models import (
    SuccessResponse,
    Content,
    RenderHints,
    Section,
    SimpleQuestionResponse,
)


# ---------------------------------------------------------------------------
# Public helpers
# ---------------------------------------------------------------------------

def build_success_response(
    *,
    body: str,
    summary: Optional[str] = None,
    sections: Optional[List[Dict[str, str]]] = None,
    fallback_plain: Optional[str] = None,
    css_classes: Optional[List[str]] = None,
    preferred_renderer: Optional[str] = None,
    meta: Optional[Dict[str, Any]] = None,
    status: str = "ok",
) -> Dict[str, Any]:
    """Create a serialisable success response.

    Args:
        body: Markdown (or other) string to send to the client.
        summary: Optional summary string for previews.
        sections: Optional list of dicts with `title` and `body` keys.
        fallback_plain: Optional plain-text fallback.
        css_classes: Suggested CSS classes for the frontend wrapper.
        preferred_renderer: Suggested renderer name (e.g. "react-markdown").
        meta: Opaque metadata useful for debugging (e.g. execution time).
        status: "ok" (default) or "partial".

    Returns:
        dict compliant with SuccessResponse Pydantic model.
    """

    # Convert sections dict -> Section models if provided
    model_sections: Optional[List[Section]] = None
    if sections:
        model_sections = [Section(**s) for s in sections]

    content = Content(
        type="markdown",
        body=body,
        summary=summary,
        sections=model_sections,
        fallback_plain=fallback_plain,
    )

    render_hints: Optional[RenderHints] = None
    if css_classes or preferred_renderer:
        render_hints = RenderHints(
            preferred_renderer=preferred_renderer,
            css_classes=css_classes,
        )

    resp_model = SuccessResponse(
        status=status,
        request_id=str(uuid.uuid4()),
        generated_at=datetime.now(timezone.utc).isoformat(),
        content=content,
        render_hints=render_hints,
        meta=meta,
    )

    return resp_model.dict()


def build_simple_question_response(
    *,
    markdown: str,
    session_id: str,
    query: str,
    has_data: bool = True,
    success: bool = True,
    error: Optional[str] = None,
) -> Dict[str, Any]:
    """Create a simple question response for easy frontend rendering.

    Args:
        markdown: Clean markdown content ready for frontend rendering
        session_id: Session ID for conversation tracking
        query: Original user query
        has_data: Whether relevant data was found
        success: Whether the request was successful
        error: Error message if success=false

    Returns:
        dict compliant with SimpleQuestionResponse Pydantic model.
    """
    resp_model = SimpleQuestionResponse(
        success=success,
        markdown=markdown,
        session_id=session_id,
        query=query,
        has_data=has_data,
        error=error,
    )

    return resp_model.dict()