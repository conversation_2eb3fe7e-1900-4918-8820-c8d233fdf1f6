"use client";

import React, { useState, useCallback, useEffect, useMemo } from 'react';
import { usePageTitle } from '@/hooks/usePageTitle';
import { usePageHeader } from '@/providers/PageHeaderContext';
import { Brain } from 'lucide-react';
import AnalysisProjectList from './AnalysisProjectList';
import AnalysisProjectView from './AnalysisProjectView';

// Types for analysis projects
export interface AnalysisProject {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'completed' | 'error';
  createdAt: Date;
  updatedAt: Date;
  dataSource?: string;
  stepCount: number;
  progress?: number;
}

interface AnalysisNavigationState {
  currentView: 'list' | 'project';
  selectedProject: AnalysisProject | null;
  breadcrumbs: Array<{
    label: string;
    onClick?: () => void;
  }>;
}

// Mock data for analysis projects
const MOCK_ANALYSIS_PROJECTS: AnalysisProject[] = [
  {
    id: 'analysis-1',
    name: 'Employee Performance Analysis',
    description: 'Analyzing employee performance metrics, salary distributions, and department insights',
    status: 'completed',
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    dataSource: 'HR Database',
    stepCount: 3,
  },
  {
    id: 'analysis-2',
    name: 'Customer Behavior Study',
    description: 'Understanding customer purchasing patterns and churn prediction',
    status: 'running',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    dataSource: 'Sales Database',
    stepCount: 4,
    progress: 67,
  },
  {
    id: 'analysis-3',
    name: 'Market Trend Forecasting',
    description: 'Forecasting market trends and demand patterns for Q4',
    status: 'draft',
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    dataSource: 'Market Data API',
    stepCount: 0,
  },
];

const AIWorkflowsPageContent = () => {
  // Navigation state
  const [navigationState, setNavigationState] = useState<AnalysisNavigationState>({
    currentView: 'list',
    selectedProject: null,
    breadcrumbs: [{ label: 'Analysis Projects' }],
  });

  // Analysis projects data
  const [analysisProjects, setAnalysisProjects] = useState<AnalysisProject[]>(MOCK_ANALYSIS_PROJECTS);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);

  const { setPageActions } = usePageHeader();

  // Load analysis projects on component mount
  useEffect(() => {
    loadAnalysisProjects();
  }, []);

  const loadAnalysisProjects = useCallback(async () => {
    setIsLoadingProjects(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      setAnalysisProjects(MOCK_ANALYSIS_PROJECTS);
    } catch (error) {
      console.error('Failed to load analysis projects:', error);
    } finally {
      setIsLoadingProjects(false);
    }
  }, []);

  // Navigation handlers
  const handleSelectProject = useCallback((project: AnalysisProject) => {
    setNavigationState({
      currentView: 'project',
      selectedProject: project,
      breadcrumbs: [
        { label: 'Analysis Projects', onClick: () => handleNavigateBack() },
        { label: project.name },
      ],
    });
  }, []);

  const handleNavigateBack = useCallback(() => {
    setNavigationState({
      currentView: 'list',
      selectedProject: null,
      breadcrumbs: [{ label: 'Analysis Projects' }],
    });
  }, []);

  // Set page title dynamically based on navigation state
  const pageConfig = useMemo(() => ({
    title: navigationState.currentView === 'project' && navigationState.selectedProject 
      ? navigationState.selectedProject.name
      : 'Analysis Projects',
    icon: Brain,
    breadcrumbs: navigationState.currentView === 'project' && navigationState.selectedProject
      ? [
          { label: 'Analysis Projects', onClick: () => handleNavigateBack() },
          { label: navigationState.selectedProject.name },
        ]
      : [{ label: 'Analysis Projects' }],
  }), [navigationState.currentView, navigationState.selectedProject, handleNavigateBack]);

  usePageTitle(pageConfig);

  // Project CRUD handlers
  const handleCreateProject = useCallback(async () => {
    setIsCreatingProject(true);
    try {
      // Create project with default name
      const defaultName = `Analysis Project ${analysisProjects.length + 1}`;
      const newProject: AnalysisProject = {
        id: `analysis-${Date.now()}`,
        name: defaultName,
        description: '',
        status: 'draft',
        createdAt: new Date(),
        updatedAt: new Date(),
        stepCount: 0,
      };

      setAnalysisProjects(prev => [...prev, newProject]);

      // Immediately navigate to the new project
      handleSelectProject(newProject);
    } catch (error) {
      console.error('Failed to create analysis project:', error);
    } finally {
      setIsCreatingProject(false);
    }
  }, [analysisProjects.length, handleSelectProject]);

  const handleUpdateProject = useCallback(async (projectId: string, updates: Partial<AnalysisProject>) => {
    try {
      setAnalysisProjects(prev => prev.map(p =>
        p.id === projectId ? { ...p, ...updates, updatedAt: new Date() } : p
      ));

      // Update the selected project if it's the one being edited
      if (navigationState.selectedProject?.id === projectId) {
        setNavigationState(prev => ({
          ...prev,
          selectedProject: prev.selectedProject ? { ...prev.selectedProject, ...updates, updatedAt: new Date() } : null,
          breadcrumbs: [
            { label: 'Analysis Projects', onClick: () => handleNavigateBack() },
            { label: updates.name || prev.selectedProject?.name || '' },
          ],
        }));
      }
    } catch (error) {
      console.error('Failed to update analysis project:', error);
    }
  }, [navigationState.selectedProject, handleNavigateBack]);

  const handleDeleteProject = useCallback(async (projectId: string) => {
    if (!confirm('Are you sure you want to delete this analysis project? This action cannot be undone.')) {
      return;
    }

    try {
      setAnalysisProjects(prev => prev.filter(p => p.id !== projectId));

      // If we're currently viewing the deleted project, navigate back
      if (navigationState.selectedProject?.id === projectId) {
        handleNavigateBack();
      }
    } catch (error) {
      console.error('Failed to delete analysis project:', error);
    }
  }, [navigationState.selectedProject, handleNavigateBack]);

  // Update page actions when navigation state changes
  useEffect(() => {
    const isListView = navigationState.currentView === 'list';
    const isProjectView = navigationState.currentView === 'project' && navigationState.selectedProject;
    
    setPageActions({
      // Show New Analysis button only on list view
      onCreateAnalysis: isListView ? handleCreateProject : undefined,
      isCreatingAnalysis: isListView ? isCreatingProject : false,
    });
  }, [navigationState.currentView, navigationState.selectedProject, setPageActions, handleCreateProject, isCreatingProject]);

  return (
    <div
      className="min-h-screen"
      style={{ backgroundColor: 'var(--sidebar-bg)' }}
    >
      <div className="container mx-auto p-6 space-y-6">

        {/* Main Content */}
        {navigationState.currentView === 'list' ? (
          <AnalysisProjectList
            projects={analysisProjects}
            onSelectProject={handleSelectProject}
            onDeleteProject={handleDeleteProject}
            isLoading={isLoadingProjects}
          />
        ) : navigationState.selectedProject ? (
          <AnalysisProjectView
            project={navigationState.selectedProject}
            onUpdateProject={handleUpdateProject}
          />
        ) : null}
      </div>
    </div>
  );
};

export default AIWorkflowsPageContent;