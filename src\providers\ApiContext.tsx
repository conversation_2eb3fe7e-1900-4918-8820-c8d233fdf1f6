"use client";
import React, { createContext, use<PERSON>ontext, ReactNode, useCallback } from 'react';
import axios from 'axios';
import {
  ApiContextType,
  QueryRequest,
  DatabaseConnectionRequestParams,
  DatabaseConnectionResponse,
  ConnectedDatabase,
  LoginRequest,
  RegisterRequest,
  RegisterResponse,
  TokenResponse,
  ChatListItem,
  ApiChatMessage,
  ChatHistoryRequest,
  ReportInfo,
  ListReportsRequest,
  ListReportsResponse,
  DeleteChatRequest,
  ChartType,
} from '@/types';
import { getApiBaseUrl, STORAGE_KEYS, API_ENDPOINTS } from '@/lib/constants';

// Re-export types for backward compatibility
export type {
  QueryRequest,
  DatabaseConnectionRequestParams,
  DatabaseConnectionResponse,
  ConnectedDatabase,
  LoginRequest,
  TokenResponse,
  ChatListItem,
  ApiChatMessage,
  ReportInfo,
  ListReportsRequest,
  ListReportsResponse,
};
const ApiContext = createContext<ApiContextType | undefined>(undefined);

// Mock data generation functions for chart development
const generateMockChartTitle = (prompt: string): string => {
  const keywords = prompt.toLowerCase();
  if (keywords.includes('user') || keywords.includes('registration')) return 'User Registrations Over Time';
  if (keywords.includes('revenue') || keywords.includes('sales')) return 'Revenue Breakdown';
  if (keywords.includes('product') || keywords.includes('category')) return 'Product Performance';
  if (keywords.includes('monthly') || keywords.includes('month')) return 'Monthly Analytics';
  return 'Data Analysis Results';
};

const generateMockChartType = (prompt: string): 'bar' | 'line' | 'pie' | 'area' => {
  const keywords = prompt.toLowerCase();
  if (keywords.includes('breakdown') || keywords.includes('distribution')) return 'pie';
  if (keywords.includes('over time') || keywords.includes('trend')) return 'line';
  if (keywords.includes('comparison') || keywords.includes('vs')) return 'bar';
  return 'bar';
};

const generateMockChartData = (prompt: string) => {
  const chartType = generateMockChartType(prompt);

  if (chartType === 'pie') {
    return [
      { label: 'Product A', value: 35 },
      { label: 'Product B', value: 25 },
      { label: 'Product C', value: 20 },
      { label: 'Product D', value: 20 }
    ];
  }

  return [
    { label: 'Jan', value: 120 },
    { label: 'Feb', value: 150 },
    { label: 'Mar', value: 180 },
    { label: 'Apr', value: 200 },
    { label: 'May', value: 170 },
    { label: 'Jun', value: 220 }
  ];
};

export const ApiProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const queryDatabases = useCallback(async (request: QueryRequest) => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use FormData to match backend expectations
      const formData = new FormData();
      formData.append('query', request.query);
      formData.append('output_format', request.output_format || "excel");
      if (request.session_id) {
        formData.append('session_id', request.session_id);
      }
      if (request.target_databases) {
        formData.append('target_databases', JSON.stringify(request.target_databases));
      }
      if (request.target_tables) {
        formData.append('target_tables', JSON.stringify(request.target_tables));
      }
      if (request.target_columns) {
        formData.append('target_columns', JSON.stringify(request.target_columns));
      }
      // Add streaming support - this was the missing piece!
      if (request.enable_token_streaming) {
        formData.append('enable_token_streaming', 'true');
      }

      const headers: Record<string, string> = {
        'Authorization': `Bearer ${token}`,
        // Don't set Content-Type - let axios set it with boundary for FormData
      };

      const response = await axios.post(`${getApiBaseUrl()}/ask/question`, formData, { headers });

      return response.data;
    } catch (error) {
      console.error("API error (queryDatabases):", error);
      throw error;
    }
  }, []);

  const getDatabaseSchema = useCallback(async (dbId: string) => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axios.post(`${getApiBaseUrl()}/databases/schema`, { db_id: dbId }, { headers });
      return response.data;
    } catch (error) {
      console.error("Error fetching database schema:", error);
      throw error;
    }
  }, []);

  const listDatabases = useCallback(async (): Promise<ConnectedDatabase[]> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        console.log('No access token found, skipping database list request');
        return [];
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      const response = await axios.post(`${getApiBaseUrl()}/databases/listdatabases`, {}, { headers });
      return response.data.databases || response.data || [];
    } catch (error) {
      console.error("Error listing databases:", error);
      throw error;
    }
  }, []);

  const connectNewDatabase = useCallback(async (params: DatabaseConnectionRequestParams): Promise<DatabaseConnectionResponse> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      const response = await axios.post<DatabaseConnectionResponse>(
        `${getApiBaseUrl()}/databases/connectdatabase`,
        params,
        { headers }
      );
      return response.data;
    } catch (error) {
      console.error("Error connecting new database:", error);
      throw error;
    }
  }, []);

  const disconnectExistingDatabase = useCallback(async (dbId: string): Promise<{ message: string }> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axios.post<{ message: string }>(`${getApiBaseUrl()}/databases/disconnectdatabase`, { db_id: dbId }, { headers });
      return response.data;
    } catch (error) {
      console.error("Error disconnecting database:", error);
      throw error;
    }
  }, []);

  const askQuery = useCallback(async (
    query: string,
    outputFormat: string,
    conversationHistory: any[],
    targetDatabases?: string[],
    targetTables?: string[],
    targetColumns?: string[]
  ): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await axios.post(`${getApiBaseUrl()}/query/ask`, {
        query,
        output_format: outputFormat,
        target_databases: targetDatabases,
        target_tables: targetTables,
        target_columns: targetColumns,
      }, { headers });
      return response.data;
    } catch (error) {
      console.error("Error asking query:", error);
      throw error;
    }
  }, []);

  const loginUser = useCallback(async (credentials: LoginRequest): Promise<TokenResponse> => {
    try {
      const params = new URLSearchParams();
      params.append('username', credentials.username);
      params.append('password', credentials.password);

      const response = await axios.post<TokenResponse>(
        `${getApiBaseUrl()}/auth/login`,
        params,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );
      if (response.data.access_token) {
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, response.data.access_token);
        console.log('Access token set in localStorage:', response.data.access_token);
        localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, response.data.token_type);
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.refresh_token);
        localStorage.setItem(STORAGE_KEYS.USER_ID, response.data.user_id);
        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, response.data.expires_at);
      }
      return response.data;
    } catch (error) {
      console.error("Error during login:", error);
      throw error;
    }
  }, []);

  const registerUser = useCallback(async (credentials: RegisterRequest): Promise<RegisterResponse> => {
    try {
      const response = await axios.post<RegisterResponse>(
        `${getApiBaseUrl()}${API_ENDPOINTS.AUTH.REGISTER}`,
        {
          email: credentials.email,
          password: credentials.password,
          full_name: credentials.full_name,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error("Error registering user:", error);
      throw error;
    }
  }, []);

  const refreshToken = useCallback(async (): Promise<TokenResponse> => {
    try {
      const refreshTokenValue = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);

      if (!refreshTokenValue) {
        throw new Error('No refresh token found');
      }

      const response = await axios.post<TokenResponse>(
        `${getApiBaseUrl()}/auth/refresh`,
        { refresh_token: refreshTokenValue },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.access_token) {
        localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, response.data.access_token);
        localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, response.data.token_type);
        localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, response.data.refresh_token);
        localStorage.setItem(STORAGE_KEYS.USER_ID, response.data.user_id);
        localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, response.data.expires_at);
        console.log('Token refreshed successfully');
      }

      return response.data;
    } catch (error) {
      console.error("Error refreshing token:", error);
      // Clear stored tokens on refresh failure
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_ID);
      localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
      localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
      throw error;
    }
  }, []);

  const logoutUser = useCallback(async (): Promise<void> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (token) {
        await axios.post(
          `${getApiBaseUrl()}/auth/logout`,
          {},
          {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          }
        );
      }
    } catch (error) {
      console.error("Error during logout:", error);
      // Continue with local cleanup even if backend call fails
    } finally {
      // Always clear local storage
      localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER_ID);
      localStorage.removeItem(STORAGE_KEYS.EXPIRES_AT);
      localStorage.removeItem(STORAGE_KEYS.TOKEN_TYPE);
      console.log('User logged out and tokens cleared');
    }
  }, []);

  const getUserProfile = useCallback(async (): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.post(`${getApiBaseUrl()}/auth/me`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error fetching user profile:", error);
      throw error;
    }
  }, []);

  const listUserChats = useCallback(async (): Promise<ChatListItem[]> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        console.log('No access token found, skipping chat list request');
        return [];
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      const apiUrl = `${getApiBaseUrl()}/chats/listchats`;
      console.log('🔗 Making request to:', apiUrl);
      console.log('🔑 Using Bearer token:', token.substring(0, 20) + '...');

      const response = await axios.post(apiUrl, {}, { headers });
      console.log('✅ List chats response received:', response.status);
      return response.data || [];
    } catch (error) {
      console.error("❌ Error listing user chats:", error);
      if (axios.isAxiosError(error)) {
        console.error("📍 Request URL:", error.config?.url);
        console.error("📋 Request headers:", error.config?.headers);
        if (error.response) {
          console.error("🚨 Response status:", error.response.status);
          console.error("🚨 Response data:", error.response.data);
          console.error("🚨 Response headers:", error.response.headers);
        } else if (error.request) {
          console.error("🌐 Network error - no response received:", error.request);
        }
      }
      throw error;
    }
  }, []);

  const getChatHistory = useCallback(async (sessionId: string): Promise<ApiChatMessage[]> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      
      if (!token) {
        console.log('No access token found, skipping chat history request');
        return [];
      }
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      const requestBody = { session_id: sessionId };
      const response = await axios.post(`${getApiBaseUrl()}/chats/getchathistory`, requestBody, { headers });
      return response.data || [];
    } catch (error) {
      console.error("Error getting chat history:", error);
      if (axios.isAxiosError(error) && error.response) {
        console.error("Chat history error response:", error.response.data);
        console.error("Chat history error status:", error.response.status);
      }
      throw error;
    }
  }, []);

  const listUserReports = useCallback(async (request: ListReportsRequest = {}): Promise<ListReportsResponse> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      
      if (!token) {
        console.log('No access token found, skipping reports list request');
        return { reports: [], total_count: 0 };
      }
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      const response = await axios.post(`${getApiBaseUrl()}/reports/listreports`, request, { headers });
      return response.data;
    } catch (error) {
      console.error("Error listing user reports:", error);
      if (axios.isAxiosError(error) && error.response) {
        console.error("List reports error response:", error.response.data);
        console.error("List reports error status:", error.response.status);
      }
      throw error;
    }
  }, []);

  const deleteChat = useCallback(async (sessionId: string): Promise<{ message: string }> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      
      if (!token) {
        throw new Error('No authentication token found');
      }
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      const response = await axios.post<{ message: string }>(`${getApiBaseUrl()}/chats/deletechat`, { session_id: sessionId }, { headers });
      return response.data;
    } catch (error) {
      console.error("Error deleting chat:", error);
      throw error;
    }
  }, []);

  // Profile and Settings API methods
  const updateUserProfile = useCallback(async (data: any): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.put(`${getApiBaseUrl()}/auth/profile`, data, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  }, []);

  const changePassword = useCallback(async (data: any): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.post(`${getApiBaseUrl()}/auth/change-password`, data, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error changing password:", error);
      throw error;
    }
  }, []);

  const saveEmailPreferences = useCallback(async (data: any): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.put(`${getApiBaseUrl()}/auth/email-preferences`, data, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error saving email preferences:", error);
      throw error;
    }
  }, []);

  const savePrivacySettings = useCallback(async (data: any): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.put(`${getApiBaseUrl()}/auth/privacy-settings`, data, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error saving privacy settings:", error);
      throw error;
    }
  }, []);

  const exportUserData = useCallback(async (): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.post(`${getApiBaseUrl()}/auth/export-data`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error exporting user data:", error);
      throw error;
    }
  }, []);

  const deleteAccount = useCallback(async (): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await axios.delete(`${getApiBaseUrl()}/auth/account`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    } catch (error) {
      console.error("Error deleting account:", error);
      throw error;
    }
  }, []);

  const completeOnboarding = useCallback(async (): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Check if we're in development mode and the endpoint doesn't exist
      const isDevelopment = process.env.NODE_ENV === 'development';

      try {
        const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.AUTH.COMPLETE_ONBOARDING}`, {}, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        return response.data;
      } catch (error: any) {
        // If it's a 404 error and we're in development, mock the response
        if (error.response?.status === 404 && isDevelopment) {
          console.warn(`⚠️ Backend endpoint ${API_ENDPOINTS.AUTH.COMPLETE_ONBOARDING} not implemented yet. Using mock response for development.`);

          // Simulate a successful response
          await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay

          return {
            success: true,
            message: 'Onboarding completed successfully (mocked)',
            user: {
              is_new_user: false,
              onboarding_completed_at: new Date().toISOString(),
            }
          };
        }

        // Re-throw the error if it's not a 404 or not in development
        throw error;
      }
    } catch (error) {
      console.error("Error completing onboarding:", error);
      throw error;
    }
  }, []);

  const queryChart = useCallback(async (request: { prompt: string; user_id?: string; dashboard_id?: string }): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      // Mock response for development - replace with actual API call
      const mockResponse = {
        success: true,
        data: {
          title: generateMockChartTitle(request.prompt),
          chartType: generateMockChartType(request.prompt),
          data: generateMockChartData(request.prompt),
          metadata: {
            xAxisLabel: 'Time Period',
            yAxisLabel: 'Count',
            colors: ['#8884d8', '#82ca9d', '#ffc658', '#ff7300']
          }
        }
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));

      return mockResponse;

      // Uncomment when backend is ready:
      // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.CHART.QUERY}`, request, { headers });
      // return response.data;
    } catch (error) {
      console.error("Error querying chart:", error);
      throw error;
    }
  }, []);

  // Dashboard management methods
  const listDashboards = useCallback(async (): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Return an empty list in development until the user creates dashboards
      const mockDashboards: any[] = [];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

      return {
        success: true,
        data: mockDashboards,
      };

      // Uncomment when backend is ready:
      // const headers: Record<string, string> = {
      //   'Content-Type': 'application/json',
      //   'Authorization': `Bearer ${token}`,
      // };
      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.LIST}`, { headers });
      // return response.data;
    } catch (error) {
      console.error("Error listing dashboards:", error);
      throw error;
    }
  }, []);

  const createDashboard = useCallback(async (request: any): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Mock response for development
      const newDashboard = {
        id: `dashboard-${Date.now()}`,
        name: request.name,
        description: request.description,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'user-1',
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

      return {
        success: true,
        data: newDashboard,
      };

      // Uncomment when backend is ready:
      // const headers: Record<string, string> = {
      //   'Content-Type': 'application/json',
      //   'Authorization': `Bearer ${token}`,
      // };
      // const response = await axios.post(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.CREATE}`, request, { headers });
      // return response.data;
    } catch (error) {
      console.error("Error creating dashboard:", error);
      throw error;
    }
  }, []);

  const getDashboard = useCallback(async (dashboardId: string): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Mock response for development
      const mockDashboard = {
        id: dashboardId,
        name: 'Sample Dashboard',
        description: 'A sample dashboard with charts',
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        user_id: 'user-1',
        widgets: [], // Empty widgets array for now
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

      return {
        success: true,
        data: mockDashboard,
      };

      // Uncomment when backend is ready:
      // const headers: Record<string, string> = {
      //   'Content-Type': 'application/json',
      //   'Authorization': `Bearer ${token}`,
      // };
      // const response = await axios.get(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.GET}/${dashboardId}`, { headers });
      // return response.data;
    } catch (error) {
      console.error("Error getting dashboard:", error);
      throw error;
    }
  }, []);

  const updateDashboard = useCallback(async (dashboardId: string, request: any): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Mock response for development
      const updatedDashboard = {
        id: dashboardId,
        name: request.name || 'Updated Dashboard',
        description: request.description || 'Updated description',
        created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date().toISOString(),
        user_id: 'user-1',
      };

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

      return {
        success: true,
        data: updatedDashboard,
      };

      // Uncomment when backend is ready:
      // const headers: Record<string, string> = {
      //   'Content-Type': 'application/json',
      //   'Authorization': `Bearer ${token}`,
      // };
      // const response = await axios.put(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.UPDATE}/${dashboardId}`, request, { headers });
      // return response.data;
    } catch (error) {
      console.error("Error updating dashboard:", error);
      throw error;
    }
  }, []);

  const deleteDashboard = useCallback(async (dashboardId: string): Promise<any> => {
    try {
      const token = localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, Math.random() * 500 + 200));

      return {
        success: true,
        message: 'Dashboard deleted successfully',
      };

      // Uncomment when backend is ready:
      // const headers: Record<string, string> = {
      //   'Content-Type': 'application/json',
      //   'Authorization': `Bearer ${token}`,
      // };
      // const response = await axios.delete(`${getApiBaseUrl()}${API_ENDPOINTS.DASHBOARD.DELETE}/${dashboardId}`, { headers });
      // return response.data;
    } catch (error) {
      console.error("Error deleting dashboard:", error);
      throw error;
    }
  }, []);

  return (
    <ApiContext.Provider value={{
      queryDatabases,
      getDatabaseSchema,
      listDatabases,
      connectNewDatabase,
      disconnectExistingDatabase,
      askQuery,
      loginUser,
      registerUser,
      refreshToken,
      logoutUser,
      getUserProfile,
      updateUserProfile,
      changePassword,
      saveEmailPreferences,
      savePrivacySettings,
      exportUserData,
      deleteAccount,
      listUserChats,
      getChatHistory,
      listUserReports,
      deleteChat,
      completeOnboarding,
      queryChart,
      listDashboards,
      createDashboard,
      getDashboard,
      updateDashboard,
      deleteDashboard,
    }}>
      {children}
    </ApiContext.Provider>
  );
};

export const useApi = () => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};