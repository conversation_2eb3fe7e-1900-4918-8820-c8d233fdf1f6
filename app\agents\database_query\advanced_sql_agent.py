"""Advanced SQL Generation & Validation Agent

This module provides sophisticated SQL generation capabilities including:
- Multi-stage SQL generation with validation loops
- Query optimization and performance analysis
- Comprehensive safety and syntax validation
- Iterative refinement based on execution feedback
- ReAct framework integration for intelligent decision making
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, AsyncGenerator, <PERSON><PERSON>
from datetime import datetime
from enum import Enum

from app.agents.base import Agent, AgentResponse
from app.utils.bedrock_client import BedrockClient
from app.services.database_service import DatabaseService
from app.utils.error_utils import friendly_agent_errors

logger = logging.getLogger(__name__)


class SQLGenerationStage(Enum):
    """Stages of SQL generation process."""
    ANALYSIS = "analysis"
    INITIAL_GENERATION = "initial_generation"
    SYNTAX_VALIDATION = "syntax_validation"
    SAFETY_VALIDATION = "safety_validation"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    EXECUTION_VALIDATION = "execution_validation"
    REFINEMENT = "refinement"
    FINALIZATION = "finalization"


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""
    CRITICAL = "critical"  # Blocks execution
    HIGH = "high"         # Should be fixed
    MEDIUM = "medium"     # Recommended to fix
    LOW = "low"          # Optional improvement


class AdvancedSQLAgent(Agent):
    """Advanced SQL generation agent with multi-stage validation and optimization."""
    
    def __init__(self, database_service: DatabaseService, agent_id: str = None):
        self.agent_id = agent_id or "advanced_sql_agent"
        self.database_service = database_service
        self.bedrock_client = BedrockClient()
        self.initialized = False
        
        # Generation state tracking
        self.generation_history = []
        self.validation_cache = {}
        
        # Configuration
        self.max_refinement_iterations = 3
        self.performance_threshold = 0.8
        self.safety_threshold = 1.0  # Must be perfect for safety
        
    async def initialize(self) -> None:
        """Initialize the agent."""
        if self.initialized:
            return
        self.initialized = True
        logger.info("✅ Advanced SQL Agent initialized")
    
    @friendly_agent_errors("advanced-sql")
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a request for advanced SQL generation.
        
        Expected message format:
        {
            "query": "enhanced natural language question",
            "query_analysis": {...},  # From QueryUnderstandingAgent
            "database_infos": [...],  # Database schema information
            "output_format": "csv|excel|json",
            "user_id": "user_123",
            "session_id": "session_456",
            "generation_mode": "standard|optimized|safe",
            "validation_level": "basic|comprehensive|strict"
        }
        
        Returns comprehensive SQL generation results with validation details.
        """
        if not self.initialized:
            await self.initialize()
            
        # Extract parameters
        query = message.get("query", "").strip()
        query_analysis = message.get("query_analysis", {})
        database_infos = message.get("database_infos", [])
        output_format = message.get("output_format", "csv")
        user_id = message.get("user_id", "")
        session_id = message.get("session_id", "")
        generation_mode = message.get("generation_mode", "standard")
        validation_level = message.get("validation_level", "comprehensive")
        
        if not query:
            return AgentResponse(
                self.agent_id, False, error="No query provided"
            ).to_dict()
        
        if not database_infos:
            return AgentResponse(
                self.agent_id, False, error="No database information provided"
            ).to_dict()
        
        try:
            # Process each database
            results = []
            for db_info in database_infos:
                if not db_info.get("tables"):
                    continue
                    
                db_result = await self._process_database(
                    query, query_analysis, db_info, generation_mode, validation_level
                )
                results.append(db_result)
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=len(results) > 0,
                data={
                    "query": query,
                    "query_analysis": query_analysis,
                    "generation_mode": generation_mode,
                    "validation_level": validation_level,
                    "database_results": results,
                    "summary": self._create_generation_summary(results),
                    "metadata": {
                        "processing_time": datetime.utcnow().isoformat(),
                        "total_databases": len(database_infos),
                        "successful_generations": len([r for r in results if r.get("success", False)])
                    }
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Advanced SQL generation failed: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"SQL generation failed: {str(e)}"
            ).to_dict()
    
    @friendly_agent_errors("advanced-sql-stream")
    async def process_stream(self, message: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Process with streaming support for real-time feedback."""
        
        def create_event(event_type: str, stage: str, data: Any) -> Dict[str, Any]:
            return {
                "type": event_type,
                "agent": self.agent_id,
                "stage": stage,
                "data": data,
                "timestamp": datetime.utcnow().isoformat() + "Z"
            }
        
        if not self.initialized:
            await self.initialize()
            
        # Extract parameters
        query = message.get("query", "").strip()
        query_analysis = message.get("query_analysis", {})
        database_infos = message.get("database_infos", [])
        generation_mode = message.get("generation_mode", "standard")
        validation_level = message.get("validation_level", "comprehensive")
        
        if not query or not database_infos:
            yield create_event("error", "initialization", {
                "message": "Missing required parameters",
                "status": "failed"
            })
            return
        
        try:
            yield create_event("stage_start", "initialization", {
                "message": "🚀 Starting advanced SQL generation...",
                "databases": len(database_infos),
                "mode": generation_mode
            })
            
            results = []
            for i, db_info in enumerate(database_infos, 1):
                if not db_info.get("tables"):
                    continue
                
                db_name = db_info.get("database_name", f"Database {i}")
                yield create_event("database_start", "processing", {
                    "message": f"🔧 Processing {db_name}...",
                    "database": db_name,
                    "progress": f"{i}/{len(database_infos)}"
                })
                
                # Stream the database processing
                async for event in self._process_database_stream(
                    query, query_analysis, db_info, generation_mode, validation_level, create_event
                ):
                    yield event
                
                # Get final result for this database
                db_result = await self._process_database(
                    query, query_analysis, db_info, generation_mode, validation_level
                )
                results.append(db_result)
            
            # Final summary
            yield create_event("completion", "finalization", {
                "message": "✅ Advanced SQL generation complete",
                "summary": self._create_generation_summary(results),
                "total_databases": len(database_infos),
                "successful_generations": len([r for r in results if r.get("success", False)])
            })
            
        except Exception as e:
            logger.error(f"Streaming SQL generation failed: {str(e)}")
            yield create_event("error", "processing", {
                "message": f"❌ Generation failed: {str(e)}",
                "status": "failed"
            })
    
    async def _process_database(
        self,
        query: str,
        query_analysis: Dict[str, Any],
        db_info: Dict[str, Any],
        generation_mode: str,
        validation_level: str
    ) -> Dict[str, Any]:
        """Process SQL generation for a single database."""
        
        db_id = db_info.get("database_id", "unknown")
        db_name = db_info.get("database_name", "Unknown Database")
        db_type = db_info.get("database_type", "postgresql")
        tables = db_info.get("tables", [])
        
        generation_state = {
            "query": query,
            "query_analysis": query_analysis,
            "database_id": db_id,
            "database_name": db_name,
            "database_type": db_type,
            "tables": tables,
            "generation_mode": generation_mode,
            "validation_level": validation_level,
            "current_stage": SQLGenerationStage.ANALYSIS,
            "iterations": 0,
            "sql_candidates": [],
            "validation_results": [],
            "performance_metrics": {},
            "final_sql": None,
            "success": False,
            "error": None
        }
        
        try:
            # Multi-stage SQL generation process
            await self._stage_analysis(generation_state)
            await self._stage_initial_generation(generation_state)
            await self._stage_syntax_validation(generation_state)
            await self._stage_safety_validation(generation_state)
            await self._stage_performance_optimization(generation_state)
            await self._stage_execution_validation(generation_state)
            await self._stage_refinement(generation_state)
            await self._stage_finalization(generation_state)
            
            return {
                "database_id": db_id,
                "database_name": db_name,
                "database_type": db_type,
                "success": generation_state["success"],
                "final_sql": generation_state["final_sql"],
                "validation_results": generation_state["validation_results"],
                "performance_metrics": generation_state["performance_metrics"],
                "iterations": generation_state["iterations"],
                "error": generation_state["error"],
                "metadata": {
                    "generation_mode": generation_mode,
                    "validation_level": validation_level,
                    "stages_completed": generation_state["current_stage"].value,
                    "sql_candidates_generated": len(generation_state["sql_candidates"])
                }
            }
            
        except Exception as e:
            logger.error(f"Database processing failed for {db_id}: {str(e)}")
            return {
                "database_id": db_id,
                "database_name": db_name,
                "database_type": db_type,
                "success": False,
                "error": str(e),
                "final_sql": None,
                "validation_results": [],
                "performance_metrics": {},
                "iterations": 0
            }

    async def _stage_analysis(self, state: Dict[str, Any]) -> None:
        """Stage 1: Analyze the query and database schema for optimal generation strategy."""
        state["current_stage"] = SQLGenerationStage.ANALYSIS

        query = state["query"]
        query_analysis = state["query_analysis"]
        tables = state["tables"]
        db_type = state["database_type"]

        # Analyze query complexity and requirements
        complexity = query_analysis.get("complexity", "simple")
        intent = query_analysis.get("intent", "operational")
        entities = query_analysis.get("entities", [])

        # Determine generation strategy based on analysis
        if complexity in ["complex", "very_complex"]:
            state["generation_strategy"] = "multi_step"
            state["requires_optimization"] = True
        elif intent == "analytical":
            state["generation_strategy"] = "analytical_focused"
            state["requires_aggregation"] = True
        else:
            state["generation_strategy"] = "standard"
            state["requires_optimization"] = False

        # Identify relevant tables and relationships
        relevant_tables = self._identify_relevant_tables(query, entities, tables)
        state["relevant_tables"] = relevant_tables

        # Plan join strategy if multiple tables needed
        if len(relevant_tables) > 1:
            state["join_strategy"] = self._plan_join_strategy(relevant_tables)

        logger.info(f"Analysis complete: strategy={state.get('generation_strategy')}, tables={len(relevant_tables)}")

    async def _stage_initial_generation(self, state: Dict[str, Any]) -> None:
        """Stage 2: Generate initial SQL candidates using different approaches."""
        state["current_stage"] = SQLGenerationStage.INITIAL_GENERATION

        query = state["query"]
        query_analysis = state["query_analysis"]
        db_type = state["database_type"]
        relevant_tables = state["relevant_tables"]
        generation_strategy = state.get("generation_strategy", "standard")

        # Generate multiple SQL candidates using different approaches
        candidates = []

        # Approach 1: Direct generation
        direct_sql = await self._generate_direct_sql(query, db_type, relevant_tables)
        if direct_sql:
            # Apply performance optimizations to prevent timeouts
            direct_sql = self._optimize_query_for_performance(direct_sql, db_type)
            candidates.append({
                "approach": "direct",
                "sql": direct_sql,
                "confidence": 0.8,
                "reasoning": "Direct translation from natural language with performance optimizations"
            })

        # Approach 2: Template-based generation (for common patterns)
        if query_analysis.get("intent") in ["analytical", "reporting"]:
            template_sql = await self._generate_template_based_sql(query, query_analysis, db_type, relevant_tables)
            if template_sql:
                # Apply performance optimizations to prevent timeouts
                template_sql = self._optimize_query_for_performance(template_sql, db_type)
                candidates.append({
                    "approach": "template",
                    "sql": template_sql,
                    "confidence": 0.9,
                    "reasoning": "Template-based generation for analytical queries with performance optimizations"
                })

        # Approach 3: Optimized generation (for complex queries)
        if generation_strategy in ["multi_step", "analytical_focused"]:
            optimized_sql = await self._generate_optimized_sql(query, query_analysis, db_type, relevant_tables)
            if optimized_sql:
                # Apply additional performance optimizations to prevent timeouts
                optimized_sql = self._optimize_query_for_performance(optimized_sql, db_type)
                candidates.append({
                    "approach": "optimized",
                    "sql": optimized_sql,
                    "confidence": 0.95,
                    "reasoning": "Optimized generation for complex analytical queries with timeout prevention"
                })

        state["sql_candidates"] = candidates
        logger.info(f"Generated {len(candidates)} SQL candidates")

    async def _stage_syntax_validation(self, state: Dict[str, Any]) -> None:
        """Stage 3: Validate SQL syntax and basic structure."""
        state["current_stage"] = SQLGenerationStage.SYNTAX_VALIDATION

        db_type = state["database_type"]
        candidates = state["sql_candidates"]
        validation_results = []

        for candidate in candidates:
            sql = candidate["sql"]
            validation_result = {
                "approach": candidate["approach"],
                "sql": sql,
                "syntax_valid": True,
                "syntax_issues": [],
                "structure_score": 0.0
            }

            # Basic syntax validation
            syntax_issues = self._validate_sql_syntax(sql, db_type)
            validation_result["syntax_issues"] = syntax_issues
            validation_result["syntax_valid"] = len(syntax_issues) == 0

            # Structure quality assessment
            structure_score = self._assess_sql_structure(sql, db_type)
            validation_result["structure_score"] = structure_score

            validation_results.append(validation_result)

        # Filter out candidates with critical syntax issues
        valid_candidates = [
            candidate for candidate, validation in zip(candidates, validation_results)
            if validation["syntax_valid"]
        ]

        state["sql_candidates"] = valid_candidates
        state["syntax_validation_results"] = validation_results

        logger.info(f"Syntax validation: {len(valid_candidates)}/{len(candidates)} candidates passed")

    async def _stage_safety_validation(self, state: Dict[str, Any]) -> None:
        """Stage 4: Comprehensive safety validation."""
        state["current_stage"] = SQLGenerationStage.SAFETY_VALIDATION

        candidates = state["sql_candidates"]
        safety_results = []

        for candidate in candidates:
            sql = candidate["sql"]
            safety_result = {
                "approach": candidate["approach"],
                "sql": sql,
                "is_safe": True,
                "safety_issues": [],
                "risk_level": "low"
            }

            # Comprehensive safety checks
            safety_issues = self._comprehensive_safety_check(sql)
            safety_result["safety_issues"] = safety_issues

            # Determine risk level
            critical_issues = [issue for issue in safety_issues if issue.get("severity") == "critical"]
            high_issues = [issue for issue in safety_issues if issue.get("severity") == "high"]

            if critical_issues:
                safety_result["is_safe"] = False
                safety_result["risk_level"] = "critical"
            elif high_issues:
                safety_result["is_safe"] = False
                safety_result["risk_level"] = "high"
            elif safety_issues:
                safety_result["risk_level"] = "medium"

            safety_results.append(safety_result)

        # Filter out unsafe candidates
        safe_candidates = [
            candidate for candidate, safety in zip(candidates, safety_results)
            if safety["is_safe"]
        ]

        state["sql_candidates"] = safe_candidates
        state["safety_validation_results"] = safety_results

        logger.info(f"Safety validation: {len(safe_candidates)}/{len(candidates)} candidates are safe")

    async def _stage_performance_optimization(self, state: Dict[str, Any]) -> None:
        """Stage 5: Optimize SQL for performance."""
        state["current_stage"] = SQLGenerationStage.PERFORMANCE_OPTIMIZATION

        candidates = state["sql_candidates"]
        db_type = state["database_type"]
        relevant_tables = state["relevant_tables"]
        optimization_results = []

        for candidate in candidates:
            sql = candidate["sql"]

            # Analyze performance characteristics
            performance_analysis = self._analyze_query_performance(sql, db_type, relevant_tables)

            # Apply optimizations if needed
            optimized_sql = sql
            optimizations_applied = []

            if performance_analysis.get("needs_optimization", False):
                optimized_sql, optimizations = await self._apply_performance_optimizations(
                    sql, db_type, performance_analysis
                )
                optimizations_applied = optimizations

            optimization_result = {
                "approach": candidate["approach"],
                "original_sql": sql,
                "optimized_sql": optimized_sql,
                "performance_score": performance_analysis.get("score", 0.5),
                "optimizations_applied": optimizations_applied,
                "estimated_improvement": performance_analysis.get("improvement_potential", 0.0)
            }

            # Update candidate with optimized SQL
            candidate["sql"] = optimized_sql
            candidate["performance_score"] = performance_analysis.get("score", 0.5)

            optimization_results.append(optimization_result)

        state["performance_optimization_results"] = optimization_results
        logger.info(f"Performance optimization: {len(optimization_results)} candidates optimized")

    async def _stage_execution_validation(self, state: Dict[str, Any]) -> None:
        """Stage 6: Validate SQL by attempting execution (dry run)."""
        state["current_stage"] = SQLGenerationStage.EXECUTION_VALIDATION

        candidates = state["sql_candidates"]
        db_id = state["database_id"]
        execution_results = []

        for candidate in candidates:
            sql = candidate["sql"]
            execution_result = {
                "approach": candidate["approach"],
                "sql": sql,
                "execution_valid": False,
                "execution_error": None,
                "result_preview": None,
                "execution_time": None
            }

            try:
                # Attempt dry run execution (with LIMIT to avoid large results)
                limited_sql = self._add_execution_limit(sql)
                start_time = datetime.utcnow()

                result = await self.database_service.execute_query(db_id, limited_sql)

                execution_time = (datetime.utcnow() - start_time).total_seconds()

                execution_result.update({
                    "execution_valid": True,
                    "result_preview": self._create_result_preview(result),
                    "execution_time": execution_time
                })

                # Update candidate confidence based on successful execution
                candidate["confidence"] = min(1.0, candidate.get("confidence", 0.5) + 0.2)

            except Exception as e:
                execution_result["execution_error"] = str(e)
                # Reduce candidate confidence for execution failures
                candidate["confidence"] = max(0.1, candidate.get("confidence", 0.5) - 0.3)
                logger.warning(f"Execution validation failed for {candidate['approach']}: {str(e)}")

            execution_results.append(execution_result)

        # Filter candidates that executed successfully
        executable_candidates = [
            candidate for candidate, execution in zip(candidates, execution_results)
            if execution["execution_valid"]
        ]

        state["sql_candidates"] = executable_candidates
        state["execution_validation_results"] = execution_results

        logger.info(f"Execution validation: {len(executable_candidates)}/{len(candidates)} candidates executable")

    async def _stage_refinement(self, state: Dict[str, Any]) -> None:
        """Stage 7: Iterative refinement based on validation results."""
        state["current_stage"] = SQLGenerationStage.REFINEMENT

        candidates = state["sql_candidates"]
        max_iterations = self.max_refinement_iterations

        for iteration in range(max_iterations):
            state["iterations"] = iteration + 1

            if not candidates:
                logger.warning("No candidates available for refinement")
                break

            # Select best candidate for refinement
            best_candidate = max(candidates, key=lambda c: c.get("confidence", 0.0))

            # Check if refinement is needed
            if best_candidate.get("confidence", 0.0) >= 0.9:
                logger.info(f"Refinement complete: best candidate confidence {best_candidate['confidence']:.2f}")
                break

            # Attempt refinement
            refined_sql = await self._refine_sql_candidate(best_candidate, state)

            if refined_sql and refined_sql != best_candidate["sql"]:
                # Create refined candidate
                refined_candidate = {
                    "approach": f"{best_candidate['approach']}_refined_{iteration + 1}",
                    "sql": refined_sql,
                    "confidence": min(1.0, best_candidate.get("confidence", 0.5) + 0.1),
                    "reasoning": f"Refined version of {best_candidate['approach']}"
                }

                # Validate refined candidate
                if await self._quick_validate_candidate(refined_candidate, state):
                    candidates.append(refined_candidate)
                    logger.info(f"Refinement iteration {iteration + 1}: added refined candidate")
                else:
                    logger.warning(f"Refinement iteration {iteration + 1}: refined candidate failed validation")
            else:
                logger.info(f"Refinement iteration {iteration + 1}: no improvement found")
                break

        state["sql_candidates"] = candidates

    async def _stage_finalization(self, state: Dict[str, Any]) -> None:
        """Stage 8: Select final SQL and prepare results."""
        state["current_stage"] = SQLGenerationStage.FINALIZATION

        candidates = state["sql_candidates"]

        if not candidates:
            state["success"] = False
            state["error"] = "No valid SQL candidates generated"
            return

        # Select best candidate based on multiple criteria
        best_candidate = self._select_best_candidate(candidates)

        if best_candidate:
            state["final_sql"] = best_candidate["sql"]
            state["success"] = True
            state["selected_approach"] = best_candidate["approach"]
            state["final_confidence"] = best_candidate.get("confidence", 0.0)

            logger.info(f"Finalization: selected {best_candidate['approach']} with confidence {best_candidate.get('confidence', 0.0):.2f}")
        else:
            state["success"] = False
            state["error"] = "Failed to select best SQL candidate"

    # ────────────────────────────────────────────────────────────────────────
    # Helper Methods for SQL Generation
    # ────────────────────────────────────────────────────────────────────────

    def _identify_relevant_tables(self, query: str, entities: List[str], tables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify tables relevant to the query."""
        relevant_tables = []
        query_lower = query.lower()
        entities_lower = [entity.lower() for entity in entities]

        for table in tables:
            table_name = table.get("name", "").lower()
            columns = [col.get("name", "").lower() for col in table.get("columns", [])]

            # Check if table name matches entities or appears in query
            if (table_name in query_lower or
                any(entity in table_name for entity in entities_lower) or
                any(entity in query_lower and entity in table_name for entity in entities_lower)):
                relevant_tables.append(table)
                continue

            # Check if any column names are mentioned in query
            if any(col in query_lower for col in columns):
                relevant_tables.append(table)
                continue

        # If no tables found, include all tables (let SQL generation decide)
        if not relevant_tables:
            relevant_tables = tables[:3]  # Limit to first 3 tables to avoid overwhelming

        return relevant_tables

    def _plan_join_strategy(self, tables: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Plan join strategy for multiple tables."""
        join_strategy = {
            "primary_table": None,
            "join_pairs": [],
            "join_type": "INNER"
        }

        if not tables:
            return join_strategy

        # Select primary table (usually the largest or most central)
        primary_table = max(tables, key=lambda t: len(t.get("columns", [])))
        join_strategy["primary_table"] = primary_table["name"]

        # Identify potential join relationships
        for i, table1 in enumerate(tables):
            for table2 in tables[i+1:]:
                join_pair = self._find_join_relationship(table1, table2)
                if join_pair:
                    join_strategy["join_pairs"].append(join_pair)

        return join_strategy

    def _find_join_relationship(self, table1: Dict[str, Any], table2: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find potential join relationship between two tables."""
        table1_cols = [col.get("name", "").lower() for col in table1.get("columns", [])]
        table2_cols = [col.get("name", "").lower() for col in table2.get("columns", [])]

        # Look for common column patterns (id, foreign keys)
        for col1 in table1_cols:
            for col2 in table2_cols:
                if (col1 == col2 or
                    col1 == f"{table2['name'].lower()}_id" or
                    col2 == f"{table1['name'].lower()}_id" or
                    (col1.endswith("_id") and col1[:-3] in table2["name"].lower()) or
                    (col2.endswith("_id") and col2[:-3] in table1["name"].lower())):

                    return {
                        "table1": table1["name"],
                        "table2": table2["name"],
                        "column1": col1,
                        "column2": col2,
                        "confidence": 0.8
                    }

        return None

    async def _generate_direct_sql(self, query: str, db_type: str, tables: List[Dict[str, Any]]) -> Optional[str]:
        """Generate SQL using direct translation approach."""
        from app.prompts.sql import get_sql_generation_prompt

        schema_text = self._format_schema_for_generation(tables)
        system_prompt = get_sql_generation_prompt(db_type)

        user_prompt = f"""Database schema:
{schema_text}

User question: {query}

Generate a {db_type} SQL query that answers the question. Return ONLY the SQL query."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.2
            )

            return self._clean_sql_response(response)
        except Exception as e:
            logger.error(f"Direct SQL generation failed: {e}")
            return None

    async def _generate_template_based_sql(
        self,
        query: str,
        query_analysis: Dict[str, Any],
        db_type: str,
        tables: List[Dict[str, Any]]
    ) -> Optional[str]:
        """Generate SQL using template-based approach for common patterns."""

        intent = query_analysis.get("intent", "")
        entities = query_analysis.get("entities", [])

        # Select appropriate template based on intent
        if intent == "analytical":
            template = self._get_analytical_template(query, entities, tables)
        elif intent == "comparative":
            template = self._get_comparative_template(query, entities, tables)
        elif intent == "reporting":
            template = self._get_reporting_template(query, entities, tables)
        else:
            return None

        if not template:
            return None

        # Fill template with specific values
        try:
            filled_sql = await self._fill_sql_template(template, query, query_analysis, tables, db_type)
            return filled_sql
        except Exception as e:
            logger.error(f"Template-based SQL generation failed: {e}")
            return None

    async def _generate_optimized_sql(
        self,
        query: str,
        query_analysis: Dict[str, Any],
        db_type: str,
        tables: List[Dict[str, Any]]
    ) -> Optional[str]:
        """Generate optimized SQL for complex queries."""

        schema_text = self._format_schema_for_generation(tables)
        complexity = query_analysis.get("complexity", "simple")
        intent = query_analysis.get("intent", "operational")

        system_prompt = f"""You are an expert SQL optimization specialist for {db_type} databases.
Generate highly optimized SQL queries that follow performance best practices.

OPTIMIZATION PRIORITIES:
1. Use appropriate indexes and join strategies
2. Minimize data scanning with efficient WHERE clauses
3. Use CTEs for complex logic organization
4. Apply proper aggregation techniques
5. Optimize for {complexity} complexity and {intent} intent

PERFORMANCE GUIDELINES:
- Use LIMIT clauses for large result sets
- Prefer EXISTS over IN for subqueries
- Use appropriate JOIN types (INNER vs LEFT)
- Consider window functions for analytical queries
- Use proper GROUP BY and HAVING clauses

Return ONLY the optimized SQL query with performance comments."""

        user_prompt = f"""Database schema:
{schema_text}

Query complexity: {complexity}
Query intent: {intent}
User question: {query}

Generate an optimized {db_type} SQL query with performance considerations."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1  # Lower temperature for more consistent optimization
            )

            return self._clean_sql_response(response)
        except Exception as e:
            logger.error(f"Optimized SQL generation failed: {e}")
            return None

    # ────────────────────────────────────────────────────────────────────────
    # Validation Methods
    # ────────────────────────────────────────────────────────────────────────

    def _validate_sql_syntax(self, sql: str, db_type: str) -> List[Dict[str, Any]]:
        """Validate SQL syntax and structure."""
        issues = []
        sql_lower = sql.lower().strip()

        # Basic syntax checks
        if not sql_lower.startswith(('select', 'with')):
            issues.append({
                "type": "syntax",
                "severity": "critical",
                "message": "Query must start with SELECT or WITH",
                "location": "beginning"
            })

        # Check for balanced parentheses
        if sql.count('(') != sql.count(')'):
            issues.append({
                "type": "syntax",
                "severity": "critical",
                "message": "Unbalanced parentheses",
                "location": "throughout"
            })

        # Check for proper semicolon termination
        if sql_lower.endswith(';'):
            # Remove semicolon for further processing
            sql = sql[:-1].strip()

        # Database-specific syntax checks
        if db_type.lower() == "postgresql":
            issues.extend(self._validate_postgresql_syntax(sql))
        elif db_type.lower() == "mysql":
            issues.extend(self._validate_mysql_syntax(sql))

        return issues

    def _assess_sql_structure(self, sql: str, db_type: str) -> float:
        """Assess the structural quality of SQL (0.0 to 1.0)."""
        score = 1.0
        sql_lower = sql.lower()

        # Deduct points for poor practices
        if 'select *' in sql_lower:
            score -= 0.2  # Avoid SELECT *

        if 'where' not in sql_lower and 'limit' not in sql_lower:
            score -= 0.3  # Missing filtering

        if sql_lower.count('join') > 3:
            score -= 0.1  # Too many joins might be inefficient

        # Add points for good practices
        if 'limit' in sql_lower:
            score += 0.1  # Good practice to limit results

        if any(keyword in sql_lower for keyword in ['index', 'explain', 'analyze']):
            score += 0.1  # Performance awareness

        return max(0.0, min(1.0, score))

    def _comprehensive_safety_check(self, sql: str) -> List[Dict[str, Any]]:
        """Comprehensive safety validation."""
        issues = []
        sql_lower = sql.lower().strip()

        # Check for dangerous operations
        dangerous_operations = {
            'drop': 'critical',
            'delete': 'critical',
            'truncate': 'critical',
            'alter': 'high',
            'create': 'high',
            'insert': 'medium',
            'update': 'medium'
        }

        for operation, severity in dangerous_operations.items():
            if operation in sql_lower:
                issues.append({
                    "type": "safety",
                    "severity": severity,
                    "message": f"Contains potentially dangerous operation: {operation.upper()}",
                    "operation": operation
                })

        # Check for SQL injection patterns
        injection_patterns = [
            ("'.*or.*'", "Potential OR-based SQL injection"),
            ("'.*union.*'", "Potential UNION-based SQL injection"),
            ("--;", "SQL comment injection pattern"),
            ("'.*exec.*'", "Potential command execution"),
        ]

        import re
        for pattern, message in injection_patterns:
            if re.search(pattern, sql_lower):
                issues.append({
                    "type": "security",
                    "severity": "critical",
                    "message": message,
                    "pattern": pattern
                })

        # Check for resource-intensive operations
        if not any(keyword in sql_lower for keyword in ['limit', 'top', 'rownum']):
            if any(keyword in sql_lower for keyword in ['join', 'group by', 'order by']):
                issues.append({
                    "type": "performance",
                    "severity": "medium",
                    "message": "Query may return large result set without LIMIT",
                    "recommendation": "Add LIMIT clause"
                })

        return issues

    def _analyze_query_performance(self, sql: str, db_type: str, tables: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze query performance characteristics."""
        analysis = {
            "score": 0.5,
            "needs_optimization": False,
            "improvement_potential": 0.0,
            "bottlenecks": [],
            "recommendations": []
        }

        sql_lower = sql.lower()

        # Analyze join complexity
        join_count = sql_lower.count('join')
        if join_count > 3:
            analysis["bottlenecks"].append("Multiple joins may impact performance")
            analysis["needs_optimization"] = True
            analysis["improvement_potential"] += 0.3

        # Check for missing WHERE clauses
        if 'where' not in sql_lower:
            analysis["bottlenecks"].append("Missing WHERE clause may scan entire table")
            analysis["recommendations"].append("Add appropriate WHERE conditions")
            analysis["improvement_potential"] += 0.4

        # Check for SELECT *
        if 'select *' in sql_lower:
            analysis["bottlenecks"].append("SELECT * retrieves unnecessary columns")
            analysis["recommendations"].append("Specify only required columns")
            analysis["improvement_potential"] += 0.2

        # Calculate overall score
        base_score = 0.8
        penalty = len(analysis["bottlenecks"]) * 0.15
        analysis["score"] = max(0.1, base_score - penalty)

        if analysis["score"] < self.performance_threshold:
            analysis["needs_optimization"] = True

        return analysis

    async def _apply_performance_optimizations(
        self,
        sql: str,
        db_type: str,
        performance_analysis: Dict[str, Any]
    ) -> Tuple[str, List[str]]:
        """Apply performance optimizations to SQL."""
        optimized_sql = sql
        optimizations = []

        # Add LIMIT if missing and query is complex
        if ('limit' not in sql.lower() and
            any(keyword in sql.lower() for keyword in ['join', 'group by', 'order by'])):
            optimized_sql = f"{optimized_sql.rstrip(';')} LIMIT 1000"
            optimizations.append("Added LIMIT clause to prevent large result sets")

        # Optimize SELECT * if present
        if 'select *' in sql.lower():
            # This would require more sophisticated parsing to implement properly
            optimizations.append("Recommend replacing SELECT * with specific columns")

        # Add query hints for specific databases
        if db_type.lower() == "postgresql" and 'join' in sql.lower():
            optimizations.append("Consider adding PostgreSQL-specific join hints")

        return optimized_sql, optimizations

    # ────────────────────────────────────────────────────────────────────────
    # Utility Methods
    # ────────────────────────────────────────────────────────────────────────

    def _add_execution_limit(self, sql: str) -> str:
        """Add execution limit to SQL for safe testing."""
        sql = sql.strip().rstrip(';')
        if 'limit' not in sql.lower():
            return f"{sql} LIMIT 10"
        return sql

    def _optimize_query_for_performance(self, sql: str, db_type: str) -> str:
        """Optimize SQL query to prevent timeouts and improve performance."""
        sql_lower = sql.lower()
        optimized_sql = sql.strip()

        # Add LIMIT if missing for potentially large result sets
        if 'limit' not in sql_lower and any(keyword in sql_lower for keyword in ['join', 'group by', 'order by']):
            optimized_sql = f"{optimized_sql.rstrip(';')} LIMIT 1000"

        # For PostgreSQL, add query hints to prevent timeout
        if db_type.lower() in ['postgresql', 'supabase']:
            # Check if query has multiple JOINs that might cause timeout
            join_count = sql_lower.count('join')
            if join_count > 2:
                # Add comment with optimization hint
                optimized_sql = f"-- Optimized query with performance considerations\n{optimized_sql}"

                # If query contains subqueries in JOIN conditions, suggest optimization
                if 'join (' in sql_lower or 'join(' in sql_lower:
                    logger.warning("Query contains subqueries in JOIN conditions which may cause timeouts")

        return optimized_sql

    def _create_result_preview(self, result: Any) -> Dict[str, Any]:
        """Create a preview of query results."""
        import pandas as pd

        preview = {
            "row_count": 0,
            "column_count": 0,
            "columns": [],
            "sample_rows": []
        }

        try:
            if isinstance(result, pd.DataFrame):
                preview["row_count"] = len(result)
                preview["column_count"] = len(result.columns)
                preview["columns"] = list(result.columns)
                preview["sample_rows"] = result.head(3).to_dict('records')
            elif isinstance(result, list):
                preview["row_count"] = len(result)
                if result:
                    if isinstance(result[0], dict):
                        preview["columns"] = list(result[0].keys())
                        preview["column_count"] = len(preview["columns"])
                    preview["sample_rows"] = result[:3]
        except Exception as e:
            logger.warning(f"Failed to create result preview: {e}")

        return preview

    async def _quick_validate_candidate(self, candidate: Dict[str, Any], state: Dict[str, Any]) -> bool:
        """Quick validation of a refined candidate."""
        sql = candidate["sql"]
        db_type = state["database_type"]

        # Quick syntax check
        syntax_issues = self._validate_sql_syntax(sql, db_type)
        if any(issue["severity"] == "critical" for issue in syntax_issues):
            return False

        # Quick safety check
        safety_issues = self._comprehensive_safety_check(sql)
        if any(issue["severity"] == "critical" for issue in safety_issues):
            return False

        return True

    async def _refine_sql_candidate(self, candidate: Dict[str, Any], state: Dict[str, Any]) -> Optional[str]:
        """Refine a SQL candidate based on validation feedback."""
        sql = candidate["sql"]
        approach = candidate["approach"]

        # Collect validation feedback
        feedback = []

        # Add syntax feedback
        syntax_results = state.get("syntax_validation_results", [])
        for result in syntax_results:
            if result.get("approach") == approach and result.get("syntax_issues"):
                feedback.extend([f"Syntax: {issue['message']}" for issue in result["syntax_issues"]])

        # Add safety feedback
        safety_results = state.get("safety_validation_results", [])
        for result in safety_results:
            if result.get("approach") == approach and result.get("safety_issues"):
                feedback.extend([f"Safety: {issue['message']}" for issue in result["safety_issues"]])

        if not feedback:
            return None

        # Generate refined SQL based on feedback
        system_prompt = f"""You are an expert SQL refinement specialist.
Your task is to improve SQL queries based on validation feedback.

REFINEMENT PRINCIPLES:
1. Address all critical issues first
2. Maintain the original query intent
3. Improve performance and safety
4. Follow database best practices
5. Return ONLY the refined SQL query

Original approach: {approach}
Database type: {state['database_type']}"""

        user_prompt = f"""Original SQL:
{sql}

Validation feedback:
{chr(10).join(feedback)}

Refine this SQL query to address the feedback while maintaining the original intent."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.1
            )

            return self._clean_sql_response(response)
        except Exception as e:
            logger.error(f"SQL refinement failed: {e}")
            return None

    def _select_best_candidate(self, candidates: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Select the best SQL candidate based on multiple criteria."""
        if not candidates:
            return None

        # Score candidates based on multiple factors
        scored_candidates = []

        for candidate in candidates:
            score = 0.0

            # Base confidence score
            confidence = candidate.get("confidence", 0.5)
            score += confidence * 0.4

            # Performance score
            performance_score = candidate.get("performance_score", 0.5)
            score += performance_score * 0.3

            # Approach preference (optimized > template > direct)
            approach = candidate.get("approach", "")
            if "optimized" in approach:
                score += 0.2
            elif "template" in approach:
                score += 0.15
            elif "refined" in approach:
                score += 0.1

            # Execution success bonus
            if candidate.get("execution_valid", False):
                score += 0.1

            scored_candidates.append((candidate, score))

        # Return candidate with highest score
        best_candidate, best_score = max(scored_candidates, key=lambda x: x[1])
        logger.info(f"Selected best candidate: {best_candidate.get('approach')} with score {best_score:.2f}")

        return best_candidate

    def _create_generation_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a summary of the generation process."""
        total_databases = len(results)
        successful = len([r for r in results if r.get("success", False)])

        summary = {
            "total_databases": total_databases,
            "successful_generations": successful,
            "success_rate": successful / total_databases if total_databases > 0 else 0.0,
            "average_iterations": 0.0,
            "common_issues": [],
            "performance_metrics": {}
        }

        if results:
            # Calculate average iterations
            total_iterations = sum(r.get("iterations", 0) for r in results)
            summary["average_iterations"] = total_iterations / len(results)

            # Collect common issues
            all_errors = [r.get("error", "") for r in results if r.get("error")]
            if all_errors:
                summary["common_issues"] = list(set(all_errors))

        return summary

    # ────────────────────────────────────────────────────────────────────────
    # Template and Formatting Methods
    # ────────────────────────────────────────────────────────────────────────

    def _format_schema_for_generation(self, tables: List[Dict[str, Any]]) -> str:
        """Format schema information for SQL generation."""
        schema_lines = []

        for table in tables:
            table_name = table.get("name", "unknown")
            columns = table.get("columns", [])

            schema_lines.append(f"Table: {table_name}")
            for column in columns:
                col_name = column.get("name", "unknown")
                col_type = column.get("type", "unknown")
                schema_lines.append(f"  - {col_name} ({col_type})")
            schema_lines.append("")

        return "\n".join(schema_lines)

    def _clean_sql_response(self, response: str) -> str:
        """Clean SQL response from LLM."""
        sql = response.strip()

        # Remove markdown code blocks
        if sql.startswith("```sql"):
            sql = sql[6:]
        elif sql.startswith("```"):
            sql = sql[3:]

        if sql.endswith("```"):
            sql = sql[:-3]

        # Remove comments and extra whitespace
        lines = sql.split('\n')
        cleaned_lines = []

        for line in lines:
            line = line.strip()
            if line:
                # Remove inline comments (-- comments)
                comment_pos = line.find('--')
                if comment_pos >= 0:
                    line = line[:comment_pos].strip()

                # Only add non-empty lines
                if line:
                    cleaned_lines.append(line)

        return ' '.join(cleaned_lines).strip()

    # Placeholder methods for template-based generation
    def _get_analytical_template(self, query: str, entities: List[str], tables: List[Dict[str, Any]]) -> Optional[str]:
        """Get analytical query template."""
        # This would contain pre-built templates for analytical queries
        return None

    def _get_comparative_template(self, query: str, entities: List[str], tables: List[Dict[str, Any]]) -> Optional[str]:
        """Get comparative query template."""
        return None

    def _get_reporting_template(self, query: str, entities: List[str], tables: List[Dict[str, Any]]) -> Optional[str]:
        """Get reporting query template."""
        return None

    async def _fill_sql_template(self, template: str, query: str, query_analysis: Dict[str, Any], tables: List[Dict[str, Any]], db_type: str) -> str:
        """Fill SQL template with specific values."""
        return template

    def _validate_postgresql_syntax(self, sql: str) -> List[Dict[str, Any]]:
        """PostgreSQL-specific syntax validation."""
        return []

    def _validate_mysql_syntax(self, sql: str) -> List[Dict[str, Any]]:
        """MySQL-specific syntax validation."""
        return []

    async def _process_database_stream(self, query: str, query_analysis: Dict[str, Any], db_info: Dict[str, Any], generation_mode: str, validation_level: str, create_event) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream database processing events."""
        # This would yield events for each stage of processing
        yield create_event("stage_progress", "analysis", {"message": "Analyzing query requirements..."})
        yield create_event("stage_progress", "generation", {"message": "Generating SQL candidates..."})
        yield create_event("stage_progress", "validation", {"message": "Validating SQL safety and syntax..."})
        yield create_event("stage_progress", "optimization", {"message": "Optimizing for performance..."})
        yield create_event("stage_progress", "execution", {"message": "Testing SQL execution..."})
        yield create_event("stage_complete", "finalization", {"message": "SQL generation complete"})
