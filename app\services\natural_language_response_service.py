"""
Natural Language Response Service
================================

Enhanced response generation for conversational, ChatGPT-like interactions.
Provides data storytelling, follow-up suggestions, and clarifying questions.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from app.utils.bedrock_client import Bedrock<PERSON>lient
from app.services.conversational_context_service import conversational_context_service
from app.prompts.natural_language_responses import (
    CONVERSATIONAL_DATA_SUMMARY_PROMPT,
    PROFESSIONAL_DATA_SUMMARY_PROMPT,
    ANALYTICAL_DATA_SUMMARY_PROMPT,
    FRIENDLY_DATA_SUMMARY_PROMPT,
    EDUCATIONAL_DATA_SUMMARY_PROMPT,
    CONVERSATIONAL_NO_RESULTS_PROMPT,
    PROFESSIONAL_NO_RESULTS_PROMPT,
    FRIENDLY_NO_RESULTS_PROMPT,
    INSIGHTS_GENERATION_PROMPT,
    FOLLOW_UP_SUGGESTIONS_PROMPT,
    C<PERSON><PERSON>FYING_QUESTIONS_PROMPT,
    DATA_STORYTELLING_PROMPT
)

logger = logging.getLogger(__name__)


class ResponseTone(Enum):
    """Different tones for natural language responses."""
    CONVERSATIONAL = "conversational"
    PROFESSIONAL = "professional"
    ANALYTICAL = "analytical"
    FRIENDLY = "friendly"
    EDUCATIONAL = "educational"


class ResponseType(Enum):
    """Types of responses that can be generated."""
    DATA_SUMMARY = "data_summary"
    NO_RESULTS = "no_results"
    INSIGHTS = "insights"
    FOLLOW_UP = "follow_up"
    CLARIFICATION = "clarification"


@dataclass
class DataStory:
    """Structure for data storytelling elements."""
    headline: str
    key_insights: List[str]
    supporting_details: List[str]
    implications: List[str]
    follow_up_questions: List[str]


@dataclass
class ResponseEnhancement:
    """Enhanced response with natural language features."""
    main_response: str
    data_story: Optional[DataStory] = None
    follow_up_suggestions: List[str] = None
    clarifying_questions: List[str] = None
    tone: ResponseTone = ResponseTone.CONVERSATIONAL
    confidence_level: float = 0.8
    
    def __post_init__(self):
        if self.follow_up_suggestions is None:
            self.follow_up_suggestions = []
        if self.clarifying_questions is None:
            self.clarifying_questions = []


class NaturalLanguageResponseService:
    """Service for generating natural, conversational responses."""
    
    def __init__(self):
        self.bedrock_client = BedrockClient()
        
    async def generate_enhanced_response(
        self,
        query: str,
        results: List[Dict[str, Any]],
        context: Dict[str, Any] = None,
        response_type: ResponseType = ResponseType.DATA_SUMMARY,
        tone: ResponseTone = ResponseTone.CONVERSATIONAL,
        session_id: Optional[str] = None
    ) -> ResponseEnhancement:
        """Generate an enhanced natural language response."""
        
        try:
            # Get conversational context if available
            conversation_context = {}
            if session_id:
                conversation_context = await conversational_context_service.extract_context_for_query(
                    session_id, query
                )
            
            # Generate main response based on type
            if response_type == ResponseType.DATA_SUMMARY:
                main_response = await self._generate_data_summary(
                    query, results, context, tone, conversation_context
                )
            elif response_type == ResponseType.NO_RESULTS:
                main_response = await self._generate_no_results_response(
                    query, tone, conversation_context
                )
            elif response_type == ResponseType.INSIGHTS:
                main_response = await self._generate_insights_response(
                    query, results, context, tone, conversation_context
                )
            else:
                main_response = await self._generate_generic_response(
                    query, results, context, tone
                )
            
            # Generate data story if we have results
            data_story = None
            if results and response_type in [ResponseType.DATA_SUMMARY, ResponseType.INSIGHTS]:
                data_story = await self._generate_data_story(query, results, context)
            
            # Generate follow-up suggestions
            follow_up_suggestions = await self._generate_follow_up_suggestions(
                query, results, conversation_context
            )
            
            # Generate clarifying questions if needed
            clarifying_questions = await self._generate_clarifying_questions(
                query, results, conversation_context
            )
            
            return ResponseEnhancement(
                main_response=main_response,
                data_story=data_story,
                follow_up_suggestions=follow_up_suggestions,
                clarifying_questions=clarifying_questions,
                tone=tone,
                confidence_level=self._calculate_response_confidence(results, context)
            )
            
        except Exception as e:
            logger.error(f"Error generating enhanced response: {e}")
            return ResponseEnhancement(
                main_response="I found some data for your query, but had trouble generating a detailed response.",
                tone=tone,
                confidence_level=0.3
            )
    
    def _is_list_query(self, query: str) -> bool:
        """Detect if the query is asking for a list or ranking."""
        query_lower = query.lower()
        list_indicators = [
            'top ', 'bottom ', 'most ', 'least ', 'highest ', 'lowest ',
            'best ', 'worst ', 'first ', 'last ', 'all ', 'list of',
            'show me all', 'give me all', 'what are the'
        ]
        return any(indicator in query_lower for indicator in list_indicators)

    async def _generate_data_summary(
        self,
        query: str,
        results: List[Dict[str, Any]],
        context: Dict[str, Any],
        tone: ResponseTone,
        conversation_context: Dict[str, Any]
    ) -> str:
        """Generate a conversational data summary."""
        
        # Prepare data context
        data_context = self._prepare_data_context(results)
        conversation_info = self._format_conversation_context(conversation_context)
        
        system_prompt = self._get_data_summary_system_prompt(tone)
        
        # Detect if this is a list/ranking query
        is_list_query = self._is_list_query(query)
        
        if is_list_query:
            user_prompt = f"""
User Question: "{query}"

Data Found:
{data_context}

Conversation Context:
{conversation_info}

This is a LIST/RANKING query. The user wants to see the complete list, not just examples.

Generate a response that:
1. SHOWS THE COMPLETE LIST with all items and their values in a clean, numbered format
2. Provides meaningful statistical insights (ranges, patterns, distributions)
3. Identifies interesting trends or outliers in the data
4. Uses a {tone.value} but professional tone
5. Includes business context and implications
6. Does NOT use excessive emojis or visual clutter
7. Focuses on actionable insights rather than just listing data

Make it informative and analytical while remaining conversational.
"""
        else:
            user_prompt = f"""
User Question: "{query}"

Data Found:
{data_context}

Conversation Context:
{conversation_info}

Generate a natural, conversational response that:
1. Directly answers the user's question
2. Tells a story with the data
3. Highlights the most interesting findings
4. Uses specific numbers and examples
5. Maintains a {tone.value} tone

Make it feel like you're having a conversation with a colleague about interesting data discoveries.
"""
        
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        return response.strip()
    
    async def _generate_no_results_response(
        self,
        query: str,
        tone: ResponseTone,
        conversation_context: Dict[str, Any]
    ) -> str:
        """Generate a helpful no-results response."""
        
        conversation_info = self._format_conversation_context(conversation_context)
        
        system_prompt = self._get_no_results_system_prompt(tone)
        
        user_prompt = f"""
User Question: "{query}"

Conversation Context:
{conversation_info}

No data was found. Generate a helpful, empathetic response that:
1. Acknowledges what they were looking for
2. Suggests possible reasons (be specific to their query)
3. Offers concrete next steps
4. Maintains a {tone.value} tone
5. Shows understanding of their intent

Make it feel supportive and solution-oriented.
"""
        
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        return response.strip()
    
    async def _generate_insights_response(
        self,
        query: str,
        results: List[Dict[str, Any]],
        context: Dict[str, Any],
        tone: ResponseTone,
        conversation_context: Dict[str, Any]
    ) -> str:
        """Generate an insights-focused response."""
        
        data_context = self._prepare_data_context(results)
        conversation_info = self._format_conversation_context(conversation_context)
        
        system_prompt = self._get_insights_system_prompt(tone)
        
        user_prompt = f"""
User Question: "{query}"

Data Found:
{data_context}

Conversation Context:
{conversation_info}

Generate an insights-rich response that:
1. Identifies patterns and trends in the data
2. Explains what the data means in business terms
3. Highlights surprising or noteworthy findings
4. Connects insights to potential actions
5. Uses a {tone.value} tone

Focus on the "so what" - why should they care about these findings?
"""
        
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        return response.strip()
    
    async def _generate_generic_response(
        self,
        query: str,
        results: List[Dict[str, Any]],
        context: Dict[str, Any],
        tone: ResponseTone
    ) -> str:
        """Generate a generic conversational response."""
        
        data_context = self._prepare_data_context(results)
        
        system_prompt = f"""You are a helpful data analyst with a {tone.value} communication style. 
        Provide clear, natural responses that feel like a conversation with a knowledgeable colleague."""
        
        user_prompt = f"""
User asked: "{query}"

Data found: {data_context}

Provide a natural, helpful response that directly addresses their question.
"""
        
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        return response.strip()
    
    def _prepare_data_context(self, results: List[Dict[str, Any]]) -> str:
        """Prepare data context for prompts with statistical analysis."""
        if not results:
            return "No data available"

        context_parts = []

        # For each database result
        for result in results:
            db_name = result.get("database_name", "database")
            columns = result.get("columns", [])
            preview = result.get("preview", [])

            if columns and preview:
                # For list/ranking queries, show more complete data
                preview_limit = len(preview) if len(preview) <= 20 else 15

                # Calculate basic statistics if numeric data is present
                stats_info = self._calculate_data_statistics(preview, columns)

                context_parts.append(f"""
Database: {db_name}
Columns: {', '.join(columns)}
Data sample ({len(preview)} total rows): {json.dumps(preview[:preview_limit], indent=2)}
{stats_info}
""")

        return "\n".join(context_parts) if context_parts else "Data structure not available"

    def _calculate_data_statistics(self, data: List[Dict], columns: List[str]) -> str:
        """Calculate basic statistics for the data."""
        if not data:
            return ""

        stats = []

        # Find numeric columns and calculate stats
        for col in columns:
            values = []
            for row in data:
                val = row.get(col)
                if isinstance(val, (int, float)):
                    values.append(val)

            if values and len(values) > 1:
                avg = sum(values) / len(values)
                min_val = min(values)
                max_val = max(values)
                stats.append(f"{col}: avg={avg:.1f}, range={min_val}-{max_val}")

        # Calculate total records
        total_info = f"Total records: {len(data)}"

        if stats:
            return f"Statistics: {total_info}, {', '.join(stats)}"
        else:
            return f"Statistics: {total_info}"
    
    def _format_conversation_context(self, conversation_context: Dict[str, Any]) -> str:
        """Format conversation context for prompts."""
        if not conversation_context:
            return "No previous conversation context"
        
        context_parts = []
        
        # Current focus
        if conversation_context.get("current_focus"):
            context_parts.append(f"Currently discussing: {conversation_context['current_focus']}")
        
        # Recent queries
        recent_queries = conversation_context.get("recent_queries", [])
        if recent_queries:
            context_parts.append("Recent questions:")
            for query in recent_queries[-2:]:  # Last 2 queries
                context_parts.append(f"- {query.get('query_text', '')}")
        
        # Tables discussed
        tables = conversation_context.get("tables_discussed", {})
        if tables:
            table_names = [info.get("table_name", "") for info in tables.values()]
            context_parts.append(f"Tables discussed: {', '.join(table_names)}")
        
        return "\n".join(context_parts) if context_parts else "No conversation context"
    
    def _calculate_response_confidence(
        self, 
        results: List[Dict[str, Any]], 
        context: Dict[str, Any]
    ) -> float:
        """Calculate confidence level for the response."""
        confidence = 0.5  # Base confidence
        
        # Boost confidence if we have good data
        if results:
            confidence += 0.3
            
            # More confidence if we have multiple results
            if len(results) > 1:
                confidence += 0.1
            
            # More confidence if we have sample data
            if any(result.get("preview") for result in results):
                confidence += 0.1
        
        # Boost confidence if we have context
        if context:
            confidence += 0.1
        
        return min(1.0, confidence)


    async def _generate_data_story(
        self,
        query: str,
        results: List[Dict[str, Any]],
        context: Dict[str, Any]
    ) -> DataStory:
        """Generate a data story from the results."""

        data_context = self._prepare_data_context(results)

        system_prompt = DATA_STORYTELLING_PROMPT

        user_prompt = f"""
User asked: "{query}"

Data: {data_context}

Create a data story structure. Return as JSON with these fields:
- headline: One compelling sentence summarizing the main finding
- key_insights: Array of 2-3 main insights
- supporting_details: Array of specific data points that support the insights
- implications: Array of what these findings mean or suggest
- follow_up_questions: Array of natural next questions someone might ask
"""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.6
            )

            # Parse JSON response
            story_data = json.loads(self._clean_json_response(response))

            return DataStory(
                headline=story_data.get("headline", "Data Analysis Results"),
                key_insights=story_data.get("key_insights", []),
                supporting_details=story_data.get("supporting_details", []),
                implications=story_data.get("implications", []),
                follow_up_questions=story_data.get("follow_up_questions", [])
            )

        except Exception as e:
            logger.warning(f"Failed to generate data story: {e}")
            # Generate a basic story from the data
            return self._generate_fallback_data_story(query, results)

    def _generate_fallback_data_story(self, query: str, results: List[Dict[str, Any]]) -> DataStory:
        """Generate a basic data story when AI generation fails."""
        if not results:
            return DataStory(
                headline="No data found for your query",
                key_insights=[],
                supporting_details=[],
                implications=[],
                follow_up_questions=[]
            )

        # Extract basic information from results
        total_records = 0
        data_sources = []

        for result in results:
            preview = result.get("preview", [])
            total_records += len(preview)
            db_name = result.get("database_name", "database")
            if db_name not in data_sources:
                data_sources.append(db_name)

        # Create a basic headline
        if "top" in query.lower() or "most" in query.lower():
            headline = f"Found {total_records} records matching your ranking query"
        elif "count" in query.lower() or "how many" in query.lower():
            headline = f"Analysis shows {total_records} total records"
        else:
            headline = f"Retrieved {total_records} records from {len(data_sources)} data source(s)"

        # Generate basic insights
        insights = []
        if total_records > 0:
            insights.append(f"Dataset contains {total_records} records")
            if len(data_sources) > 1:
                insights.append(f"Data sourced from {len(data_sources)} different databases")

        return DataStory(
            headline=headline,
            key_insights=insights,
            supporting_details=[],
            implications=[],
            follow_up_questions=[]
        )

    async def _generate_follow_up_suggestions(
        self,
        query: str,
        results: List[Dict[str, Any]],
        conversation_context: Dict[str, Any]
    ) -> List[str]:
        """Generate follow-up question suggestions."""

        data_context = self._prepare_data_context(results)
        conversation_info = self._format_conversation_context(conversation_context)

        system_prompt = FOLLOW_UP_SUGGESTIONS_PROMPT

        user_prompt = f"""
Original question: "{query}"

Data found: {data_context}

Conversation context: {conversation_info}

Generate 3-5 specific, actionable follow-up questions that:
1. Build naturally on the current results
2. Explore deeper insights or related aspects
3. Are specific to the actual data found (not generic)
4. Would provide business value
5. Use the actual column names and data values when relevant

Return as a JSON array of strings.
"""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.6
            )

            suggestions = json.loads(self._clean_json_response(response))
            if isinstance(suggestions, list):
                # Filter out generic suggestions
                filtered = []
                generic_patterns = [
                    "what genres", "from a specific year", "what is the average",
                    "tell me more", "show me", "can you"
                ]
                for suggestion in suggestions:
                    if not any(pattern in suggestion.lower() for pattern in generic_patterns):
                        filtered.append(suggestion)
                return filtered[:3]  # Limit to 3 best suggestions
            return []

        except Exception as e:
            logger.warning(f"Failed to generate follow-up suggestions: {e}")
            return self._generate_fallback_suggestions(query, results)

    def _generate_fallback_suggestions(self, query: str, results: List[Dict[str, Any]]) -> List[str]:
        """Generate fallback suggestions when AI generation fails."""
        suggestions = []

        if not results:
            return suggestions

        # Analyze the query type and data to generate relevant suggestions
        query_lower = query.lower()

        # Get column information from results
        columns = []
        for result in results:
            columns.extend(result.get("columns", []))

        # Generate suggestions based on query type and available columns
        if "top" in query_lower or "most" in query_lower:
            suggestions.append("What factors contribute to these top performers?")
            if any("date" in col.lower() or "time" in col.lower() for col in columns):
                suggestions.append("How have these rankings changed over time?")

        if "rental" in query_lower and any("customer" in col.lower() for col in columns):
            suggestions.append("Which customers are driving these rental patterns?")

        if len(columns) > 2:  # If we have multiple columns, suggest correlation analysis
            suggestions.append("Are there correlations between different data attributes?")

        return suggestions[:3]

    async def _generate_clarifying_questions(
        self,
        query: str,
        results: List[Dict[str, Any]],
        conversation_context: Dict[str, Any]
    ) -> List[str]:
        """Generate clarifying questions when the query might be ambiguous."""

        # Only generate clarifying questions if results are limited or query seems vague
        if len(query.split()) > 5 and results:  # Detailed query with results
            return []

        conversation_info = self._format_conversation_context(conversation_context)

        system_prompt = CLARIFYING_QUESTIONS_PROMPT

        user_prompt = f"""
User question: "{query}"

Conversation context: {conversation_info}

Results found: {"Yes" if results else "No"}

Should we ask clarifying questions to help them get better results?
"""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.6
            )

            questions = json.loads(self._clean_json_response(response))
            return questions if isinstance(questions, list) else []

        except Exception as e:
            logger.warning(f"Failed to generate clarifying questions: {e}")
            return []

    def _get_data_summary_system_prompt(self, tone: ResponseTone) -> str:
        """Get system prompt for data summary generation."""

        tone_prompts = {
            ResponseTone.CONVERSATIONAL: CONVERSATIONAL_DATA_SUMMARY_PROMPT,
            ResponseTone.PROFESSIONAL: PROFESSIONAL_DATA_SUMMARY_PROMPT,
            ResponseTone.ANALYTICAL: ANALYTICAL_DATA_SUMMARY_PROMPT,
            ResponseTone.FRIENDLY: FRIENDLY_DATA_SUMMARY_PROMPT,
            ResponseTone.EDUCATIONAL: EDUCATIONAL_DATA_SUMMARY_PROMPT
        }

        return tone_prompts.get(tone, CONVERSATIONAL_DATA_SUMMARY_PROMPT)

    def _get_no_results_system_prompt(self, tone: ResponseTone) -> str:
        """Get system prompt for no results responses."""

        tone_prompts = {
            ResponseTone.CONVERSATIONAL: CONVERSATIONAL_NO_RESULTS_PROMPT,
            ResponseTone.PROFESSIONAL: PROFESSIONAL_NO_RESULTS_PROMPT,
            ResponseTone.FRIENDLY: FRIENDLY_NO_RESULTS_PROMPT,
            ResponseTone.ANALYTICAL: CONVERSATIONAL_NO_RESULTS_PROMPT,  # Use conversational as fallback
            ResponseTone.EDUCATIONAL: CONVERSATIONAL_NO_RESULTS_PROMPT   # Use conversational as fallback
        }

        return tone_prompts.get(tone, CONVERSATIONAL_NO_RESULTS_PROMPT)

    def _get_insights_system_prompt(self, tone: ResponseTone) -> str:
        """Get system prompt for insights generation."""
        return INSIGHTS_GENERATION_PROMPT

    def _clean_json_response(self, response: str) -> str:
        """Clean and extract JSON from LLM response."""
        response = response.strip()

        # Remove markdown code blocks
        if response.startswith("```json"):
            response = response[7:]
        elif response.startswith("```"):
            response = response[3:]

        if response.endswith("```"):
            response = response[:-3]

        # Find JSON boundaries
        start_idx = max(response.find("{"), response.find("["))
        if start_idx == -1:
            return "{}"

        # Find matching closing bracket
        if response[start_idx] == "{":
            end_idx = response.rfind("}") + 1
        else:
            end_idx = response.rfind("]") + 1

        if end_idx > start_idx:
            return response[start_idx:end_idx]

        return response.strip()


# Global instance
natural_language_response_service = NaturalLanguageResponseService()
