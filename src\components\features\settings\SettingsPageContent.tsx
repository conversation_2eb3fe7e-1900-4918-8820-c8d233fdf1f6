"use client";
import React, { useState, useCallback, useMemo } from 'react';
import { useAuth } from '@/providers/AuthContext';
import { useTheme } from '@/providers/theme-provider';
import { usePageTitle } from '@/hooks/usePageTitle';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Settings,
  Shield,
  Key,
  Trash2,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  EyeOff,
  Moon,
  Sun,
  Monitor,
  Lock,
  Mail,
  Bell,
  Database,
  Download,
  X,
  Save,
  Link2,
  Unlink,
  ExternalLink
} from 'lucide-react';

interface PasswordChangeForm {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface EmailPreferences {
  marketingEmails: boolean;
  securityAlerts: boolean;
  productUpdates: boolean;
  weeklyDigest: boolean;
}

interface PrivacySettings {
  profileVisibility: 'public' | 'private';
  dataCollection: boolean;
  analyticsOptOut: boolean;
  thirdPartySharing: boolean;
}

const SettingsPageContent = () => {
  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();

  // Set page title - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: 'Settings',
    icon: Settings
  }), []);

  usePageTitle(pageConfig);

  // Form states
  const [passwordForm, setPasswordForm] = useState<PasswordChangeForm>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const [emailPreferences, setEmailPreferences] = useState<EmailPreferences>({
    marketingEmails: true,
    securityAlerts: true,
    productUpdates: false,
    weeklyDigest: true,
  });

  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    profileVisibility: 'private',
    dataCollection: true,
    analyticsOptOut: false,
    thirdPartySharing: false,
  });

  // UI states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [isSavingPreferences, setIsSavingPreferences] = useState(false);
  const [isExportingData, setIsExportingData] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [deleteConfirmationText, setDeleteConfirmationText] = useState('');
  const [isDeletingAccount, setIsDeletingAccount] = useState(false);

  // Messages
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Validation errors
  const [passwordErrors, setPasswordErrors] = useState<Record<string, string>>({});

  const validatePasswordForm = useCallback(() => {
    const errors: Record<string, string> = {};

    if (!passwordForm.currentPassword) {
      errors.currentPassword = 'Current password is required';
    }

    if (!passwordForm.newPassword) {
      errors.newPassword = 'New password is required';
    } else if (passwordForm.newPassword.length < 8) {
      errors.newPassword = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(passwordForm.newPassword)) {
      errors.newPassword = 'Password must contain uppercase, lowercase, and number';
    }

    if (!passwordForm.confirmPassword) {
      errors.confirmPassword = 'Please confirm your new password';
    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setPasswordErrors(errors);
    return Object.keys(errors).length === 0;
  }, [passwordForm]);

  const handlePasswordInputChange = useCallback((field: keyof PasswordChangeForm, value: string) => {
    setPasswordForm(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field when user starts typing
    if (passwordErrors[field]) {
      setPasswordErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [passwordErrors]);

  const handleChangePassword = useCallback(async () => {
    if (!validatePasswordForm()) {
      return;
    }

    setIsChangingPassword(true);
    setError(null);

    try {
      // TODO: Implement changePassword API call
      // await changePassword(passwordForm);
      
      // For now, simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      
      setSuccessMessage('Password changed successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error('Failed to change password:', err);
      setError('Failed to change password. Please check your current password and try again.');
    } finally {
      setIsChangingPassword(false);
    }
  }, [passwordForm, validatePasswordForm]);

  const handleSaveEmailPreferences = useCallback(async () => {
    setIsSavingPreferences(true);
    setError(null);

    try {
      // TODO: Implement saveEmailPreferences API call
      // await saveEmailPreferences(emailPreferences);
      
      // For now, simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccessMessage('Email preferences saved successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error('Failed to save email preferences:', err);
      setError('Failed to save email preferences. Please try again.');
    } finally {
      setIsSavingPreferences(false);
    }
  }, [emailPreferences]);

  const handleSavePrivacySettings = useCallback(async () => {
    setIsSavingPreferences(true);
    setError(null);

    try {
      // TODO: Implement savePrivacySettings API call
      // await savePrivacySettings(privacySettings);
      
      // For now, simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccessMessage('Privacy settings saved successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error('Failed to save privacy settings:', err);
      setError('Failed to save privacy settings. Please try again.');
    } finally {
      setIsSavingPreferences(false);
    }
  }, [privacySettings]);

  const handleExportData = useCallback(async () => {
    setIsExportingData(true);
    setError(null);

    try {
      // TODO: Implement exportUserData API call
      // const dataUrl = await exportUserData();
      
      // For now, simulate the API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Simulate download
      const element = document.createElement('a');
      element.href = 'data:text/json;charset=utf-8,' + encodeURIComponent(JSON.stringify({
        message: 'This would be your actual data export',
        timestamp: new Date().toISOString()
      }));
      element.download = `user-data-export-${Date.now()}.json`;
      element.click();
      
      setSuccessMessage('Data export completed successfully!');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error('Failed to export data:', err);
      setError('Failed to export data. Please try again.');
    } finally {
      setIsExportingData(false);
    }
  }, []);

  const handleDeleteAccount = useCallback(async () => {
    if (deleteConfirmationText !== 'DELETE') {
      setError('Please type "DELETE" to confirm account deletion');
      return;
    }

    setIsDeletingAccount(true);
    setError(null);

    try {
      // TODO: Implement deleteAccount API call
      // await deleteAccount();
      
      // For now, simulate the API call and logout
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      await logout();
    } catch (err: any) {
      console.error('Failed to delete account:', err);
      setError('Failed to delete account. Please try again.');
    } finally {
      setIsDeletingAccount(false);
    }
  }, [deleteConfirmationText, logout]);

  const clearMessages = useCallback(() => {
    setError(null);
    setSuccessMessage(null);
  }, []);

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Removed hardcoded header - now using centralized header system */}

            {/* Success/Error Messages */}
            {successMessage && (
              <div className="p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0" />
                    <div className="text-green-800 dark:text-green-200">
                      <div className="font-medium">Success</div>
                      <div className="text-sm">{successMessage}</div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={clearMessages}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {error && (
              <div className="p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0" />
                    <div className="text-red-800 dark:text-red-200">
                      <div className="font-medium">Error</div>
                      <div className="text-sm">{error}</div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm" onClick={clearMessages}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}

            {/* Account Security Card */}
            <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                  <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Account Security
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400">
                  Manage your password and security settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Change Password Section */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 flex items-center gap-2">
                    <Key className="h-4 w-4" />
                    Change Password
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Current Password */}
                    <div className="space-y-2">
                      <Label htmlFor="currentPassword">Current Password</Label>
                      <div className="relative">
                        <Input
                          id="currentPassword"
                          type={showCurrentPassword ? 'text' : 'password'}
                          value={passwordForm.currentPassword}
                          onChange={(e) => handlePasswordInputChange('currentPassword', e.target.value)}
                          placeholder="Enter current password"
                          className={passwordErrors.currentPassword ? 'border-red-300 dark:border-red-700' : ''}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        >
                          {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                      {passwordErrors.currentPassword && (
                        <p className="text-sm text-red-600 dark:text-red-400">
                          {passwordErrors.currentPassword}
                        </p>
                      )}
                    </div>

                    {/* New Password */}
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <div className="relative">
                        <Input
                          id="newPassword"
                          type={showNewPassword ? 'text' : 'password'}
                          value={passwordForm.newPassword}
                          onChange={(e) => handlePasswordInputChange('newPassword', e.target.value)}
                          placeholder="Enter new password"
                          className={passwordErrors.newPassword ? 'border-red-300 dark:border-red-700' : ''}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                      {passwordErrors.newPassword && (
                        <p className="text-sm text-red-600 dark:text-red-400">
                          {passwordErrors.newPassword}
                        </p>
                      )}
                    </div>

                    {/* Confirm Password */}
                    <div className="space-y-2 md:col-span-2">
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <div className="relative">
                        <Input
                          id="confirmPassword"
                          type={showConfirmPassword ? 'text' : 'password'}
                          value={passwordForm.confirmPassword}
                          onChange={(e) => handlePasswordInputChange('confirmPassword', e.target.value)}
                          placeholder="Confirm new password"
                          className={passwordErrors.confirmPassword ? 'border-red-300 dark:border-red-700' : ''}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                      {passwordErrors.confirmPassword && (
                        <p className="text-sm text-red-600 dark:text-red-400">
                          {passwordErrors.confirmPassword}
                        </p>
                      )}
                    </div>
                  </div>

                  <Button
                    onClick={handleChangePassword}
                    disabled={isChangingPassword}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isChangingPassword ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Lock className="h-4 w-4 mr-2" />
                    )}
                    {isChangingPassword ? 'Changing Password...' : 'Change Password'}
                  </Button>
                </div>

                {/* OAuth Connections */}
                <div className="border-t border-slate-200 dark:border-slate-700 pt-6">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 flex items-center gap-2 mb-4">
                    <Link2 className="h-4 w-4" />
                    Connected Accounts
                  </h3>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                          <span className="text-red-600 dark:text-red-400 text-sm font-bold">G</span>
                        </div>
                        <div>
                          <p className="font-medium text-slate-900 dark:text-slate-100">Google</p>
                          <p className="text-sm text-slate-500 dark:text-slate-400">Connect with Google OAuth</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Link2 className="h-4 w-4 mr-2" />
                        Connect
                      </Button>
                    </div>

                    <div className="flex items-center justify-between p-3 border border-slate-200 dark:border-slate-700 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-100 dark:bg-gray-900 rounded-full flex items-center justify-center">
                          <span className="text-gray-600 dark:text-gray-400 text-sm font-bold">GH</span>
                        </div>
                        <div>
                          <p className="font-medium text-slate-900 dark:text-slate-100">GitHub</p>
                          <p className="text-sm text-slate-500 dark:text-slate-400">Connect with GitHub OAuth</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        <Link2 className="h-4 w-4 mr-2" />
                        Connect
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Theme Preferences Card */}
            <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                  <Monitor className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Theme Preferences
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400">
                  Choose your preferred theme and appearance
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      theme === 'light'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                        : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                    }`}
                    onClick={() => setTheme('light')}
                  >
                    <div className="flex items-center gap-3">
                      <Sun className="h-5 w-5 text-yellow-500" />
                      <div>
                        <p className="font-medium text-slate-900 dark:text-slate-100">Light</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">Light theme</p>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      theme === 'dark'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                        : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                    }`}
                    onClick={() => setTheme('dark')}
                  >
                    <div className="flex items-center gap-3">
                      <Moon className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                      <div>
                        <p className="font-medium text-slate-900 dark:text-slate-100">Dark</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">Dark theme</p>
                      </div>
                    </div>
                  </div>

                  <div
                    className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                      theme === 'system'
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950'
                        : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                    }`}
                    onClick={() => setTheme('system')}
                  >
                    <div className="flex items-center gap-3">
                      <Monitor className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      <div>
                        <p className="font-medium text-slate-900 dark:text-slate-100">System</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">Follow system</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Email Preferences Card */}
            <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                  <Mail className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Email Preferences
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400">
                  Control what emails you receive from us
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="marketingEmails"
                      checked={emailPreferences.marketingEmails}
                      onCheckedChange={(checked) =>
                        setEmailPreferences(prev => ({ ...prev, marketingEmails: !!checked }))
                      }
                    />
                    <div className="flex-1">
                      <Label htmlFor="marketingEmails" className="text-sm font-medium cursor-pointer">
                        Marketing emails
                      </Label>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Receive updates about new features and promotions
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="securityAlerts"
                      checked={emailPreferences.securityAlerts}
                      onCheckedChange={(checked) =>
                        setEmailPreferences(prev => ({ ...prev, securityAlerts: !!checked }))
                      }
                    />
                    <div className="flex-1">
                      <Label htmlFor="securityAlerts" className="text-sm font-medium cursor-pointer">
                        Security alerts
                      </Label>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Important notifications about your account security
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="productUpdates"
                      checked={emailPreferences.productUpdates}
                      onCheckedChange={(checked) =>
                        setEmailPreferences(prev => ({ ...prev, productUpdates: !!checked }))
                      }
                    />
                    <div className="flex-1">
                      <Label htmlFor="productUpdates" className="text-sm font-medium cursor-pointer">
                        Product updates
                      </Label>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        News about product improvements and new releases
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="weeklyDigest"
                      checked={emailPreferences.weeklyDigest}
                      onCheckedChange={(checked) =>
                        setEmailPreferences(prev => ({ ...prev, weeklyDigest: !!checked }))
                      }
                    />
                    <div className="flex-1">
                      <Label htmlFor="weeklyDigest" className="text-sm font-medium cursor-pointer">
                        Weekly digest
                      </Label>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Summary of your activity and insights
                      </p>
                    </div>
                  </div>
                </div>

                <Button
                  onClick={handleSaveEmailPreferences}
                  disabled={isSavingPreferences}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSavingPreferences ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {isSavingPreferences ? 'Saving...' : 'Save Preferences'}
                </Button>
              </CardContent>
            </Card>

            {/* Privacy & Data Card */}
            <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                  <Database className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Privacy & Data
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400">
                  Manage your data and privacy settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Privacy Settings */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100">Privacy Settings</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="dataCollection"
                        checked={privacySettings.dataCollection}
                        onCheckedChange={(checked) =>
                          setPrivacySettings(prev => ({ ...prev, dataCollection: !!checked }))
                        }
                      />
                      <div className="flex-1">
                        <Label htmlFor="dataCollection" className="text-sm font-medium cursor-pointer">
                          Allow data collection for service improvement
                        </Label>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          Help us improve our services by collecting anonymous usage data
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="analyticsOptOut"
                        checked={privacySettings.analyticsOptOut}
                        onCheckedChange={(checked) =>
                          setPrivacySettings(prev => ({ ...prev, analyticsOptOut: !!checked }))
                        }
                      />
                      <div className="flex-1">
                        <Label htmlFor="analyticsOptOut" className="text-sm font-medium cursor-pointer">
                          Opt out of analytics tracking
                        </Label>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          Disable analytics and tracking cookies
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3">
                      <Checkbox
                        id="thirdPartySharing"
                        checked={privacySettings.thirdPartySharing}
                        onCheckedChange={(checked) =>
                          setPrivacySettings(prev => ({ ...prev, thirdPartySharing: !!checked }))
                        }
                      />
                      <div className="flex-1">
                        <Label htmlFor="thirdPartySharing" className="text-sm font-medium cursor-pointer">
                          Allow third-party data sharing
                        </Label>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          Share anonymized data with trusted partners for research
                        </p>
                      </div>
                    </div>
                  </div>

                  <Button
                    onClick={handleSavePrivacySettings}
                    disabled={isSavingPreferences}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSavingPreferences ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {isSavingPreferences ? 'Saving...' : 'Save Privacy Settings'}
                  </Button>
                </div>

                {/* Data Management */}
                <div className="border-t border-slate-200 dark:border-slate-700 pt-6">
                  <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-4">Data Management</h3>
                  
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border border-slate-200 dark:border-slate-700 rounded-lg">
                      <div>
                        <p className="font-medium text-slate-900 dark:text-slate-100">Export your data</p>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          Download a copy of all your account data
                        </p>
                      </div>
                      <Button
                        onClick={handleExportData}
                        disabled={isExportingData}
                        variant="outline"
                      >
                        {isExportingData ? (
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Download className="h-4 w-4 mr-2" />
                        )}
                        {isExportingData ? 'Exporting...' : 'Export Data'}
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Danger Zone Card */}
            <Card className="bg-white dark:bg-slate-900 border-red-200 dark:border-red-800">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-red-900 dark:text-red-100 flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                  Danger Zone
                </CardTitle>
                <CardDescription className="text-red-700 dark:text-red-300">
                  Irreversible actions that will permanently affect your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!showDeleteConfirmation ? (
                  <div className="flex items-center justify-between p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950">
                    <div>
                      <p className="font-medium text-red-900 dark:text-red-100">Delete Account</p>
                      <p className="text-sm text-red-700 dark:text-red-300">
                        Permanently delete your account and all associated data
                      </p>
                    </div>
                    <Button
                      onClick={() => setShowDeleteConfirmation(true)}
                      variant="destructive"
                      className="bg-red-600 hover:bg-red-700"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete Account
                    </Button>
                  </div>
                ) : (
                  <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950 space-y-4">
                    <div>
                      <p className="font-medium text-red-900 dark:text-red-100 mb-2">
                        Are you absolutely sure?
                      </p>
                      <p className="text-sm text-red-700 dark:text-red-300 mb-4">
                        This action cannot be undone. This will permanently delete your account and remove all of your data from our servers.
                      </p>
                      <Label htmlFor="deleteConfirmation" className="text-sm font-medium text-red-900 dark:text-red-100">
                        Type <strong>DELETE</strong> to confirm:
                      </Label>
                      <Input
                        id="deleteConfirmation"
                        value={deleteConfirmationText}
                        onChange={(e) => setDeleteConfirmationText(e.target.value)}
                        placeholder="DELETE"
                        className="mt-2 border-red-300 dark:border-red-700"
                      />
                    </div>
                    
                    <div className="flex gap-3">
                      <Button
                        onClick={() => {
                          setShowDeleteConfirmation(false);
                          setDeleteConfirmationText('');
                        }}
                        variant="outline"
                        disabled={isDeletingAccount}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleDeleteAccount}
                        disabled={isDeletingAccount || deleteConfirmationText !== 'DELETE'}
                        variant="destructive"
                        className="bg-red-600 hover:bg-red-700"
                      >
                        {isDeletingAccount ? (
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4 mr-2" />
                        )}
                        {isDeletingAccount ? 'Deleting Account...' : 'Delete Account'}
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default SettingsPageContent; 