"use client";
import React from 'react';
import Layout from '@/components/layout/Layout';
import DataSourcesPageContent from '@/components/features/datasources/DataSourcesPageContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

const DataSourcesPage = () => {
  return (
    <ProtectedRoute>
      <Layout>
        <DataSourcesPageContent />
      </Layout>
    </ProtectedRoute>
  );
};

export default DataSourcesPage;