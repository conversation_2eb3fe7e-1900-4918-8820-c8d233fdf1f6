"""
Prompts Module

This module provides system prompts for the various agents in the system.
"""

# Import all prompts for easier access
from app.prompts.orchestrator import SYSTEM_PROMPT as ORCHESTRATOR_SYSTEM_PROMPT
from app.prompts.orchestrator import QUERY_ENHANCEMENT_SYSTEM_PROMPT
from app.prompts.sql import get_sql_generation_prompt
from app.prompts.output import NO_RESULTS_SYSTEM_PROMPT, RESULTS_SUMMARY_SYSTEM_PROMPT
from app.prompts.database import RELEVANT_TABLES_SYSTEM_PROMPT
from app.prompts.query_understanding import (
    INTENT_ANALYSIS_SYSTEM_PROMPT,
    ENTITY_EXTRACTION_SYSTEM_PROMPT,
    QUERY_ENHANCEMENT_SYSTEM_PROMPT as ENHANCED_QUERY_ENHANCEMENT_SYSTEM_PROMPT,
    CLARIFICATION_GENERATION_SYSTEM_PROMPT
)
from app.prompts.advanced_sql import (
    MULTI_STAGE_SQL_GENERATION_SYSTEM_PROMPT,
    ANALYTICAL_SQL_TEMPLATE_PROMPT,
    PERFORMANCE_OPTIMIZATION_PROMPT,
    SAFETY_VALIDATION_PROMPT,
    QUERY_REFINEMENT_PROMPT
)
