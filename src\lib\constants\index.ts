// Main constants exports

export * from './api';
export * from './routes';

// Application constants
export const APP_CONFIG = {
  NAME: 'Agent Platform',
  DESCRIPTION: 'Agent Platform for database queries',
  VERSION: '1.0.0',
} as const;

// UI constants
export const UI_CONFIG = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
  MOBILE_BREAKPOINT: 768,
} as const;

// Chat constants
export const CHAT_CONFIG = {
  MAX_MESSAGE_LENGTH: 4000,
  DEFAULT_OUTPUT_FORMAT: 'excel',
  SESSION_ID_LENGTH: 36,
} as const;

// Database constants
export const DATABASE_TYPES = {
  POSTGRESQL: 'POSTGRESQL',
  MONGODB: 'MONGODB',
  MYSQL: 'MYSQL',
  SQLITE: 'SQLITE',
} as const;

// File upload constants
export const FILE_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'],
} as const;
