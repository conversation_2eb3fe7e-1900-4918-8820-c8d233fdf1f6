'use client';

import RegisterForm from '@/components/features/auth/RegisterForm';
import Link from 'next/link';
import React from 'react';

const RegisterPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with logo */}
      <div className="absolute top-0 left-0 p-6 z-20">
        <Link href="/" className="flex items-center space-x-2 group">
          <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-blue-500 group-hover:bg-blue-600 transition-colors duration-200 shadow-sm">
            <svg viewBox="0 0 24 24" fill="none" className="w-5 h-5 text-white">
              <path
                d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <span className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-500 transition-colors duration-200">
            Agent
          </span>
        </Link>
      </div>
      
      {/* Main content */}
      <div className="min-h-screen flex items-center justify-center p-6">
        <div className="w-full max-w-md">
          {/* Glassmorphism card */}
          <div className="backdrop-blur-xl bg-white/80 dark:bg-gray-800/80 rounded-3xl border border-gray-200/50 dark:border-gray-700/50 shadow-xl">
            {/* Header section */}
            <div className="p-8 pb-0 text-center">
              <h1 className="text-3xl font-light text-gray-900 dark:text-white mb-2 tracking-tight">
                Create your account
              </h1>
              <p className="text-gray-500 dark:text-gray-400 font-light">
                Join us and start your journey
              </p>
            </div>

            {/* Form section */}
            <div className="p-8">
              <RegisterForm />
            </div>
          </div>

          {/* Footer */}
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500 dark:text-gray-400">
              By creating an account, you agree to our{' '}
              <a href="#" className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline">
                Terms of Service
              </a>{' '}
              and{' '}
              <a href="#" className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 hover:underline">
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
