"""Report Service

This module provides high-level services for managing user reports.
It acts as a coordinator between controllers and the report storage service.
All business logic and error handling is contained within this service.
"""

from __future__ import annotations

import logging
from typing import List, Dict, Any, Optional

from fastapi import HTTPException, status

from app.models.api_models import ListReportsResponse, ReportInfo
from app.services.report_storage_service import ReportStorageService

logger = logging.getLogger(__name__)


class ReportService:
    """Service for managing user reports.
    
    This service coordinates between controllers and the report storage operations.
    It contains all business logic, validation, and error handling for report operations.
    """
    
    def __init__(self):
        self.report_storage_service = ReportStorageService()

    async def list_user_reports(
        self,
        *,
        user_id: str,
        max_reports: Optional[int] = None,
    ) -> ListReportsResponse:
        """
        List all reports for a user with comprehensive error handling.
        
        Args:
            user_id: The user ID to fetch reports for
            max_reports: Maximum number of reports to return (optional)
            
        Returns:
            ListReportsResponse: Response containing list of reports and metadata
            
        Raises:
            HTTPException: For various error conditions with appropriate status codes
        """
        try:
            # Validate inputs
            if not user_id or not user_id.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User ID is required"
                )
            
            # Validate max_reports parameter
            validated_max_reports = self._validate_max_reports(max_reports)
            
            logger.info(f"Fetching reports for user {user_id} (max: {validated_max_reports})")
            
            # Fetch reports from storage
            reports_data = await self.report_storage_service.list_user_reports(
                user_id=user_id,
                max_reports=validated_max_reports
            )
            
            # Convert to response models
            report_infos = self._convert_to_report_infos(reports_data)
            
            # Create response
            response = ListReportsResponse(
                reports=report_infos,
                total_count=len(report_infos)
            )
            
            logger.info(f"Successfully retrieved {len(report_infos)} reports for user {user_id}")
            return response
            
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error listing reports for user {user_id}: {e}")
            
            # Map specific storage errors to appropriate HTTP status codes
            error_message = str(e).lower()
            
            if "credentials not configured" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Report storage service is not properly configured"
                )
            elif "access denied" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Unable to access report storage"
                )
            elif "bucket not found" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                    detail="Report storage is not available"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to retrieve reports due to an internal error"
                )

    def _validate_max_reports(self, max_reports: Optional[int]) -> int:
        """
        Validate and normalize the max_reports parameter.
        
        Args:
            max_reports: The max_reports value to validate
            
        Returns:
            int: Validated max_reports value
            
        Raises:
            HTTPException: If validation fails
        """
        # Set default if not provided
        if max_reports is None:
            return 100
        
        # Validate range
        if max_reports < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="max_reports must be at least 1"
            )
        
        if max_reports > 500:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="max_reports cannot exceed 500"
            )
        
        return max_reports

    def _convert_to_report_infos(self, reports_data: List[Dict[str, Any]]) -> List[ReportInfo]:
        """
        Convert raw report data to ReportInfo models.
        
        Args:
            reports_data: Raw report data from storage service
            
        Returns:
            List[ReportInfo]: List of validated ReportInfo models
        """
        report_infos = []
        
        for report_data in reports_data:
            try:
                # Validate required fields are present
                required_fields = ['key', 'session_id', 'file_name', 'size', 
                                 'last_modified', 'download_url', 'content_type', 'format']
                
                for field in required_fields:
                    if field not in report_data:
                        logger.warning(f"Missing required field '{field}' in report data: {report_data}")
                        continue
                
                # Create ReportInfo model
                report_info = ReportInfo(
                    key=report_data['key'],
                    session_id=report_data['session_id'],
                    file_name=report_data['file_name'],
                    size=report_data['size'],
                    last_modified=report_data['last_modified'],
                    download_url=report_data['download_url'],
                    content_type=report_data['content_type'],
                    format=report_data['format']
                )
                
                report_infos.append(report_info)
                
            except Exception as e:
                logger.error(f"Error converting report data to ReportInfo: {e}, data: {report_data}")
                # Skip invalid reports rather than failing the entire request
                continue
        
        return report_infos 
