"""Credential Service

This module provides secure storage and retrieval of database credentials.
Credentials are encrypted before storage and only decrypted when needed.
"""

import logging
import secrets
from typing import Dict, List, Optional, Tuple
from cryptography.fernet import Fernet
from sqlalchemy.orm import Session

from app.models.database import Database, DatabaseCredentials, DatabaseType
from app.models.auth_db import StoredCredentialDB
from app.utils.db import get_db
from app.config import settings
from sqlalchemy import Column, String, Text, Boolean, Integer, DateTime
from datetime import datetime

logger = logging.getLogger(__name__)


class CredentialService:
    """Service for securely managing database credentials."""
    
    def __init__(self):
        # Generate or use encryption key - in production, use a secure key management service
        self.encryption_key = self._get_or_create_encryption_key()
        self.cipher = Fernet(self.encryption_key)
    
    def _get_or_create_encryption_key(self) -> bytes:
        """Get or create encryption key. In production, use AWS KMS or similar."""
        # For now, use a key from environment or generate one
        # In production, this should come from a secure key management service
        key_string = getattr(settings, 'CREDENTIAL_ENCRYPTION_KEY', None)
        if key_string:
            return key_string.encode()
        else:
            # Generate a new key (this should be persisted securely in production)
            return Fernet.generate_key()
    
    def _encrypt(self, value: str) -> str:
        """Encrypt a string value."""
        if not value:
            return ""
        return self.cipher.encrypt(value.encode()).decode()
    
    def _decrypt(self, encrypted_value: str) -> str:
        """Decrypt an encrypted string value."""
        if not encrypted_value:
            return ""
        return self.cipher.decrypt(encrypted_value.encode()).decode()
    
    async def store_credentials(
        self,
        user_id: str,
        database_id: str,
        name: str,
        description: Optional[str],
        db_type: DatabaseType,
        credentials: DatabaseCredentials,
        connection_string: Optional[str] = None
    ) -> None:
        """Store encrypted database credentials."""
        try:
            # Get database session
            db_session = next(get_db())
            
            # Check if credentials already exist
            existing = db_session.query(StoredCredentialDB).filter_by(
                id=database_id, user_id=user_id
            ).first()
            
            if existing:
                # Update existing credentials
                existing.name = name
                existing.description = description
                existing.db_type = db_type.value
                existing.encrypted_host = self._encrypt(credentials.host)
                existing.encrypted_port = self._encrypt(str(credentials.port))
                existing.encrypted_username = self._encrypt(credentials.username)
                existing.encrypted_password = self._encrypt(credentials.password)
                existing.encrypted_database = self._encrypt(credentials.database)
                existing.encrypted_db_schema = self._encrypt(credentials.db_schema or "")
                existing.ssl_enabled = credentials.ssl_enabled
                existing.encrypted_connection_string = self._encrypt(connection_string or "")
                existing.updated_at = datetime.utcnow()
            else:
                # Create new credential record
                stored_cred = StoredCredentialDB(
                    id=database_id,
                    user_id=user_id,
                    name=name,
                    description=description,
                    db_type=db_type.value,
                    encrypted_host=self._encrypt(credentials.host),
                    encrypted_port=self._encrypt(str(credentials.port)),
                    encrypted_username=self._encrypt(credentials.username),
                    encrypted_password=self._encrypt(credentials.password),
                    encrypted_database=self._encrypt(credentials.database),
                    encrypted_db_schema=self._encrypt(credentials.db_schema or ""),
                    ssl_enabled=credentials.ssl_enabled,
                    encrypted_connection_string=self._encrypt(connection_string or "")
                )
                db_session.add(stored_cred)
            
            db_session.commit()
            logger.info(f"Stored encrypted credentials for database {database_id}")
            
        except Exception as e:
            db_session.rollback()
            logger.error(f"Error storing credentials: {str(e)}")
            raise
        finally:
            db_session.close()
    
    async def get_credentials(self, user_id: str, database_id: str) -> Optional[Database]:
        """Retrieve and decrypt database credentials."""
        try:
            # Get database session
            db_session = next(get_db())
            
            # Query for the credentials
            stored_cred = db_session.query(StoredCredentialDB).filter_by(
                id=database_id, user_id=user_id
            ).first()
            
            if not stored_cred:
                return None
            
            # Decrypt credentials
            credentials = DatabaseCredentials(
                host=self._decrypt(stored_cred.encrypted_host),
                port=int(self._decrypt(stored_cred.encrypted_port)),
                username=self._decrypt(stored_cred.encrypted_username),
                password=self._decrypt(stored_cred.encrypted_password),
                database=self._decrypt(stored_cred.encrypted_database),
                db_schema=self._decrypt(stored_cred.encrypted_db_schema) or None,
                ssl_enabled=stored_cred.ssl_enabled
            )
            
            # Create Database object
            database = Database(
                id=stored_cred.id,
                user_id=stored_cred.user_id,
                name=stored_cred.name,
                description=stored_cred.description,
                db_type=DatabaseType(stored_cred.db_type),
                credentials=credentials,
                connection_string=self._decrypt(stored_cred.encrypted_connection_string) or None
            )
            
            return database
            
        except Exception as e:
            logger.error(f"Error retrieving credentials: {str(e)}")
            return None
        finally:
            db_session.close()
    
    async def list_user_databases(self, user_id: str) -> List[Dict[str, any]]:
        """List all databases for a user (without credentials)."""
        try:
            # Get database session
            db_session = next(get_db())
            
            # Query for user's databases
            credentials = db_session.query(StoredCredentialDB).filter_by(
                user_id=user_id
            ).all()
            
            # Return basic info without sensitive data
            result = []
            for cred in credentials:
                result.append({
                    "id": cred.id,
                    "name": cred.name,
                    "description": cred.description,
                    "type": cred.db_type,
                    "host": self._decrypt(cred.encrypted_host),  # Host is usually not super sensitive
                    "database": self._decrypt(cred.encrypted_database),
                    "created_at": cred.created_at.isoformat() if cred.created_at else None
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Error listing user databases: {str(e)}")
            return []
        finally:
            db_session.close()
    
    async def delete_credentials(self, user_id: str, database_id: str) -> bool:
        """Delete stored credentials."""
        try:
            # Get database session
            db_session = next(get_db())
            
            # Find and delete the credentials
            deleted = db_session.query(StoredCredentialDB).filter_by(
                id=database_id, user_id=user_id
            ).delete()
            
            db_session.commit()
            
            if deleted:
                logger.info(f"Deleted credentials for database {database_id}")
                return True
            else:
                logger.warning(f"No credentials found to delete for database {database_id}")
                return False
                
        except Exception as e:
            db_session.rollback()
            logger.error(f"Error deleting credentials: {str(e)}")
            return False
        finally:
            db_session.close() 
