"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Loader2, X, MoreHorizontal } from 'lucide-react';
import { ChartWidget as ChartWidgetType } from '@/types';
import { useApi } from '@/providers/ApiContext';
import ChartRenderer from './ChartRenderer';
import CreateChartInput from './CreateChartInput';

interface ChartWidgetProps {
  widget: ChartWidgetType;
  onDelete: (widgetId: string) => void;
  onUpdate: (widgetId: string, updates: Partial<ChartWidgetType>) => void;
  className?: string;
}

const ChartWidget: React.FC<ChartWidgetProps> = ({
  widget,
  onDelete,
  onUpdate,
  className = '',
}) => {
  const { queryChart } = useApi();
  const [isEditing, setIsEditing] = useState(false);
  const [editPrompt, setEditPrompt] = useState('');

  const handleCreateChart = useCallback(async (prompt: string) => {
    onUpdate(widget.id, {
      isLoading: true,
      error: null,
      title: 'Generating...'
    });

    try {
      const response = await queryChart({ prompt });

      if (response.success && response.data) {
        onUpdate(widget.id, {
          chartData: response.data,
          isLoading: false,
          error: null,
          title: response.data.title,
        });
      } else {
        throw new Error(response.error || 'Failed to generate chart');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create chart';
      onUpdate(widget.id, {
        isLoading: false,
        error: errorMessage,
        title: 'Generation Failed',
      });
    }
  }, [widget.id, queryChart, onUpdate]);

  const handleEdit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editPrompt.trim()) return;

    await handleCreateChart(editPrompt.trim());
    setIsEditing(false);
    setEditPrompt('');
  }, [editPrompt, handleCreateChart]);

  const isLoading = widget.isLoading;
  const error = widget.error;
  const hasChart = !!widget.chartData;

  return (
    <Card className={`h-full overflow-hidden ${className}`}>
      {/* Header */}
      <CardHeader 
        className="flex flex-row items-center justify-between p-4 pb-2 react-grid-no-drag"
        onMouseDown={(e) => e.stopPropagation()}
      >
        <div></div>
        <div className="flex items-center gap-2">
          {hasChart && !isLoading && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
              className="h-8 w-8 p-0"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      {/* Content */}
      <CardContent className="flex-1 h-full p-4">
        {/* Loading State */}
        {isLoading && (
          <div className="h-full flex flex-col items-center justify-center space-y-4">
            <div className="text-center space-y-4">
              <div className="text-lg font-medium text-foreground">Generating</div>
              <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto" />
            </div>
          </div>
        )}

        {/* Edit Mode */}
        {isEditing && (
          <div className="h-full flex flex-col items-center justify-center space-y-4">
            <form onSubmit={handleEdit} className="w-full max-w-md space-y-4">
              <Input
                value={editPrompt}
                onChange={(e) => setEditPrompt(e.target.value)}
                placeholder="Describe any changes..."
                className="w-full"
                autoFocus
              />
              <div className="flex gap-2">
                <Button
                  type="submit"
                  disabled={!editPrompt.trim()}
                  className="flex-1"
                >
                  Update
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={() => {
                    setIsEditing(false);
                    setEditPrompt('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>
        )}

        {/* Chart Display */}
        {hasChart && !isLoading && !error && !isEditing && widget.chartData && (
          <div className="h-full">
            <ChartRenderer
              chartData={widget.chartData}
              className="h-full"
            />
          </div>
        )}

        {/* Empty State */}
        {!hasChart && !isLoading && !error && !isEditing && (
          <CreateChartInput
            onSubmit={handleCreateChart}
            isLoading={isLoading}
            className="h-full"
          />
        )}

        {/* Error State */}
        {error && !isLoading && !isEditing && (
          <div className="h-full flex flex-col items-center justify-center space-y-4">
            <div className="text-center space-y-2">
              <div className="text-destructive font-medium">Generation Failed</div>
              <p className="text-muted-foreground text-sm">{error}</p>
              <Button
                onClick={() => onUpdate(widget.id, { error: null })}
                variant="outline"
              >
                Try Again
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ChartWidget;
