"""Configuration settings for the application.

This module loads environment variables and provides configuration values for the application.
"""

import os
from dotenv import load_dotenv
from pathlib import Path
import secrets

# Load environment variables from .env file if it exists
env_path = Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=env_path)

# ──────────────────────────────────────────────────────────────
# AWS Settings
# ──────────────────────────────────────────────────────────────
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.getenv('AWS_REGION', 'us-east-1')

# ──────────────────────────────────────────────────────────────
# DynamoDB Tables
# ──────────────────────────────────────────────────────────────
CONVERSATION_TABLE = os.getenv('CONVERSATION_TABLE')
DATASET_CATALOG_TABLE = os.getenv('DATASET_CATALOG_TABLE')

# S3 Report Storage
REPORTS_BUCKET = os.getenv("REPORTS_BUCKET")
PRESIGN_TTL_SECONDS = int(os.getenv("PRESIGN_TTL_SECONDS", "600"))  # 10 min

# ──────────────────────────────────────────────────────────────
# Bedrock Model Settings
# ──────────────────────────────────────────────────────────────
BEDROCK_MODEL_ID = os.getenv('BEDROCK_MODEL_ID', 'anthropic.claude-3-sonnet-20240229-v1:0')
BEDROCK_STREAMING = os.getenv('BEDROCK_STREAMING', 'true').lower() == 'true'
BEDROCK_MAX_TOKENS = int(os.getenv('BEDROCK_MAX_TOKENS', '4096'))

# ──────────────────────────────────────────────────────────────
# Application Settings
# ──────────────────────────────────────────────────────────────
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
PARALLEL_AGENT_LIMIT = int(os.getenv('PARALLEL_AGENT_LIMIT', '10'))
MAX_TABLES_PER_AGENT = int(os.getenv('MAX_TABLES_PER_AGENT', '700'))

# ──────────────────────────────────────────────────────────────
# Performance Optimization Settings
# ──────────────────────────────────────────────────────────────
# Database Connection Pool Settings
DB_POOL_SIZE = int(os.getenv('DB_POOL_SIZE', '8'))
DB_POOL_MAX_OVERFLOW = int(os.getenv('DB_POOL_MAX_OVERFLOW', '15'))
DB_POOL_TIMEOUT = int(os.getenv('DB_POOL_TIMEOUT', '30'))
DB_POOL_RECYCLE = int(os.getenv('DB_POOL_RECYCLE', '1800'))  # 30 minutes

# Query Execution Timeouts
SQL_EXECUTION_TIMEOUT = int(os.getenv('SQL_EXECUTION_TIMEOUT', '180'))  # 3 minutes
SQL_GENERATION_TIMEOUT = int(os.getenv('SQL_GENERATION_TIMEOUT', '30'))  # 30 seconds
QUERY_UNDERSTANDING_TIMEOUT = int(os.getenv('QUERY_UNDERSTANDING_TIMEOUT', '60'))  # 1 minute

# Caching Configuration
CACHE_DEFAULT_TTL = int(os.getenv('CACHE_DEFAULT_TTL', '600'))  # 10 minutes
CACHE_MAX_SIZE = int(os.getenv('CACHE_MAX_SIZE', '1000'))
CACHE_CLEANUP_INTERVAL = int(os.getenv('CACHE_CLEANUP_INTERVAL', '300'))  # 5 minutes

# Async Processing Configuration
MAX_CONCURRENT_DB_OPERATIONS = int(os.getenv('MAX_CONCURRENT_DB_OPERATIONS', '5'))
MAX_CONCURRENT_SQL_GENERATIONS = int(os.getenv('MAX_CONCURRENT_SQL_GENERATIONS', '3'))
TASK_QUEUE_MAX_SIZE = int(os.getenv('TASK_QUEUE_MAX_SIZE', '100'))

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING = os.getenv('ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true'
PERFORMANCE_METRICS_RETENTION_HOURS = int(os.getenv('PERFORMANCE_METRICS_RETENTION_HOURS', '24'))
BOTTLENECK_DETECTION_ENABLED = os.getenv('BOTTLENECK_DETECTION_ENABLED', 'true').lower() == 'true'

# ──────────────────────────────────────────────────────────────
# Authentication & Security Settings (Enterprise Standards)
# ──────────────────────────────────────────────────────────────
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')
JWT_REFRESH_SECRET_KEY = os.getenv('JWT_REFRESH_SECRET_KEY')

# Enterprise-standard token lifetimes
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv('ACCESS_TOKEN_EXPIRE_MINUTES', '240'))  # 4 hours
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv('REFRESH_TOKEN_EXPIRE_DAYS', '30'))      # 30 days

# Extended session support ("Remember Me" functionality)
REMEMBER_ME_TOKEN_EXPIRE_DAYS = int(os.getenv('REMEMBER_ME_TOKEN_EXPIRE_DAYS', '90'))  # 90 days
REMEMBER_ME_ENABLED = os.getenv('REMEMBER_ME_ENABLED', 'true').lower() == 'true'

# Sliding sessions - extend session on activity
SLIDING_SESSION_ENABLED = os.getenv('SLIDING_SESSION_ENABLED', 'true').lower() == 'true'
SLIDING_SESSION_EXTEND_MINUTES = int(os.getenv('SLIDING_SESSION_EXTEND_MINUTES', '60'))  # Extend by 1 hour on activity

# Auto-refresh tokens when they're close to expiring (minutes before expiration)
AUTO_REFRESH_THRESHOLD_MINUTES = int(os.getenv('AUTO_REFRESH_THRESHOLD_MINUTES', '30'))  # Refresh 30 min before expiry

# Session security
MAX_CONCURRENT_SESSIONS = int(os.getenv('MAX_CONCURRENT_SESSIONS', '5'))  # Max 5 active sessions per user
SESSION_FINGERPRINTING = os.getenv('SESSION_FINGERPRINTING', 'true').lower() == 'true'

# Credential encryption settings
CREDENTIAL_ENCRYPTION_KEY = os.getenv('CREDENTIAL_ENCRYPTION_KEY')

# ──────────────────────────────────────────────────────────────
# Database Settings
# ──────────────────────────────────────────────────────────────
AUTH_DATABASE_URL = os.getenv('AUTH_DATABASE_URL', 'sqlite:///./auth.db')

# ──────────────────────────────────────────────────────────────
# OAuth Settings
# ──────────────────────────────────────────────────────────────
GOOGLE_CLIENT_ID = os.getenv('GOOGLE_CLIENT_ID', '')
GOOGLE_CLIENT_SECRET = os.getenv('GOOGLE_CLIENT_SECRET', '')
GOOGLE_REDIRECT_URI = os.getenv('GOOGLE_REDIRECT_URI', 'http://localhost:8000/api/auth/google/callback')
BASE_URL = os.getenv('BASE_URL', 'http://localhost:8000')

# ──────────────────────────────────────────────────────────────
# Generate fallback keys only if not provided in environment
# ──────────────────────────────────────────────────────────────
if not JWT_SECRET_KEY:
    JWT_SECRET_KEY = secrets.token_hex(32)
    
if not JWT_REFRESH_SECRET_KEY:
    JWT_REFRESH_SECRET_KEY = secrets.token_hex(32) 