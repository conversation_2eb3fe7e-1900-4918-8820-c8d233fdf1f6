"""Planner Agent

This module provides a planner agent that determines the next action for the orchestrator.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from enum import Enum

from pydantic import BaseModel, Field, ValidationError, model_validator
from app.utils.bedrock_client import BedrockClient

logger = logging.getLogger(__name__)

class ActionType(str, Enum):
    """Valid action types for the orchestrator."""
    ASK_USER_CLARIFICATION = "ASK_USER_CLARIFICATION"
    GET_SCHEMA = "GET_SCHEMA"
    GENERATE_SQL = "GENERATE_SQL"
    EXECUTE_QUERY = "EXECUTE_QUERY"
    FORMAT_RESULTS = "FORMAT_RESULTS"
    RESPOND_TO_USER = "RESPOND_TO_USER"

class ActionDetails(BaseModel):
    """Details of an orchestration action."""
    content: str = Field(description="The specific content for this action")
    targets: Optional[List[str]] = Field(default_factory=list, description="Target database IDs or agent types")

class ActionPlan(BaseModel):
    """The plan for the next orchestration action."""
    thought_process: str = Field(description="Reasoning about the query and approach")
    action: ActionType = Field(description="The type of action to take")
    action_details: ActionDetails = Field(description="Details of the action to take")
    
    @model_validator(mode='after')
    def validate_action_details(self):
        """Validate that action details are appropriate for the action type."""
        # Add validation logic here if needed, especially if certain actions require specific fields
        return self

class Planner:
    """Agent responsible for planning the next orchestration action."""
    
    # The core system prompt for the Planner
    SYSTEM_PROMPT = """
System Prompt: Orchestrator Agent

You are the Orchestrator Agent, the central coordinator for a multi-agent platform that allows users to query databases using natural language.

Your Mission: Seamlessly understand user requests, manage interactions with specialized agents (Database Manager, SQL Agent, Output Agent), and deliver accurate, well-formatted data results to the user.

Core Principles to Uphold at All Times:
1.  Accuracy First: The correctness of the retrieved information is paramount.
2.  User-Centricity: Always focus on the user's intent and communicate clearly.
3.  Efficient Collaboration: Clearly instruct and interpret responses from other agents.
4.  Context is Key: Diligently maintain and utilize conversational history.

Your Core Responsibilities (Execute these for every user request):
1.  Analyze User Query: Deeply understand the user's core intent, identify key entities, attributes, values, filters, relationships, and any temporal constraints from their natural language.
2.  Resolve Ambiguity & Seek Clarification: If the query is unclear or lacks critical information, formulate specific, polite questions for the user. Do not proceed with risky assumptions.
3.  Database & Schema Scoping: Based on the query and conversation history, determine the relevant database(s). Engage the Database Manager Agent(s) to identify and retrieve necessary schema information (tables, columns, types, relationships).
4.  Plan, Delegate & Validate SQL Generation: Develop a logical query plan. Provide a comprehensive brief to the SQL Agent (including user query, target database(s), specific schema elements, and your plan). Critically review the SQL Agent's proposed query. If flawed, provide feedback and iterate until correct.
5.  Oversee Execution & Result Collation: Instruct the Database Manager Agent(s) to execute the validated query. If necessary, aggregate or synthesize results from multiple queries/sources.
6.  Format & Present Output: Delegate to the Output Agent to format the final data according to the requested output format (or a sensible default). Deliver the response clearly to the user.

Critical Operating Procedure (Internal "Chain-of-Thought" before acting):
*   What is the user's precise goal for this turn?
*   What information do I have? What's critically missing?
*   Which specialized agent is needed for the next immediate step?
*   What are the exact instructions and data that agent needs from me?
*   How will I verify the quality and correctness of that agent's output?
*   How will I communicate the outcome (or any issues) clearly to the user?

Always prioritize clarity in your reasoning, accuracy in execution, and overall user satisfaction. If errors occur that you cannot resolve, explain them transparently to the user.

Based on the user query and context, output a structured JSON object with your plan:

{
  "thought_process": "Your internal reasoning about the query, context, and what approach to take",
  "action": "One of: ASK_USER_CLARIFICATION, GET_SCHEMA, GENERATE_SQL, EXECUTE_QUERY, FORMAT_RESULTS, RESPOND_TO_USER",
  "action_details": {
    "content": "The specific content for this action (clarification question, schema request, SQL brief, etc.)",
    "targets": ["target_database_ids or agent_types for this action"]
  }
}
"""

    def __init__(self, bedrock_client: BedrockClient):
        """Initialize the planner agent.
        
        Args:
            bedrock_client: Client for calling Bedrock LLM
        """
        self.bedrock_client = bedrock_client
        self.retry_count = 0
        self.max_retries = 1
    
    def _extract_and_validate_action_plan(self, response: str) -> Optional[Dict[str, Any]]:
        """Extract and validate the action plan JSON from the LLM response."""
        start_idx = response.find("{")
        end_idx = response.rfind("}") + 1
        if start_idx >= 0 and end_idx > start_idx:
            json_str = response[start_idx:end_idx]
            try:
                action_plan = ActionPlan.model_validate_json(json_str)
                return action_plan.model_dump()
            except ValidationError as e:
                raise e
        else:
            raise ValueError("No JSON structure found in response")

    async def get_plan(self, context: str) -> Dict[str, Any]:
        """Get the next action plan from the LLM."""
        self.retry_count = 0
        while self.retry_count <= self.max_retries:
            try:
                response = await self.bedrock_client.generate_response(
                    prompt=context,
                    system_prompt=self.SYSTEM_PROMPT,
                    temperature=0.2
                )
                try:
                    return self._extract_and_validate_action_plan(response)
                except ValidationError as e:
                    if self.retry_count < self.max_retries:
                        self.retry_count += 1
                        context += f"\n\nYour previous response had validation errors. Please fix them:\n{e}\n\nPlease retry with a valid JSON response."
                        logger.warning(f"LLM response validation failed, retrying: {e}")
                        continue
                    else:
                        logger.error(f"LLM response validation failed after retries: {e}")
                        return self._fallback_action(f"Failed to validate response: {e}")
                except (json.JSONDecodeError, ValueError) as e:
                    if self.retry_count < self.max_retries:
                        self.retry_count += 1
                        context += f"\n\nYour previous response had JSON parsing errors: {str(e)}. Please provide a valid JSON response as shown in the system prompt."
                        logger.warning(f"Error parsing LLM response: {str(e)}. Response: {response[:100]}...")
                        continue
                    else:
                        logger.error(f"Error parsing LLM response after retries: {str(e)}")
                        return self._fallback_action(f"Failed to parse JSON: {str(e)}")
            except Exception as e:
                logger.error(f"Error getting next orchestration action: {str(e)}")
                return self._fallback_action(f"Error calling LLM: {str(e)}")
        return self._fallback_action("Unknown error in planner")
    
    def _fallback_action(self, error_message: str) -> Dict[str, Any]:
        """Create a fallback action when things go wrong.
        
        Args:
            error_message: Description of what went wrong
            
        Returns:
            A fallback action plan
        """
        return {
            "action": ActionType.ASK_USER_CLARIFICATION.value,
            "action_details": {
                "content": "I'm having trouble processing your request. Could you try rephrasing it?",
                "targets": []
            },
            "thought_process": f"Error in planning process: {error_message}"
        } 
