// Main type exports - barrel file for easy imports

// Re-export all types from individual modules
export * from './api';
export * from './auth';
export * from './profile';
export * from './streaming';
export * from './chart';
export * from './dashboard';
export type {
  ChatHistoryItem,
  ChatMessage,
  ChatHistoryContextType,
  ChatProps
} from './chat';

// Common utility types
export interface BaseEntity {
  id: string;
  created_at: Date;
  updated_at: Date;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

export interface PaginationResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

export interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme?: Theme;
  mounted: boolean;
}
