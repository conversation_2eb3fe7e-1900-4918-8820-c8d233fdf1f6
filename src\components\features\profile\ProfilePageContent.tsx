"use client";
import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { useApi } from '@/providers/ApiContext';
import { useAuth } from '@/providers/AuthContext';
import { usePageTitle } from '@/hooks/usePageTitle';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  User,
  Mail,
  Calendar,
  Clock,
  Edit3,
  Save,
  X,
  Camera,
  Shield,
  Key,
  AlertTriangle,
  CheckCircle,
  Upload,
  RefreshCw
} from 'lucide-react';

interface UserProfile {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  bio?: string;
  profile_picture_url?: string;
  created_at: string;
  last_login?: string;
  auth_method?: 'email' | 'google_oauth' | 'github_oauth';
  email_verified?: boolean;
  is_active?: boolean;
}

interface ProfileFormData {
  full_name: string;
  bio: string;
  email: string;
}

const ProfilePageContent = () => {
  const { getUserProfile } = useApi();
  const { user } = useAuth();

  // Set page title - memoized to prevent re-renders
  const pageConfig = useMemo(() => ({
    title: 'Profile',
    icon: User
  }), []);

  usePageTitle(pageConfig);

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const [formData, setFormData] = useState<ProfileFormData>({
    full_name: '',
    bio: '',
    email: '',
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  const fetchProfile = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const profileData = await getUserProfile();
      setProfile(profileData);
      setFormData({
        full_name: profileData.full_name || '',
        bio: profileData.bio || '',
        email: profileData.email || '',
      });
    } catch (err: any) {
      console.error('Failed to fetch user profile:', err);
      setError('Failed to load profile information. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [getUserProfile]);

  useEffect(() => {
    fetchProfile();
  }, [fetchProfile]);

  const validateForm = useCallback(() => {
    const errors: Record<string, string> = {};

    if (!formData.full_name.trim()) {
      errors.full_name = 'Full name is required';
    } else if (formData.full_name.trim().length < 2) {
      errors.full_name = 'Full name must be at least 2 characters';
    }

    if (formData.email && !formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      errors.email = 'Please enter a valid email address';
    }

    if (formData.bio.length > 500) {
      errors.bio = 'Bio must be less than 500 characters';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData]);

  const handleInputChange = useCallback((field: keyof ProfileFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [validationErrors]);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
    setError(null);
    setSuccessMessage(null);
  }, []);

  const handleCancel = useCallback(() => {
    setIsEditing(false);
    setValidationErrors({});
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        bio: profile.bio || '',
        email: profile.email || '',
      });
    }
  }, [profile]);

  const handleSave = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      // TODO: Implement updateProfile API call
      // const updatedProfile = await updateProfile(formData);
      
      // For now, simulate the API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Update local profile state
      if (profile) {
        setProfile({ ...profile, ...formData });
      }
      
      setIsEditing(false);
      setSuccessMessage('Profile updated successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err: any) {
      console.error('Failed to update profile:', err);
      setError('Failed to update profile. Please try again.');
    } finally {
      setIsSaving(false);
    }
  }, [formData, validateForm, profile]);

  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }, []);

  const getAuthMethodDisplay = useCallback((method?: string) => {
    switch (method) {
      case 'google_oauth':
        return { label: 'Google OAuth', color: 'bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300' };
      case 'github_oauth':
        return { label: 'GitHub OAuth', color: 'bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300' };
      case 'email':
        return { label: 'Email & Password', color: 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300' };
      default:
        return { label: 'Unknown', color: 'bg-gray-100 dark:bg-gray-900 text-gray-700 dark:text-gray-300' };
    }
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Header Skeleton */}
            <div className="text-center space-y-4">
              <div className="inline-flex items-center gap-3 p-4 bg-white dark:bg-slate-900 rounded-2xl shadow-sm border">
                <div className="p-2 rounded-xl bg-slate-200 dark:bg-slate-700 w-12 h-12 animate-pulse"></div>
                <div className="text-left">
                  <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-32 mb-2 animate-pulse"></div>
                  <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-48 animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Profile Card Skeleton */}
            <Card className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded w-1/4 animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 animate-pulse"></div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4 animate-pulse"></div>
                <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2 animate-pulse"></div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (error && !profile) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto">
            <Card className="border-red-200 dark:border-red-800">
              <CardContent className="p-6 text-center">
                <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-2">
                  Failed to Load Profile
                </h3>
                <p className="text-red-700 dark:text-red-300 mb-4">{error}</p>
                <Button onClick={fetchProfile} variant="outline">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="container mx-auto p-4 sm:p-6 lg:p-8">
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Removed hardcoded header - now using centralized header system */}

            {/* Success/Error Messages */}
            {successMessage && (
              <div className="p-4 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 flex-shrink-0" />
                  <div className="text-green-800 dark:text-green-200">
                    <div className="font-medium">Success</div>
                    <div className="text-sm">{successMessage}</div>
                  </div>
                </div>
              </div>
            )}

            {error && (
              <div className="p-4 bg-red-50 dark:bg-red-950 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 flex-shrink-0" />
                  <div className="text-red-800 dark:text-red-200">
                    <div className="font-medium">Error</div>
                    <div className="text-sm">{error}</div>
                  </div>
                </div>
              </div>
            )}

            {/* Profile Information Card */}
            <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700">
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100">
                    Profile Information
                  </CardTitle>
                  <CardDescription className="text-slate-600 dark:text-slate-400">
                    Your personal details and account information
                  </CardDescription>
                </div>
                {!isEditing && (
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        onClick={handleEdit}
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <Edit3 className="h-4 w-4" />
                        Edit
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Edit your profile information</p>
                    </TooltipContent>
                  </Tooltip>
                )}
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Profile Picture Section */}
                <div className="flex items-center gap-6">
                  <div className="relative">
                    <div className="w-20 h-20 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                      {profile?.profile_picture_url ? (
                        <img
                          src={profile.profile_picture_url}
                          alt="Profile"
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        (profile?.full_name || profile?.username || 'U').charAt(0).toUpperCase()
                      )}
                    </div>
                    {isEditing && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="sm"
                            variant="outline"
                            className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                          >
                            <Camera className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Change profile picture</p>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100">
                      {profile?.full_name || profile?.username || 'Unknown User'}
                    </h3>
                    <p className="text-slate-600 dark:text-slate-400">
                      @{profile?.username}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">
                        User ID: {profile?.id.slice(0, 8)}...
                      </Badge>
                      {profile?.is_active && (
                        <Badge className="bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800 text-xs">
                          <CheckCircle className="w-3 h-3 mr-1" />
                          Active
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Form Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Full Name */}
                  <div className="space-y-2">
                    <Label htmlFor="full_name" className="text-sm font-medium">
                      Full Name
                    </Label>
                    {isEditing ? (
                      <div>
                        <Input
                          id="full_name"
                          value={formData.full_name}
                          onChange={(e) => handleInputChange('full_name', e.target.value)}
                          placeholder="Enter your full name"
                          className={validationErrors.full_name ? 'border-red-300 dark:border-red-700' : ''}
                        />
                        {validationErrors.full_name && (
                          <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                            {validationErrors.full_name}
                          </p>
                        )}
                      </div>
                    ) : (
                      <p className="text-slate-900 dark:text-slate-100 py-2">
                        {profile?.full_name || 'Not set'}
                      </p>
                    )}
                  </div>

                  {/* Email */}
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email Address
                    </Label>
                    {isEditing ? (
                      <div>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="Enter your email"
                          className={validationErrors.email ? 'border-red-300 dark:border-red-700' : ''}
                        />
                        {validationErrors.email && (
                          <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                            {validationErrors.email}
                          </p>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 py-2">
                        <Mail className="h-4 w-4 text-slate-500" />
                        <span className="text-slate-900 dark:text-slate-100">
                          {profile?.email || 'Not set'}
                        </span>
                        {profile?.email_verified && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Email verified</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Bio */}
                  <div className="space-y-2 md:col-span-2">
                    <Label htmlFor="bio" className="text-sm font-medium">
                      Bio
                    </Label>
                    {isEditing ? (
                      <div>
                        <Textarea
                          id="bio"
                          value={formData.bio}
                          onChange={(e) => handleInputChange('bio', e.target.value)}
                          placeholder="Tell us about yourself..."
                          rows={3}
                          className={validationErrors.bio ? 'border-red-300 dark:border-red-700' : ''}
                        />
                        <div className="flex justify-between items-center mt-1">
                          {validationErrors.bio ? (
                            <p className="text-sm text-red-600 dark:text-red-400">
                              {validationErrors.bio}
                            </p>
                          ) : (
                            <div />
                          )}
                          <p className="text-xs text-slate-500 dark:text-slate-400">
                            {formData.bio.length}/500
                          </p>
                        </div>
                      </div>
                    ) : (
                      <p className="text-slate-900 dark:text-slate-100 py-2">
                        {profile?.bio || 'No bio available'}
                      </p>
                    )}
                  </div>
                </div>

                {/* Edit Actions */}
                {isEditing && (
                  <div className="flex justify-end gap-3 pt-4 border-t border-slate-200 dark:border-slate-700">
                    <Button
                      onClick={handleCancel}
                      variant="outline"
                      disabled={isSaving}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isSaving ? (
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Account Details Card */}
            <Card className="bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100 flex items-center gap-2">
                  <Shield className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  Account Details
                </CardTitle>
                <CardDescription className="text-slate-600 dark:text-slate-400">
                  Information about your account and authentication
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Account Created */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Account Created
                    </Label>
                    <div className="flex items-center gap-2 text-slate-900 dark:text-slate-100">
                      <Calendar className="h-4 w-4 text-slate-500" />
                      <span>{profile?.created_at ? formatDate(profile.created_at) : 'Unknown'}</span>
                    </div>
                  </div>

                  {/* Last Login */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Last Login
                    </Label>
                    <div className="flex items-center gap-2 text-slate-900 dark:text-slate-100">
                      <Clock className="h-4 w-4 text-slate-500" />
                      <span>
                        {profile?.last_login ? formatDate(profile.last_login) : 'Unknown'}
                      </span>
                    </div>
                  </div>

                  {/* Authentication Method */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Authentication Method
                    </Label>
                    <div className="flex items-center gap-2">
                      <Key className="h-4 w-4 text-slate-500" />
                      <Badge 
                        variant="outline" 
                        className={getAuthMethodDisplay(profile?.auth_method).color}
                      >
                        {getAuthMethodDisplay(profile?.auth_method).label}
                      </Badge>
                    </div>
                  </div>

                  {/* Account Status */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-slate-700 dark:text-slate-300">
                      Account Status
                    </Label>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-slate-500" />
                      <Badge 
                        variant="outline"
                        className={profile?.is_active 
                          ? "bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800"
                          : "bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-300 border-red-200 dark:border-red-800"
                        }
                      >
                        {profile?.is_active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default ProfilePageContent; 