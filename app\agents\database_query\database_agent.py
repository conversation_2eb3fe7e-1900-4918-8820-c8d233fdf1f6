"""
Enhanced Database Agent with Autonomous Decision-Making Capabilities
==================================================================

A sophisticated AI agent that uses tool-based architecture for intelligent database operations.
Inspired by the report_orchestrator_agent architecture with ReAct framework integration.

🧠 CORE PHILOSOPHY:
- Works like a real database expert: analytical, methodical, context-aware
- Uses specialized tools based on database operation requirements
- Can perform complex multi-step database analysis and optimization
- Makes autonomous decisions about which database operations to perform
- Understands database relationships, patterns, and optimization opportunities

🛠️ AVAILABLE TOOLS:
- analyze_schema: Deep schema understanding and relationship mapping
- profile_data: Statistical profiling of table contents and data quality
- generate_optimized_query: Advanced SQL generation with performance optimization
- validate_query: Query validation, safety checks, and execution planning
- discover_relationships: Automatic relationship and constraint detection
- suggest_optimizations: Database structure and query optimization recommendations
- execute_analysis: Safe query execution with result analysis
- explain_operations: Detailed explanation of database operations and results

🤖 DECISION MAKING:
- Analyzes database structure and user requirements to understand intent
- Determines optimal sequence of database operations
- Chooses appropriate tools based on database type, complexity, and user goals
- Makes iterative tool calls with feedback loops for continuous improvement
- Provides detailed reasoning for each database operation decision
"""

import asyncio
import json
import logging
import time
import uuid
from datetime import datetime
from typing import Any, AsyncGenerator, Dict, List, Optional, Set, Tuple

from app.agents.base import Agent, AgentResponse, StandardizedAgentOutputs
from app.models.database import Database, DatabaseTable, DatabaseColumn, DatabaseType
from app.services.database_service import DatabaseService
from app.utils.bedrock_client import BedrockClient, ModelPurpose
from app.prompts.database import RELEVANT_TABLES_SYSTEM_PROMPT

logger = logging.getLogger(__name__)

# Configuration
_MAX_TOOL_CALLS = 8  # Maximum tool calls per database operation
_TOOL_TIMEOUT = 300  # 5 minutes max per tool call (increased for complex queries)

class EnhancedDatabaseAgent(Agent):
    """
    Enhanced Database Agent with autonomous decision-making capabilities.

    Uses tool-based architecture similar to report_orchestrator_agent for intelligent
    database operations including schema analysis, query optimization, and data profiling.
    """

    def __init__(
        self,
        database: Database,
        database_service: DatabaseService,
        agent_id: Optional[str] = None,
        parent_agent_id: Optional[str] = None,
        tables_subset: Optional[List[str]] = None
    ):
        """Initialize the enhanced database agent.

        Args:
            database: The database this agent is responsible for
            database_service: The database service for interacting with the database
            agent_id: Optional ID for this agent (generated if not provided)
            parent_agent_id: Optional ID of a parent agent if this is a sub-agent
            tables_subset: Optional subset of tables this agent is responsible for
        """
        self.database = database
        self.database_service = database_service
        self.agent_id = agent_id or f"enhanced_db_agent_{uuid.uuid4().hex[:8]}"
        self.parent_agent_id = parent_agent_id
        self.tables_subset = tables_subset

        # AI and decision making components
        self.bedrock_client = BedrockClient(purpose=ModelPurpose.ANALYSIS)

        # Database metadata and state
        self.tables: List[DatabaseTable] = []
        self.schema_analysis: Dict[str, Any] = {}
        self.data_profiles: Dict[str, Any] = {}
        self.relationship_map: Dict[str, Any] = {}

        # Tool execution state
        self.tool_execution_history: List[Dict[str, Any]] = []
        self.current_operation_context: Dict[str, Any] = {}

        self.initialized = False
        
    async def initialize(self) -> None:
        """Initialize the agent by connecting to the database and caching metadata."""
        if self.initialized:
            return

        logger.info(f"Initializing database agent {self.agent_id} for database {self.database.name}")
        start_time = time.time()

        # Connect to the database
        success, error = await self.database_service.connect_database(self.database)
        if not success:
            logger.error(f"Failed to initialize agent {self.agent_id}: {error}")
            raise RuntimeError(f"Failed to connect to database: {error}")

        # Get list of tables
        try:
            table_names = await self.database_service.list_tables(self.database.id, schema=self.database.credentials.db_schema)

            logger.info(f"Found {len(table_names)} tables in database {self.database.name}")

            # Filter to subset if specified
            if self.tables_subset:
                table_names = [t for t in table_names if t in self.tables_subset]
                logger.info(f"Filtered to {len(table_names)} tables based on subset")

            # Cache table metadata with optimized batch processing
            self.tables = []
            metadata_start = time.time()

            # Process tables in smaller batches to avoid timeouts
            batch_size = 5
            for i in range(0, len(table_names), batch_size):
                batch = table_names[i:i + batch_size]
                logger.debug(f"Processing metadata batch {i//batch_size + 1}: {batch}")

                for table_name in batch:
                    try:
                        table = await self.database_service.get_table_metadata(
                            self.database.id,
                            table_name,
                            schema=self.database.credentials.db_schema
                        )
                        self.tables.append(table)
                    except Exception as e:
                        logger.warning(f"Error getting metadata for table {table_name}: {str(e)}")
                        # Create a basic table entry even if metadata fails
                        from app.models.database import DatabaseTable
                        basic_table = DatabaseTable(name=table_name, schema=self.database.credentials.db_schema)
                        self.tables.append(basic_table)

            metadata_time = time.time() - metadata_start
            total_time = time.time() - start_time

            self.initialized = True
            logger.info(f"Agent {self.agent_id} initialized with {len(self.tables)} tables in {total_time:.2f}s (metadata: {metadata_time:.2f}s)")

        except Exception as e:
            logger.error(f"Error initializing agent {self.agent_id}: {str(e)}")
            raise

    async def process_sync(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Non-streaming version of process for backward compatibility.
        Optimized for performance with simplified logic.
        """
        if not self.initialized:
            await self.initialize()

        # Handle legacy "get_schema" action for backward compatibility
        action = message.get("action", "analyze")
        if action == "get_schema":
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={
                    "database_id": self.database.id,
                    "database_name": self.database.name,
                    "database_type": self.database.db_type.value,
                    "tables": [self._table_to_dict(t) for t in self.tables]
                }
            ).to_dict()

        # For other actions, check if we have relevant tables for the query
        user_query = message.get("query", "")
        target_tables = message.get("target_tables", [])

        # If specific tables are targeted, use those
        if target_tables:
            relevant_tables = []
            for table in self.tables:
                if table.name in target_tables:
                    relevant_tables.append(self._table_to_dict(table))

            if relevant_tables:
                logger.info(f"Found {len(relevant_tables)} targeted tables in database {self.database.name}")
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=True,
                    data={
                        "database_id": self.database.id,
                        "database_name": self.database.name,
                        "database_type": self.database.db_type.value,
                        "tables": relevant_tables
                    }
                ).to_dict()

        # Otherwise, find relevant tables based on query
        if user_query:
            logger.info(f"Searching for relevant tables in database {self.database.name}")

            # Universal table discovery based on query analysis
            relevant_tables = await self._find_relevant_tables(user_query)

            if relevant_tables:
                logger.info(f"Found {len(relevant_tables)} relevant tables in database {self.database.name}")
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=True,
                    data={
                        "database_id": self.database.id,
                        "database_name": self.database.name,
                        "database_type": self.database.db_type.value,
                        "tables": relevant_tables
                    }
                ).to_dict()

        # No relevant information found
        logger.info(f"No relevant tables found in database {self.database.name} for query: {user_query}")
        return AgentResponse(
            agent_id=self.agent_id,
            has_relevant_info=False,
            data={
                "database_id": self.database.id,
                "database_name": self.database.name,
                "database_type": self.database.db_type.value,
                "tables": []
            }
        ).to_dict()

    async def _find_relevant_tables(self, query: str) -> List[Dict[str, Any]]:
        """Find tables relevant to the user query using enhanced keyword matching."""
        if not query:
            return []

        query_lower = query.lower()
        relevant_tables = []
        scored_tables = []

        # Universal scoring based on query terms and common database patterns
        query_words = [word for word in query_lower.split() if len(word) > 2]  # Skip very short words

        # Score tables based on relevance
        for table in self.tables:
            score = 0
            table_name_lower = table.name.lower()

            # Primary scoring: Direct query word matches in table names
            for word in query_words:
                if word in table_name_lower:
                    score += 15  # Very high score for direct query word matches in table name

            # Secondary scoring: Query word matches in column names
            for column in table.columns:
                column_name_lower = column.name.lower()
                for word in query_words:
                    if word in column_name_lower:
                        score += 8  # High score for query word matches in column names

            # Tertiary scoring: Common database relationship patterns
            # Look for common table naming patterns that might be relevant
            common_patterns = ['user', 'customer', 'order', 'product', 'item', 'record', 'data', 'info', 'detail', 'log', 'history', 'transaction', 'payment', 'account']
            for pattern in common_patterns:
                if pattern in table_name_lower:
                    # Only add small bonus if query contains related terms
                    for word in query_words:
                        if any(related in word for related in ['get', 'find', 'show', 'list', 'all', 'total', 'count']):
                            score += 3  # Small bonus for common query patterns
                            break

            # Additional scoring for column name relevance
            for column in table.columns:
                column_name_lower = column.name.lower()
                # Look for columns that might contain the data being queried
                for word in query_words:
                    if word in column_name_lower or column_name_lower in word:
                        score += 5  # Medium score for column relevance

            if score > 0:
                scored_tables.append((score, table))
                logger.debug(f"Table {table.name} scored {score} points")

        # Sort by score and take top tables
        scored_tables.sort(key=lambda x: x[0], reverse=True)

        # Take top scoring tables
        for score, table in scored_tables[:15]:  # Increased limit for better coverage
            relevant_tables.append(self._table_to_dict(table))

        # If no specific matches found, return a sample of all tables
        if not relevant_tables and self.tables:
            logger.info(f"No keyword matches found, returning sample of {min(10, len(self.tables))} tables")
            relevant_tables = [self._table_to_dict(table) for table in self.tables[:10]]

        logger.info(f"Found {len(relevant_tables)} relevant tables out of {len(self.tables)} total tables")
        return relevant_tables



    async def process(self, message: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Main processing method with tool-based autonomous decision making.

        ENHANCED WORKFLOW:
        =================
        1. 🧠 ANALYZE_REQUEST - Understand database operation requirements
        2. 🛠️ CHOOSE_TOOLS - Decide which database tools to use based on intent
        3. 🔄 ITERATIVE_EXECUTION - Execute tools with feedback loops
        4. 🎯 VALIDATE_COMPLETION - Ensure database operation goals are met

        Args:
            message: The message containing database operation requirements

        Yields:
            Stream of events showing tool execution progress and results
        """
        if not self.initialized:
            await self.initialize()

        # Extract message details
        user_query = message.get("query", "")
        action = message.get("action", "analyze")
        target_tables = message.get("target_tables", [])
        target_columns = message.get("target_columns", {})
        operation_mode = message.get("operation_mode", "comprehensive")  # comprehensive, focused, optimization

        logger.info("=" * 80)
        logger.info("🤖 STARTING ENHANCED DATABASE AGENT PROCESSING")
        logger.info(f"🗄️ Database: {self.database.name} ({self.database.db_type})")
        logger.info(f"🎯 Action: {action}")
        logger.info(f"❓ Query: {user_query}")
        logger.info(f"📊 Mode: {operation_mode}")
        logger.info("=" * 80)

        # Initialize operation state
        operation_state = {
            "user_query": user_query,
            "action": action,
            "target_tables": target_tables,
            "target_columns": target_columns,
            "operation_mode": operation_mode,
            "database_info": {
                "id": self.database.id,
                "name": self.database.name,
                "type": self.database.db_type.value,
                "table_count": len(self.tables)
            },

            # Tool execution state
            "tool_calls": [],
            "schema_analysis_complete": False,
            "data_profiling_complete": False,
            "query_generation_complete": False,
            "optimization_analysis_complete": False,

            # Results accumulation
            "relevant_tables": [],
            "generated_queries": [],
            "optimization_suggestions": [],
            "analysis_insights": [],

            # Control
            "task_complete": False,
            "tool_call_count": 0
        }

        # Handle legacy "get_schema" action for backward compatibility
        if action == "get_schema":
            yield self._create_event("agent_result", "database_agent", {
                "message": "📋 Returning database schema",
                "status": "completed",
                "data": {
                    "database_id": self.database.id,
                    "database_name": self.database.name,
                    "database_type": self.database.db_type.value,
                    "tables": [self._table_to_dict(t) for t in self.tables]
                }
            })
            return

        # Main tool-based processing loop
        while not operation_state["task_complete"] and operation_state["tool_call_count"] < _MAX_TOOL_CALLS:
            operation_state["tool_call_count"] += 1
            call_number = operation_state["tool_call_count"]

            logger.info(f"\n{'='*20} TOOL CALL {call_number}/{_MAX_TOOL_CALLS} {'='*20}")

            # 1. ANALYZE - What database operation should we perform next?
            logger.info("🧠 ANALYZE: Determining next database operation...")
            decision = await self._make_intelligent_tool_decision(operation_state)

            tool_name = decision["tool"]
            reasoning = decision["reasoning"]
            parameters = decision.get("parameters", {})

            logger.info(f"🛠️ TOOL DECISION: {tool_name}")
            logger.info(f"💭 REASONING: {reasoning}")

            # 2. EXECUTE - Use the chosen database tool
            logger.info(f"⚡ EXECUTING DATABASE TOOL: {tool_name}")
            async for event in self._execute_database_tool(tool_name, parameters, operation_state):
                yield event

            # 3. CAPTURE OUTCOME - Store the result for context
            tool_outcome = await self._capture_tool_outcome(tool_name, operation_state)

            # 4. UPDATE - Record tool call with outcome
            operation_state["tool_calls"].append({
                "call_number": call_number,
                "tool": tool_name,
                "reasoning": reasoning,
                "parameters": parameters,
                "outcome": tool_outcome,
                "timestamp": datetime.utcnow().isoformat()
            })

            # 5. CHECK COMPLETION - Are we done?
            if decision.get("complete", False) or self._check_operation_completion(operation_state):
                operation_state["task_complete"] = True
                logger.info("🎉 DATABASE OPERATION COMPLETED SUCCESSFULLY!")

        # Final completion message
        if not operation_state["task_complete"]:
            logger.warning(f"⚠️ Reached maximum tool calls ({_MAX_TOOL_CALLS}) without completion")
            yield self._create_event("agent_result", "database_agent", {
                "message": f"⚠️ Complex database operation required {_MAX_TOOL_CALLS}+ tool calls. Results may be partial.",
                "status": "partial_completion",
                "tool_calls_made": operation_state["tool_call_count"]
            })
        else:
            # Create comprehensive final response
            final_response = self._create_comprehensive_response(operation_state)
            yield self._create_event("agent_result", "database_agent", {
                "message": "🎉 Database operation completed successfully!",
                "status": "completed",
                "tool_calls_made": operation_state["tool_call_count"],
                "data": final_response
            })

    async def _make_intelligent_tool_decision(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        Use LLM to make intelligent decisions about which database tool to use next.
        This is the core intelligence of the enhanced database agent.
        """

        # Build comprehensive context for decision making
        context = await self._build_tool_decision_context(state)

        system_prompt = """You are an expert database administrator and analyst with access to specialized database tools. Your job is to analyze the database operation requirements and decide which tool to use next.

CORE PRINCIPLES:
================
1. UNDERSTAND INTENT: What database operation is the user really asking for?
2. LEVERAGE CONTEXT: Use previous tool results and database metadata
3. BE EFFICIENT: Don't repeat operations that have already been completed
4. BE METHODICAL: Work step-by-step like a real database expert
5. RECOGNIZE COMPLETION: Stop when the database operation is complete!

AVAILABLE DATABASE TOOLS:
========================
- analyze_schema: Deep analysis of database schema, relationships, and structure
- profile_data: Statistical profiling of table contents and data quality assessment
- generate_optimized_query: Generate SQL queries with performance optimization
- validate_query: Validate SQL queries for safety, syntax, and performance
- discover_relationships: Automatic detection of relationships and constraints
- suggest_optimizations: Database structure and query optimization recommendations
- execute_analysis: Safe execution of database queries with result analysis
- explain_operations: Detailed explanation of database operations and results

DECISION LOGIC:
==============
SCHEMA ANALYSIS OPERATIONS:
- If user wants to understand database structure → analyze_schema
- If need to understand data quality → profile_data
- If need to find relationships → discover_relationships

QUERY OPERATIONS:
- If user wants to query data → generate_optimized_query
- If have query but need validation → validate_query
- If need to execute query → execute_analysis

OPTIMIZATION OPERATIONS:
- If user wants performance improvements → suggest_optimizations
- If need to explain database operations → explain_operations

COMPLETION CONDITIONS (CRITICAL):
================================
✅ TASK IS COMPLETE when:
- All requested database operations have been performed
- Schema analysis completed (if requested)
- Queries generated and validated (if requested)
- Data profiling completed (if requested)
- User has received comprehensive database insights
- No errors that require fixing

⚠️ AVOID INFINITE LOOPS:
- If you see repeated tool calls of the same type
- If the same tool has been called 2+ times without progress
- Set "complete": true to break the loop

🎯 HOW TO INDICATE COMPLETION:
When the task is complete, return: {"tool": "complete", "reasoning": "Database operation completed", "complete": true}

CRITICAL: You must return ONLY valid JSON. No explanations or markdown."""

        prompt = f"""Analyze the current database operation state and decide which tool to use next.

CURRENT CONTEXT:
{context}

Based on this context, decide:
1. What database tool should be used next?
2. What parameters should be passed to that tool?
3. Why is this the best choice for this database operation?
4. Is the database operation complete after this tool call?

RESPONSE FORMAT (JSON only):
{{
    "tool": "tool_name",
    "reasoning": "Clear explanation of why this database tool was chosen",
    "parameters": {{
        "param1": "value1",
        "param2": "value2"
    }},
    "complete": false,
    "confidence": 0.9
}}

IMPORTANT EXAMPLES:
- If user asks "analyze my database structure": tool="analyze_schema"
- If user wants "data quality assessment": tool="profile_data"
- If user asks "generate query for X": tool="generate_optimized_query"
- If user wants "optimize my database": tool="suggest_optimizations"
- If query needs validation: tool="validate_query"

Respond with ONLY the JSON:"""

        try:
            response = await asyncio.wait_for(
                self.bedrock_client.generate_response(prompt=prompt, system_prompt=system_prompt, temperature=0.1),
                timeout=30.0
            )

            cleaned_response = self._clean_llm_response(response)
            decision = json.loads(cleaned_response)

            # Validate required fields
            required_fields = ["tool", "reasoning"]
            if not all(field in decision for field in required_fields):
                logger.warning(f"Invalid decision format, using fallback")
                return self._fallback_tool_decision(state)

            # Set defaults
            decision.setdefault("parameters", {})
            decision.setdefault("complete", False)
            decision.setdefault("confidence", 0.8)

            logger.debug(f"✅ Database tool decision: {decision['tool']} (confidence: {decision['confidence']})")
            return decision

        except (asyncio.TimeoutError, json.JSONDecodeError, Exception) as e:
            logger.warning(f"Database tool decision failed: {e}, using fallback")
            return self._fallback_tool_decision(state)

    async def _build_tool_decision_context(self, state: Dict[str, Any]) -> str:
        """Build comprehensive context for database tool decision making."""
        context_parts = []

        # Basic request info
        context_parts.append(f"USER QUERY: {state['user_query']}")
        context_parts.append(f"ACTION: {state['action']}")
        context_parts.append(f"OPERATION MODE: {state['operation_mode']}")
        context_parts.append(f"TOOL CALL NUMBER: {state['tool_call_count']}")

        # Database context
        db_info = state['database_info']
        context_parts.append(f"DATABASE: {db_info['name']} ({db_info['type']})")
        context_parts.append(f"TABLES AVAILABLE: {db_info['table_count']} tables")

        # Target specifications
        if state['target_tables']:
            context_parts.append(f"TARGET TABLES: {', '.join(state['target_tables'])}")
        if state['target_columns']:
            context_parts.append(f"TARGET COLUMNS: {len(state['target_columns'])} table-column mappings")

        # Current operation state
        context_parts.append("OPERATION PROGRESS:")
        context_parts.append(f"  - Schema Analysis: {'✅ Complete' if state['schema_analysis_complete'] else '❌ Pending'}")
        context_parts.append(f"  - Data Profiling: {'✅ Complete' if state['data_profiling_complete'] else '❌ Pending'}")
        context_parts.append(f"  - Query Generation: {'✅ Complete' if state['query_generation_complete'] else '❌ Pending'}")
        context_parts.append(f"  - Optimization Analysis: {'✅ Complete' if state['optimization_analysis_complete'] else '❌ Pending'}")

        # Results accumulation
        if state['relevant_tables']:
            context_parts.append(f"RELEVANT TABLES IDENTIFIED: {len(state['relevant_tables'])} tables")
        if state['generated_queries']:
            context_parts.append(f"QUERIES GENERATED: {len(state['generated_queries'])} queries")
        if state['optimization_suggestions']:
            context_parts.append(f"OPTIMIZATION SUGGESTIONS: {len(state['optimization_suggestions'])} suggestions")
        if state['analysis_insights']:
            context_parts.append(f"ANALYSIS INSIGHTS: {len(state['analysis_insights'])} insights")

        # Recent tool calls
        tool_calls = state.get('tool_calls', [])
        if tool_calls:
            context_parts.append("RECENT TOOL CALLS:")
            for call in tool_calls[-3:]:  # Last 3 calls
                outcome_summary = call.get('outcome', {}).get('summary', 'No outcome recorded')
                context_parts.append(f"  - Call {call['call_number']}: {call['tool']} → {outcome_summary}")

            # Check for repeated tool calls (indication of loop)
            recent_tools = [call['tool'] for call in tool_calls[-3:]]
            if len(recent_tools) >= 2 and len(set(recent_tools)) == 1:
                context_parts.append(f"⚠️ WARNING: Repeated tool calls detected ({recent_tools[0]} called {len(recent_tools)} times in a row)")

        return "\n".join(context_parts)

    def _fallback_tool_decision(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback decision logic when LLM fails."""

        # Check for completion first
        if self._check_operation_completion(state):
            return {
                "tool": "complete",
                "reasoning": "All database operations completed successfully",
                "parameters": {},
                "complete": True,
                "confidence": 0.9
            }

        # Simple heuristic-based decision making
        user_query = state.get('user_query', '').lower()

        # Schema analysis operations
        if any(keyword in user_query for keyword in ['schema', 'structure', 'tables', 'columns', 'analyze']):
            if not state.get('schema_analysis_complete'):
                return {
                    "tool": "analyze_schema",
                    "reasoning": "User requested schema analysis and it hasn't been completed",
                    "parameters": {},
                    "complete": False,
                    "confidence": 0.8
                }

        # Query generation operations
        if any(keyword in user_query for keyword in ['query', 'select', 'find', 'get', 'retrieve']):
            if not state.get('query_generation_complete'):
                return {
                    "tool": "generate_optimized_query",
                    "reasoning": "User requested query generation and it hasn't been completed",
                    "parameters": {"query_request": state['user_query']},
                    "complete": False,
                    "confidence": 0.8
                }

        # Data profiling operations
        if any(keyword in user_query for keyword in ['profile', 'quality', 'statistics', 'data']):
            if not state.get('data_profiling_complete'):
                return {
                    "tool": "profile_data",
                    "reasoning": "User requested data profiling and it hasn't been completed",
                    "parameters": {},
                    "complete": False,
                    "confidence": 0.8
                }

        # Default to schema analysis if nothing else matches
        if not state.get('schema_analysis_complete'):
            return {
                "tool": "analyze_schema",
                "reasoning": "Starting with schema analysis as foundation for database operations",
                "parameters": {},
                "complete": False,
                "confidence": 0.7
            }

        # If all basic operations are done, suggest completion
        return {
            "tool": "complete",
            "reasoning": "Basic database operations completed, no further action needed",
            "parameters": {},
            "complete": True,
            "confidence": 0.6
        }

    async def _execute_database_tool(self, tool_name: str, parameters: Dict[str, Any],
                                   state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Execute the specified database tool with given parameters."""

        try:
            if tool_name == "analyze_schema":
                async for event in self._tool_analyze_schema(parameters, state):
                    yield event

            elif tool_name == "profile_data":
                async for event in self._tool_profile_data(parameters, state):
                    yield event

            elif tool_name == "generate_optimized_query":
                async for event in self._tool_generate_optimized_query(parameters, state):
                    yield event

            elif tool_name == "validate_query":
                async for event in self._tool_validate_query(parameters, state):
                    yield event

            elif tool_name == "discover_relationships":
                async for event in self._tool_discover_relationships(parameters, state):
                    yield event

            elif tool_name == "suggest_optimizations":
                async for event in self._tool_suggest_optimizations(parameters, state):
                    yield event

            elif tool_name == "execute_analysis":
                async for event in self._tool_execute_analysis(parameters, state):
                    yield event

            elif tool_name == "explain_operations":
                async for event in self._tool_explain_operations(parameters, state):
                    yield event

            elif tool_name == "complete":
                state["task_complete"] = True
                yield self._create_event("agent_result", "database_agent", {
                    "message": "🎉 Database operation completed successfully!",
                    "status": "completed"
                })
            else:
                logger.error(f"❌ Unknown database tool: {tool_name}")
                yield self._create_event("error", "database_agent", {
                    "message": f"Unknown database tool: {tool_name}",
                    "status": "error"
                })

        except Exception as e:
            logger.error(f"❌ Error executing database tool {tool_name}: {e}")
            yield self._create_event("error", "database_agent", {
                "message": f"Error executing {tool_name}: {str(e)}",
                "status": "error"
            })

    def _create_event(self, event_type: str, agent_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a standardized event for streaming responses."""
        return {
            "type": event_type,
            "agent": agent_name,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }

    def _clean_llm_response(self, response: str) -> str:
        """Clean LLM response to extract JSON."""
        response = response.strip()
        if response.startswith("```json"):
            response = response[7:]
        if response.startswith("```"):
            response = response[3:]
        if response.endswith("```"):
            response = response[:-3]
        return response.strip()

    async def _capture_tool_outcome(self, tool_name: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """Capture the outcome of a database tool call for context building."""
        outcome = {
            "success": False,
            "summary": "Database tool execution attempted",
            "details": {}
        }

        try:
            if tool_name == "analyze_schema":
                outcome["success"] = state.get("schema_analysis_complete", False)
                outcome["summary"] = "Schema analysis completed" if outcome["success"] else "Schema analysis failed"

            elif tool_name == "profile_data":
                outcome["success"] = state.get("data_profiling_complete", False)
                outcome["summary"] = "Data profiling completed" if outcome["success"] else "Data profiling failed"

            elif tool_name == "generate_optimized_query":
                queries = state.get("generated_queries", [])
                outcome["success"] = len(queries) > 0
                outcome["summary"] = f"Generated {len(queries)} queries" if outcome["success"] else "Query generation failed"

            elif tool_name == "validate_query":
                outcome["success"] = True  # Validation always provides results
                outcome["summary"] = "Query validation completed"

            elif tool_name == "discover_relationships":
                relationships = state.get("relationship_map", {})
                outcome["success"] = len(relationships) > 0
                outcome["summary"] = f"Discovered {len(relationships)} relationships" if outcome["success"] else "No relationships found"

            elif tool_name == "suggest_optimizations":
                suggestions = state.get("optimization_suggestions", [])
                outcome["success"] = len(suggestions) > 0
                outcome["summary"] = f"Generated {len(suggestions)} optimization suggestions" if outcome["success"] else "No optimizations suggested"

            elif tool_name == "execute_analysis":
                outcome["success"] = True  # Execution always provides results (success or error)
                outcome["summary"] = "Analysis execution completed"

            elif tool_name == "explain_operations":
                outcome["success"] = True
                outcome["summary"] = "Operations explanation completed"

        except Exception as e:
            logger.warning(f"Error capturing outcome for {tool_name}: {e}")
            outcome["summary"] = f"Error capturing outcome: {str(e)}"

        return outcome

    def _check_operation_completion(self, state: Dict[str, Any]) -> bool:
        """Check if the database operation is complete."""

        # Check for infinite loops
        tool_calls = state.get('tool_calls', [])
        if len(tool_calls) >= 3:
            recent_tools = [call['tool'] for call in tool_calls[-3:]]
            if len(set(recent_tools)) == 1:  # Same tool called 3 times in a row
                logger.warning(f"Infinite loop detected: {recent_tools[0]} called repeatedly")
                return True

        # Check based on operation mode and user query
        operation_mode = state.get('operation_mode', 'comprehensive')
        user_query = state.get('user_query', '').lower()

        if operation_mode == 'focused':
            # For focused operations, complete when primary goal is achieved
            if 'schema' in user_query and state.get('schema_analysis_complete'):
                return True
            if 'query' in user_query and state.get('query_generation_complete'):
                return True
            if 'profile' in user_query and state.get('data_profiling_complete'):
                return True

        elif operation_mode == 'comprehensive':
            # For comprehensive operations, complete when multiple goals are achieved
            completed_operations = sum([
                state.get('schema_analysis_complete', False),
                state.get('data_profiling_complete', False),
                state.get('query_generation_complete', False),
                state.get('optimization_analysis_complete', False)
            ])
            return completed_operations >= 2  # At least 2 major operations completed

        return False

    def _create_comprehensive_response(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Create a comprehensive response with all database operation results."""
        response = {
            "database_info": state["database_info"],
            "operation_summary": {
                "total_tool_calls": state["tool_call_count"],
                "operations_completed": {
                    "schema_analysis": state.get("schema_analysis_complete", False),
                    "data_profiling": state.get("data_profiling_complete", False),
                    "query_generation": state.get("query_generation_complete", False),
                    "optimization_analysis": state.get("optimization_analysis_complete", False)
                }
            },
            "results": {
                "relevant_tables": state.get("relevant_tables", []),
                "generated_queries": state.get("generated_queries", []),
                "optimization_suggestions": state.get("optimization_suggestions", []),
                "analysis_insights": state.get("analysis_insights", [])
            },
            "metadata": {
                "agent_id": self.agent_id,
                "parent_agent_id": self.parent_agent_id,
                "operation_mode": state.get("operation_mode"),
                "timestamp": datetime.now().isoformat()
            }
        }

        return response

    # =========================================================================
    # DATABASE TOOL IMPLEMENTATIONS
    # =========================================================================

    async def _tool_analyze_schema(self, parameters: Dict[str, Any],
                                 state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Analyze database schema structure and relationships."""

        yield self._create_event("tool_status", "analyze_schema", {
            "message": "🔍 Analyzing database schema structure...",
            "status": "analyzing"
        })

        try:
            # Perform deep schema analysis
            schema_insights = {
                "table_count": len(self.tables),
                "total_columns": sum(len(table.columns) for table in self.tables),
                "tables_with_primary_keys": len([t for t in self.tables if t.primary_keys]),
                "tables_with_foreign_keys": len([t for t in self.tables if t.foreign_keys]),
                "relationship_count": sum(len(table.foreign_keys) for table in self.tables)
            }

            # Analyze table relationships
            relationship_analysis = await self._analyze_table_relationships()
            schema_insights["relationship_analysis"] = relationship_analysis

            # Identify key tables (tables with many relationships)
            key_tables = self._identify_key_tables()
            schema_insights["key_tables"] = key_tables

            # Store results
            self.schema_analysis = schema_insights
            state["schema_analysis_complete"] = True
            state["analysis_insights"].append({
                "type": "schema_analysis",
                "insights": schema_insights,
                "timestamp": datetime.now().isoformat()
            })

            yield self._create_event("tool_result", "analyze_schema", {
                "message": f"✅ Schema analysis completed - {schema_insights['table_count']} tables, {schema_insights['total_columns']} columns",
                "status": "completed",
                "insights": schema_insights
            })

        except Exception as e:
            logger.error(f"❌ Schema analysis failed: {e}")
            yield self._create_event("error", "analyze_schema", {
                "message": f"Failed to analyze schema: {str(e)}",
                "status": "error"
            })

    async def _tool_profile_data(self, parameters: Dict[str, Any],
                               state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Profile data quality and statistics."""

        yield self._create_event("tool_status", "profile_data", {
            "message": "📊 Profiling data quality and statistics...",
            "status": "profiling"
        })

        try:
            # Select tables to profile (limit for performance)
            tables_to_profile = self.tables[:5] if len(self.tables) > 5 else self.tables

            profiling_results = {}
            for table in tables_to_profile:
                try:
                    # Generate basic profiling query
                    profile_query = self._generate_profiling_query(table)

                    # Execute profiling query
                    result = await self.database_service.execute_query(
                        self.database.id, profile_query
                    )

                    # Analyze results
                    table_profile = self._analyze_profiling_results(table, result)
                    profiling_results[table.name] = table_profile

                except Exception as e:
                    logger.warning(f"Failed to profile table {table.name}: {e}")
                    profiling_results[table.name] = {"error": str(e)}

            # Store results
            self.data_profiles = profiling_results
            state["data_profiling_complete"] = True
            state["analysis_insights"].append({
                "type": "data_profiling",
                "profiles": profiling_results,
                "timestamp": datetime.now().isoformat()
            })

            yield self._create_event("tool_result", "profile_data", {
                "message": f"✅ Data profiling completed for {len(profiling_results)} tables",
                "status": "completed",
                "profiles": profiling_results
            })

        except Exception as e:
            logger.error(f"❌ Data profiling failed: {e}")
            yield self._create_event("error", "profile_data", {
                "message": f"Failed to profile data: {str(e)}",
                "status": "error"
            })

    async def _tool_generate_optimized_query(self, parameters: Dict[str, Any],
                                           state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Generate optimized SQL queries."""

        query_request = parameters.get("query_request", state.get("user_query", ""))

        yield self._create_event("tool_status", "generate_optimized_query", {
            "message": f"💻 Generating optimized SQL query for: {query_request[:100]}...",
            "status": "generating"
        })

        try:
            # Find relevant tables for the query
            relevant_tables = await self._find_relevant_tables(query_request,
                                                             state.get("target_tables", []),
                                                             state.get("target_columns", {}))

            if not relevant_tables:
                yield self._create_event("error", "generate_optimized_query", {
                    "message": "No relevant tables found for query generation",
                    "status": "error"
                })
                return

            # Generate optimized SQL using AI
            optimized_query = await self._generate_optimized_sql(query_request, relevant_tables)

            # Validate and optimize the query
            query_analysis = await self._analyze_query_performance(optimized_query, relevant_tables)

            query_result = {
                "original_request": query_request,
                "generated_sql": optimized_query,
                "relevant_tables": [t.name for t in relevant_tables],
                "performance_analysis": query_analysis,
                "optimization_applied": True
            }

            # Store results
            state["generated_queries"].append(query_result)
            state["query_generation_complete"] = True
            state["relevant_tables"] = [self._table_to_dict(t) for t in relevant_tables]

            yield self._create_event("tool_result", "generate_optimized_query", {
                "message": "✅ Optimized SQL query generated successfully",
                "status": "completed",
                "query": optimized_query,
                "tables_used": len(relevant_tables),
                "performance_score": query_analysis.get("performance_score", "N/A")
            })

        except Exception as e:
            logger.error(f"❌ Query generation failed: {e}")
            yield self._create_event("error", "generate_optimized_query", {
                "message": f"Failed to generate query: {str(e)}",
                "status": "error"
            })

    async def _tool_validate_query(self, parameters: Dict[str, Any],
                                 state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Validate SQL queries for safety and performance."""

        query_to_validate = parameters.get("query", "")
        if not query_to_validate and state.get("generated_queries"):
            query_to_validate = state["generated_queries"][-1]["generated_sql"]

        yield self._create_event("tool_status", "validate_query", {
            "message": "🔍 Validating SQL query for safety and performance...",
            "status": "validating"
        })

        try:
            validation_results = {
                "query": query_to_validate,
                "is_safe": True,
                "safety_issues": [],
                "performance_warnings": [],
                "recommendations": []
            }

            # Basic safety checks
            safety_issues = self._check_query_safety(query_to_validate)
            validation_results["safety_issues"] = safety_issues
            validation_results["is_safe"] = len(safety_issues) == 0

            # Performance analysis
            performance_warnings = self._analyze_query_performance_issues(query_to_validate)
            validation_results["performance_warnings"] = performance_warnings

            # Generate recommendations
            recommendations = self._generate_query_recommendations(query_to_validate, safety_issues, performance_warnings)
            validation_results["recommendations"] = recommendations

            yield self._create_event("tool_result", "validate_query", {
                "message": f"✅ Query validation completed - {'Safe' if validation_results['is_safe'] else 'Has Issues'}",
                "status": "completed",
                "validation": validation_results
            })

        except Exception as e:
            logger.error(f"❌ Query validation failed: {e}")
            yield self._create_event("error", "validate_query", {
                "message": f"Failed to validate query: {str(e)}",
                "status": "error"
            })

    async def _tool_discover_relationships(self, parameters: Dict[str, Any],
                                         state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Discover relationships and constraints in the database."""

        yield self._create_event("tool_status", "discover_relationships", {
            "message": "🔗 Discovering database relationships and constraints...",
            "status": "discovering"
        })

        try:
            relationship_map = await self._discover_database_relationships()

            # Store results
            self.relationship_map = relationship_map
            state["analysis_insights"].append({
                "type": "relationship_discovery",
                "relationships": relationship_map,
                "timestamp": datetime.now().isoformat()
            })

            relationship_count = sum(len(rels) for rels in relationship_map.values())

            yield self._create_event("tool_result", "discover_relationships", {
                "message": f"✅ Discovered {relationship_count} relationships across {len(relationship_map)} tables",
                "status": "completed",
                "relationships": relationship_map
            })

        except Exception as e:
            logger.error(f"❌ Relationship discovery failed: {e}")
            yield self._create_event("error", "discover_relationships", {
                "message": f"Failed to discover relationships: {str(e)}",
                "status": "error"
            })

    async def _tool_suggest_optimizations(self, parameters: Dict[str, Any],
                                        state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Suggest database and query optimizations."""

        yield self._create_event("tool_status", "suggest_optimizations", {
            "message": "⚡ Analyzing database for optimization opportunities...",
            "status": "analyzing"
        })

        try:
            optimization_suggestions = []

            # Schema optimization suggestions
            schema_optimizations = self._analyze_schema_optimizations()
            optimization_suggestions.extend(schema_optimizations)

            # Query optimization suggestions
            if state.get("generated_queries"):
                query_optimizations = self._analyze_query_optimizations(state["generated_queries"])
                optimization_suggestions.extend(query_optimizations)

            # Index suggestions
            index_suggestions = self._suggest_indexes()
            optimization_suggestions.extend(index_suggestions)

            # Store results
            state["optimization_suggestions"] = optimization_suggestions
            state["optimization_analysis_complete"] = True

            yield self._create_event("tool_result", "suggest_optimizations", {
                "message": f"✅ Generated {len(optimization_suggestions)} optimization suggestions",
                "status": "completed",
                "suggestions": optimization_suggestions
            })

        except Exception as e:
            logger.error(f"❌ Optimization analysis failed: {e}")
            yield self._create_event("error", "suggest_optimizations", {
                "message": f"Failed to suggest optimizations: {str(e)}",
                "status": "error"
            })

    async def _tool_execute_analysis(self, parameters: Dict[str, Any],
                                   state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Execute database analysis queries safely."""

        query_to_execute = parameters.get("query", "")
        if not query_to_execute and state.get("generated_queries"):
            query_to_execute = state["generated_queries"][-1]["generated_sql"]

        yield self._create_event("tool_status", "execute_analysis", {
            "message": "🔄 Executing database analysis query...",
            "status": "executing"
        })

        try:
            # Safety check before execution
            if not self._is_query_safe_for_execution(query_to_execute):
                yield self._create_event("error", "execute_analysis", {
                    "message": "Query failed safety validation - execution blocked",
                    "status": "error"
                })
                return

            # Execute the query
            result = await self.database_service.execute_query(
                self.database.id, query_to_execute
            )

            # Analyze results
            result_analysis = self._analyze_execution_results(result)

            execution_result = {
                "query": query_to_execute,
                "execution_successful": True,
                "result_summary": result_analysis,
                "timestamp": datetime.now().isoformat()
            }

            yield self._create_event("tool_result", "execute_analysis", {
                "message": f"✅ Query executed successfully - {result_analysis.get('row_count', 0)} rows returned",
                "status": "completed",
                "execution": execution_result
            })

        except Exception as e:
            logger.error(f"❌ Query execution failed: {e}")
            yield self._create_event("error", "execute_analysis", {
                "message": f"Failed to execute query: {str(e)}",
                "status": "error"
            })

    async def _tool_explain_operations(self, parameters: Dict[str, Any],
                                     state: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """Tool: Explain database operations and results."""

        yield self._create_event("tool_status", "explain_operations", {
            "message": "📖 Generating detailed explanation of database operations...",
            "status": "explaining"
        })

        try:
            explanation = self._generate_comprehensive_explanation(state)

            yield self._create_event("tool_result", "explain_operations", {
                "message": "✅ Comprehensive explanation generated",
                "status": "completed",
                "explanation": explanation
            })

        except Exception as e:
            logger.error(f"❌ Operation explanation failed: {e}")
            yield self._create_event("error", "explain_operations", {
                "message": f"Failed to explain operations: {str(e)}",
                "status": "error"
            })

    # =========================================================================
    # HELPER METHODS FOR DATABASE TOOLS
    # =========================================================================

    async def _analyze_table_relationships(self) -> Dict[str, Any]:
        """Analyze relationships between tables."""
        relationships = {
            "foreign_key_relationships": [],
            "potential_relationships": [],
            "relationship_strength": {}
        }

        # Analyze explicit foreign key relationships
        for table in self.tables:
            for fk in table.foreign_keys:
                relationships["foreign_key_relationships"].append({
                    "from_table": table.name,
                    "from_column": fk.column_name,
                    "to_table": fk.referenced_table_name,
                    "to_column": fk.referenced_column_name,
                    "relationship_type": "foreign_key"
                })

        # Analyze potential relationships based on column names and types
        potential_rels = self._find_potential_relationships()
        relationships["potential_relationships"] = potential_rels

        return relationships

    def _identify_key_tables(self) -> List[Dict[str, Any]]:
        """Identify key tables based on relationships and structure."""
        key_tables = []

        for table in self.tables:
            # Calculate importance score
            importance_score = 0

            # Tables with many foreign keys pointing to them are important
            referenced_count = sum(1 for t in self.tables for fk in t.foreign_keys
                                 if fk.referenced_table_name == table.name)
            importance_score += referenced_count * 2

            # Tables with many columns might be central
            importance_score += len(table.columns) * 0.1

            # Tables with primary keys are more structured
            if table.primary_keys:
                importance_score += 1

            key_tables.append({
                "table_name": table.name,
                "importance_score": importance_score,
                "referenced_by_count": referenced_count,
                "column_count": len(table.columns)
            })

        # Sort by importance and return top tables
        key_tables.sort(key=lambda x: x["importance_score"], reverse=True)
        return key_tables[:5]  # Top 5 key tables

    def _generate_profiling_query(self, table: DatabaseTable) -> str:
        """Generate a data profiling query for a table."""
        # Basic profiling query that works across different database types
        if self.database.db_type == DatabaseType.POSTGRESQL:
            return f"SELECT COUNT(*) as row_count FROM {table.name} LIMIT 1000;"
        elif self.database.db_type == DatabaseType.MYSQL:
            return f"SELECT COUNT(*) as row_count FROM {table.name} LIMIT 1000;"
        else:
            # Generic SQL
            return f"SELECT COUNT(*) as row_count FROM {table.name};"

    def _analyze_profiling_results(self, table: DatabaseTable, result: Any) -> Dict[str, Any]:
        """Analyze profiling query results."""
        try:
            if hasattr(result, 'iloc') and len(result) > 0:
                # Pandas DataFrame
                row_count = result.iloc[0, 0] if len(result.columns) > 0 else 0
            else:
                # List of dicts or other format
                row_count = result[0].get('row_count', 0) if result else 0

            return {
                "table_name": table.name,
                "row_count": row_count,
                "column_count": len(table.columns),
                "has_primary_key": len(table.primary_keys) > 0,
                "has_foreign_keys": len(table.foreign_keys) > 0,
                "data_quality_score": self._calculate_data_quality_score(table, row_count)
            }
        except Exception as e:
            return {"error": f"Failed to analyze profiling results: {str(e)}"}

    def _calculate_data_quality_score(self, table: DatabaseTable, row_count: int) -> float:
        """Calculate a simple data quality score."""
        score = 0.0

        # Points for having data
        if row_count > 0:
            score += 0.3

        # Points for having primary key
        if table.primary_keys:
            score += 0.2

        # Points for having relationships
        if table.foreign_keys:
            score += 0.2

        # Points for having descriptions
        if table.description:
            score += 0.1

        # Points for column descriptions
        described_columns = sum(1 for col in table.columns if col.description)
        if described_columns > 0:
            score += 0.2 * (described_columns / len(table.columns))

        return min(score, 1.0)

    async def _generate_optimized_sql(self, query_request: str, relevant_tables: List[DatabaseTable]) -> str:
        """Generate optimized SQL using AI."""

        # Create schema description for the relevant tables
        schema_description = self._create_focused_schema_description(relevant_tables)

        system_prompt = f"""You are an expert SQL developer specializing in {self.database.db_type.value} databases.
Generate optimized, efficient SQL queries that follow best practices for performance and readability.

OPTIMIZATION PRINCIPLES:
- Use appropriate indexes and joins
- Minimize data transfer with specific column selection
- Use efficient WHERE clauses
- Apply proper aggregation techniques
- Follow {self.database.db_type.value} specific optimizations

Return ONLY the SQL query, no explanations."""

        user_prompt = f"""Database Schema:
{schema_description}

User Request: {query_request}

Generate an optimized {self.database.db_type.value} SQL query that efficiently answers this request."""

        try:
            response = await self.bedrock_client.generate_response(
                prompt=user_prompt,
                system_prompt=system_prompt,
                temperature=0.2
            )

            # Clean the response
            sql = response.strip()
            if sql.startswith("```sql"):
                sql = sql[6:]
            elif sql.startswith("```"):
                sql = sql[3:]
            if sql.endswith("```"):
                sql = sql[:-3]

            return sql.strip()

        except Exception as e:
            logger.error(f"Failed to generate optimized SQL: {e}")
            # Fallback to basic query generation
            return self._generate_basic_sql_fallback(query_request, relevant_tables)

    def _create_focused_schema_description(self, tables: List[DatabaseTable]) -> str:
        """Create a focused schema description for specific tables."""
        description = []

        for table in tables:
            table_desc = f"Table: {table.name}"
            if hasattr(table, 'db_schema') and table.db_schema:
                table_desc += f" (Schema: {table.db_schema})"
            if table.description:
                table_desc += f" - {table.description}"
            description.append(table_desc)

            # Add column information
            for column in table.columns:
                col_desc = f"  - {column.name} ({column.data_type})"

                attributes = []
                if column.is_primary_key:
                    attributes.append("Primary Key")
                if column.is_foreign_key:
                    attributes.append("Foreign Key")
                if column.is_unique:
                    attributes.append("Unique")
                if not column.is_nullable:
                    attributes.append("Not Null")

                if attributes:
                    col_desc += f" [{', '.join(attributes)}]"

                if column.description:
                    col_desc += f" - {column.description}"

                description.append(col_desc)

            description.append("")  # Empty line between tables

        return "\n".join(description)

    def _generate_basic_sql_fallback(self, query_request: str, relevant_tables: List[DatabaseTable]) -> str:
        """Generate a basic SQL query as fallback."""
        if not relevant_tables:
            return "SELECT 1;"

        # Simple SELECT from the first relevant table
        table = relevant_tables[0]
        columns = [col.name for col in table.columns[:5]]  # First 5 columns

        return f"SELECT {', '.join(columns)} FROM {table.name} LIMIT 100;"

    async def _analyze_query_performance(self, query: str, tables: List[DatabaseTable]) -> Dict[str, Any]:
        """Analyze query performance characteristics."""
        analysis = {
            "performance_score": 0.8,  # Default good score
            "complexity": "medium",
            "estimated_cost": "moderate",
            "recommendations": []
        }

        # Simple heuristic analysis
        query_lower = query.lower()

        # Check for potential performance issues
        if "select *" in query_lower:
            analysis["recommendations"].append("Consider selecting specific columns instead of *")
            analysis["performance_score"] -= 0.1

        if "order by" in query_lower and "limit" not in query_lower:
            analysis["recommendations"].append("Consider adding LIMIT clause with ORDER BY")
            analysis["performance_score"] -= 0.1

        # Check for joins
        join_count = query_lower.count("join")
        if join_count > 3:
            analysis["complexity"] = "high"
            analysis["recommendations"].append("Complex query with multiple joins - ensure proper indexing")

        return analysis

    def _check_query_safety(self, query: str) -> List[str]:
        """Check query for safety issues."""
        issues = []
        query_lower = query.lower().strip()

        # Check for dangerous operations
        dangerous_keywords = ['drop', 'delete', 'truncate', 'alter', 'create', 'insert', 'update']
        for keyword in dangerous_keywords:
            if keyword in query_lower:
                issues.append(f"Contains potentially dangerous keyword: {keyword.upper()}")

        # Check for SQL injection patterns
        if "'" in query and ("or" in query_lower or "union" in query_lower):
            issues.append("Potential SQL injection pattern detected")

        return issues

    def _analyze_query_performance_issues(self, query: str) -> List[str]:
        """Analyze query for performance issues."""
        warnings = []
        query_lower = query.lower()

        if "select *" in query_lower:
            warnings.append("Using SELECT * may impact performance")

        if "like '%%" in query_lower:
            warnings.append("Leading wildcard in LIKE may cause full table scan")

        if query_lower.count("join") > 2:
            warnings.append("Multiple joins may impact performance")

        return warnings

    def _generate_query_recommendations(self, query: str, safety_issues: List[str],
                                      performance_warnings: List[str]) -> List[str]:
        """Generate recommendations for query improvement."""
        recommendations = []

        if safety_issues:
            recommendations.append("Review and address safety issues before execution")

        if performance_warnings:
            recommendations.append("Consider performance optimizations")

        if "order by" in query.lower() and "limit" not in query.lower():
            recommendations.append("Add LIMIT clause to prevent large result sets")

        return recommendations

    def _is_query_safe_for_execution(self, query: str) -> bool:
        """Check if query is safe for execution."""
        safety_issues = self._check_query_safety(query)
        return len(safety_issues) == 0

    async def _discover_database_relationships(self) -> Dict[str, Any]:
        """Discover relationships in the database."""
        relationships = {}

        for table in self.tables:
            table_relationships = []

            # Add explicit foreign key relationships
            for fk in table.foreign_keys:
                table_relationships.append({
                    "type": "foreign_key",
                    "target_table": fk.referenced_table_name,
                    "target_column": fk.referenced_column_name,
                    "source_column": fk.column_name
                })

            # Add potential relationships based on naming patterns
            potential_rels = self._find_potential_relationships_for_table(table)
            table_relationships.extend(potential_rels)

            if table_relationships:
                relationships[table.name] = table_relationships

        return relationships

    def _find_potential_relationships_for_table(self, table: DatabaseTable) -> List[Dict[str, Any]]:
        """Find potential relationships for a specific table."""
        potential_relationships = []

        for column in table.columns:
            # Look for columns that might be foreign keys based on naming
            if column.name.endswith('_id') or column.name.endswith('Id'):
                # Try to find matching table
                potential_table_name = column.name[:-3]  # Remove '_id'

                # Check if there's a table with similar name
                for other_table in self.tables:
                    if (other_table.name.lower() == potential_table_name.lower() or
                        other_table.name.lower().startswith(potential_table_name.lower())):
                        potential_relationships.append({
                            "type": "potential_foreign_key",
                            "target_table": other_table.name,
                            "source_column": column.name,
                            "confidence": 0.7
                        })

        return potential_relationships

    def _find_potential_relationships(self) -> List[Dict[str, Any]]:
        """Find potential relationships across all tables."""
        potential_relationships = []

        for table in self.tables:
            table_rels = self._find_potential_relationships_for_table(table)
            for rel in table_rels:
                rel["source_table"] = table.name
                potential_relationships.append(rel)

        return potential_relationships

    def _analyze_schema_optimizations(self) -> List[Dict[str, Any]]:
        """Analyze schema for optimization opportunities."""
        optimizations = []

        for table in self.tables:
            # Check for missing primary keys
            if not table.primary_keys:
                optimizations.append({
                    "type": "schema_optimization",
                    "category": "primary_key",
                    "table": table.name,
                    "suggestion": f"Consider adding a primary key to table {table.name}",
                    "priority": "high"
                })

            # Check for tables with many columns (potential normalization opportunity)
            if len(table.columns) > 15:
                optimizations.append({
                    "type": "schema_optimization",
                    "category": "normalization",
                    "table": table.name,
                    "suggestion": f"Table {table.name} has {len(table.columns)} columns - consider normalization",
                    "priority": "medium"
                })

            # Check for missing descriptions
            if not table.description:
                optimizations.append({
                    "type": "schema_optimization",
                    "category": "documentation",
                    "table": table.name,
                    "suggestion": f"Add description to table {table.name} for better documentation",
                    "priority": "low"
                })

        return optimizations

    def _analyze_query_optimizations(self, generated_queries: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Analyze generated queries for optimization opportunities."""
        optimizations = []

        for query_info in generated_queries:
            query = query_info.get("generated_sql", "")
            query_lower = query.lower()

            # Check for SELECT * usage
            if "select *" in query_lower:
                optimizations.append({
                    "type": "query_optimization",
                    "category": "column_selection",
                    "suggestion": "Replace SELECT * with specific column names for better performance",
                    "priority": "medium",
                    "query": query[:100] + "..." if len(query) > 100 else query
                })

            # Check for missing WHERE clauses
            if "where" not in query_lower and "limit" not in query_lower:
                optimizations.append({
                    "type": "query_optimization",
                    "category": "filtering",
                    "suggestion": "Consider adding WHERE clause or LIMIT to reduce result set size",
                    "priority": "medium",
                    "query": query[:100] + "..." if len(query) > 100 else query
                })

        return optimizations

    def _suggest_indexes(self) -> List[Dict[str, Any]]:
        """Suggest indexes for performance improvement."""
        index_suggestions = []

        for table in self.tables:
            # Suggest indexes for foreign key columns
            for fk in table.foreign_keys:
                index_suggestions.append({
                    "type": "index_suggestion",
                    "category": "foreign_key_index",
                    "table": table.name,
                    "column": fk.column_name,
                    "suggestion": f"Consider adding index on {table.name}.{fk.column_name} for foreign key performance",
                    "priority": "high"
                })

            # Suggest indexes for columns that might be frequently queried
            for column in table.columns:
                if (column.name.lower() in ['name', 'email', 'username', 'status', 'type'] and
                    not column.is_primary_key):
                    index_suggestions.append({
                        "type": "index_suggestion",
                        "category": "query_performance",
                        "table": table.name,
                        "column": column.name,
                        "suggestion": f"Consider adding index on {table.name}.{column.name} for query performance",
                        "priority": "medium"
                    })

        return index_suggestions

    def _analyze_execution_results(self, result: Any) -> Dict[str, Any]:
        """Analyze query execution results."""
        analysis = {
            "row_count": 0,
            "column_count": 0,
            "data_types": [],
            "has_data": False
        }

        try:
            if hasattr(result, 'shape'):
                # Pandas DataFrame
                analysis["row_count"] = result.shape[0]
                analysis["column_count"] = result.shape[1]
                analysis["has_data"] = result.shape[0] > 0
                analysis["data_types"] = result.dtypes.to_dict() if hasattr(result, 'dtypes') else []
            elif isinstance(result, list):
                # List of dictionaries
                analysis["row_count"] = len(result)
                if result:
                    analysis["column_count"] = len(result[0]) if isinstance(result[0], dict) else 0
                    analysis["has_data"] = True

        except Exception as e:
            analysis["error"] = f"Failed to analyze results: {str(e)}"

        return analysis

    def _generate_comprehensive_explanation(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate comprehensive explanation of database operations."""
        explanation = {
            "operation_summary": f"Performed {state['tool_call_count']} database operations",
            "database_overview": {
                "name": self.database.name,
                "type": self.database.db_type.value,
                "table_count": len(self.tables),
                "total_columns": sum(len(table.columns) for table in self.tables)
            },
            "operations_performed": [],
            "key_findings": [],
            "recommendations": []
        }

        # Summarize operations performed
        for tool_call in state.get("tool_calls", []):
            explanation["operations_performed"].append({
                "operation": tool_call["tool"],
                "reasoning": tool_call["reasoning"],
                "outcome": tool_call["outcome"]["summary"]
            })

        # Add key findings from analysis
        if state.get("analysis_insights"):
            for insight in state["analysis_insights"]:
                explanation["key_findings"].append({
                    "type": insight["type"],
                    "summary": f"Completed {insight['type']} analysis",
                    "timestamp": insight["timestamp"]
                })

        # Add recommendations
        if state.get("optimization_suggestions"):
            explanation["recommendations"] = state["optimization_suggestions"][:5]  # Top 5 recommendations

        return explanation

    def _table_to_dict(self, table: DatabaseTable) -> Dict[str, Any]:
        """Convert a DatabaseTable to a dictionary.
        
        Args:
            table: The table to convert
            
        Returns:
            Dictionary representation of the table
        """
        try:
            # Try to use Pydantic's model_dump, which replaced dict() in v2
            if hasattr(table, 'model_dump'):
                return table.model_dump()
        except Exception as e:
            logger.warning(f"Error using model_dump: {str(e)}")
        
        # Fallback to manual conversion if model_dump doesn't work
        try:
            result = {
                "name": table.name,
                "schema": table.schema,
                "description": table.description,
                "columns": [self._column_to_dict(c) for c in table.columns],
                "primary_keys": table.primary_keys,
                "foreign_keys": [self._fk_to_dict(fk) for fk in table.foreign_keys],
                "row_count": table.row_count,
                "metadata": table.metadata
            }
            return result
        except Exception as e:
            logger.error(f"Error in _table_to_dict: {str(e)}")
            # Create a minimal table dict with just the essential info
            return {
                "name": getattr(table, 'name', 'unknown'),
                "schema": getattr(table, 'schema', None),
                "columns": [],
                "primary_keys": [],
                "foreign_keys": []
            }
    
    def _column_to_dict(self, column: DatabaseColumn) -> Dict[str, Any]:
        """Convert a DatabaseColumn to a dictionary.
        
        Args:
            column: The column to convert
            
        Returns:
            Dictionary representation of the column
        """
        try:
            if hasattr(column, 'model_dump'):
                return column.model_dump()
        except Exception:
            pass
        
        try:
            return {
                "name": column.name,
                "data_type": column.data_type,
                "description": column.description,
                "is_nullable": column.is_nullable,
                "is_primary_key": column.is_primary_key,
                "is_foreign_key": column.is_foreign_key,
                "is_unique": column.is_unique,
                "default_value": column.default_value,
                "metadata": column.metadata
            }
        except Exception as e:
            logger.warning(f"Error in _column_to_dict: {str(e)}")
            # Return minimal column info
            return {
                "name": getattr(column, 'name', 'unknown'),
                "data_type": getattr(column, 'data_type', 'unknown'),
                "is_nullable": True,
                "is_primary_key": False
            }
    
    def _fk_to_dict(self, fk) -> Dict[str, Any]:
        """Convert a ForeignKeyRelationship to a dictionary.
        
        Args:
            fk: The foreign key relationship to convert
            
        Returns:
            Dictionary representation of the foreign key
        """
        try:
            if hasattr(fk, 'model_dump'):
                return fk.model_dump()
        except Exception:
            pass
        
        try:
            return {
                "column_name": fk.column_name,
                "referenced_table_name": fk.referenced_table_name,
                "referenced_column_name": fk.referenced_column_name,
                "referenced_schema": fk.referenced_schema
            }
        except Exception as e:
            logger.warning(f"Error in _fk_to_dict: {str(e)}")
            # Return minimal FK info
            return {
                "column_name": getattr(fk, 'column_name', 'unknown'),
                "referenced_table_name": getattr(fk, 'referenced_table_name', 'unknown'),
                "referenced_column_name": getattr(fk, 'referenced_column_name', 'unknown')
            }
            
    async def _find_relevant_tables(self, query: str, target_tables: List[str] = None, target_columns: Dict[str, List[str]] = None) -> List[DatabaseTable]:
        """Find tables relevant to the query.
        
        Args:
            query: The user's query
            target_tables: Optional list of specific table names to consider
            target_columns: Optional mapping of table names to column names to consider
            
        Returns:
            A list of relevant tables with their columns
        """
        # If specific tables are provided, use them directly
        if target_tables:
            logger.info(f"Using specifically targeted tables: {target_tables}")
            relevant_tables = []
            for table in self.tables:
                if table.name in target_tables:
                    # If we have targeted columns for this table, filter the columns
                    if target_columns and table.name in target_columns:
                        # Create a copy of the table with only the requested columns
                        filtered_table = DatabaseTable(
                            name=table.name,
                            schema=table.schema,
                            description=table.description,
                            primary_keys=table.primary_keys,
                            foreign_keys=table.foreign_keys,
                            row_count=table.row_count,
                            metadata=table.metadata
                        )
                        
                        # Filter columns
                        targeted_column_names = target_columns[table.name]
                        filtered_table.columns = [
                            col for col in table.columns 
                            if col.name in targeted_column_names
                        ]
                        
                        relevant_tables.append(filtered_table)
                    else:
                        # Use the full table if no column targeting
                        relevant_tables.append(table)
            
            # If we found targeted tables, return them
            if relevant_tables:
                return relevant_tables
            
            # If specified tables weren't found, log a warning and proceed with LLM search
            logger.warning(f"Specified target tables not found in database, falling back to LLM search")
        
        # Create a detailed description of all tables and columns
        db_schema_description = self._create_schema_description()
        
        # Craft prompt for LLM
        system_prompt = RELEVANT_TABLES_SYSTEM_PROMPT
        
        user_prompt = f"""Database Schema:
{db_schema_description}

User Query:
{query}

Return only the names of relevant tables as a JSON array. Be very selective."""
        
        logger.info(f"Searching for relevant tables in database {self.database.name}")
        
        # Call Bedrock to identify relevant tables
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.2  # Low temperature for focused response
        )
        
        # Parse response to get table names
        try:
            # Clean response to extract only the JSON part
            response = response.strip()
            if response.startswith("```json"):
                response = response[7:]
            if response.endswith("```"):
                response = response[:-3]
            
            relevant_table_names = json.loads(response)
            logger.info(f"LLM identified these relevant tables: {relevant_table_names}")
            
            # Get full table objects
            relevant_tables = []
            for table in self.tables:
                if table.name in relevant_table_names:
                    # If we have targeted columns for this table, filter the columns
                    if target_columns and table.name in target_columns:
                        # Create a copy of the table with only the requested columns
                        filtered_table = DatabaseTable(
                            name=table.name,
                            schema=table.schema,
                            description=table.description,
                            primary_keys=table.primary_keys,
                            foreign_keys=table.foreign_keys,
                            row_count=table.row_count,
                            metadata=table.metadata
                        )
                        
                        # Filter columns
                        targeted_column_names = target_columns[table.name]
                        filtered_table.columns = [
                            col for col in table.columns 
                            if col.name in targeted_column_names
                        ]
                        
                        relevant_tables.append(filtered_table)
                    else:
                        # Use the full table if no column targeting
                        relevant_tables.append(table)
                    
            return relevant_tables
            
        except json.JSONDecodeError:
            logger.error(f"Error parsing LLM response in agent {self.agent_id}: {response}")
            # Fallback: Try to extract table names from text response
            import re
            table_name_matches = re.findall(r'"([^"]+)"', response)
            logger.info(f"Fallback extraction found these table names: {table_name_matches}")
            
            relevant_tables = []
            for table in self.tables:
                if table.name in table_name_matches:
                    # If we have targeted columns for this table, filter the columns
                    if target_columns and table.name in target_columns:
                        # Create a copy of the table with only the requested columns
                        filtered_table = DatabaseTable(
                            name=table.name,
                            schema=table.schema,
                            description=table.description,
                            primary_keys=table.primary_keys,
                            foreign_keys=table.foreign_keys,
                            row_count=table.row_count,
                            metadata=table.metadata
                        )
                        
                        # Filter columns
                        targeted_column_names = target_columns[table.name]
                        filtered_table.columns = [
                            col for col in table.columns 
                            if col.name in targeted_column_names
                        ]
                        
                        relevant_tables.append(filtered_table)
                    else:
                        # Use the full table if no column targeting
                        relevant_tables.append(table)
                    
            return relevant_tables
        
    def _create_schema_description(self) -> str:
        """Create a description of the database schema.
        
        Returns:
            A text description of tables and columns
        """
        description = []
        
        for table in self.tables:
            # Table description
            table_desc = f"Table: {table.name}"
            if table.schema:
                table_desc += f" (Schema: {table.schema})"
            if table.description:
                table_desc += f" - {table.description}"
            description.append(table_desc)
            
            # Column descriptions
            for column in table.columns:
                col_desc = f"  - {column.name} ({column.data_type})"
                
                attributes = []
                if column.is_primary_key:
                    attributes.append("Primary Key")
                if column.is_foreign_key:
                    for fk in table.foreign_keys:
                        if fk.column_name == column.name:
                            attributes.append(f"Foreign Key to {fk.referenced_table_name}.{fk.referenced_column_name}")
                if column.is_unique:
                    attributes.append("Unique")
                if not column.is_nullable:
                    attributes.append("Not Null")
                    
                if attributes:
                    col_desc += f" [{', '.join(attributes)}]"
                    
                if column.description:
                    col_desc += f" - {column.description}"
                    
                description.append(col_desc)
                
            description.append("")  # Empty line between tables
            
        return "\n".join(description)


# =========================================================================
# BACKWARD COMPATIBILITY
# =========================================================================

# Alias for backward compatibility with existing code
DatabaseAgent = EnhancedDatabaseAgent

# Export both names for flexibility
__all__ = ["EnhancedDatabaseAgent", "DatabaseAgent"]
