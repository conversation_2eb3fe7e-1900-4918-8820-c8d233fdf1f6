#!/usr/bin/env node

/**
 * Environment Switching Utility for Agent Report UI
 * 
 * This script helps developers quickly switch between different API environments
 * by updating the .env file with the appropriate configuration.
 * 
 * Usage:
 *   npm run env:local        # Switch to local development
 *   npm run env:production   # Switch to production  
 *   npm run env:staging      # Switch to staging
 *   npm run env:status       # Show current environment
 *   npm run env:help         # Show help information
 */

const fs = require('fs');
const path = require('path');

// Environment configurations
const ENVIRONMENTS = {
  local: {
    NEXT_PUBLIC_API_BASE: 'http://localhost:8000',
    NODE_ENV: 'development',
    description: 'Local Development Server'
  },
  production: {
    NEXT_PUBLIC_API_BASE: 'https://agentreportbackend.vercel.app',
    NODE_ENV: 'production',
    description: 'Production Server'
  },
  staging: {
    NEXT_PUBLIC_API_BASE: 'https://staging-agentreportbackend.vercel.app',
    NODE_ENV: 'staging',
    description: 'Staging Server'
  }
};

const ENV_FILE_PATH = path.join(process.cwd(), '.env');

/**
 * Read the current .env file
 */
function readEnvFile() {
  try {
    if (!fs.existsSync(ENV_FILE_PATH)) {
      console.log('⚠️  .env file not found. Creating from .env.example...');
      const examplePath = path.join(process.cwd(), '.env.example');
      if (fs.existsSync(examplePath)) {
        fs.copyFileSync(examplePath, ENV_FILE_PATH);
        console.log('✅ Created .env file from .env.example');
      } else {
        console.error('❌ .env.example file not found. Please create .env manually.');
        process.exit(1);
      }
    }
    return fs.readFileSync(ENV_FILE_PATH, 'utf8');
  } catch (error) {
    console.error('❌ Error reading .env file:', error.message);
    process.exit(1);
  }
}

/**
 * Write the updated .env file
 */
function writeEnvFile(content) {
  try {
    fs.writeFileSync(ENV_FILE_PATH, content, 'utf8');
  } catch (error) {
    console.error('❌ Error writing .env file:', error.message);
    process.exit(1);
  }
}

/**
 * Update environment variables in the .env content
 */
function updateEnvContent(content, envConfig) {
  let updatedContent = content;
  
  // Update each environment variable
  Object.entries(envConfig).forEach(([key, value]) => {
    const regex = new RegExp(`^${key}=.*$`, 'm');
    const newLine = `${key}="${value}"`;
    
    if (regex.test(updatedContent)) {
      updatedContent = updatedContent.replace(regex, newLine);
    } else {
      // Add the variable if it doesn't exist
      updatedContent += `\n${newLine}`;
    }
  });
  
  return updatedContent;
}

/**
 * Get current environment from .env file
 */
function getCurrentEnvironment() {
  const content = readEnvFile();
  const apiBaseMatch = content.match(/^NEXT_PUBLIC_API_BASE="?([^"\n]+)"?$/m);
  const nodeEnvMatch = content.match(/^NODE_ENV="?([^"\n]+)"?$/m);
  
  const apiBase = apiBaseMatch ? apiBaseMatch[1] : 'unknown';
  const nodeEnv = nodeEnvMatch ? nodeEnvMatch[1] : 'unknown';
  
  // Find matching environment
  for (const [envName, config] of Object.entries(ENVIRONMENTS)) {
    if (config.NEXT_PUBLIC_API_BASE === apiBase && config.NODE_ENV === nodeEnv) {
      return { name: envName, config, apiBase, nodeEnv };
    }
  }
  
  return { name: 'custom', config: null, apiBase, nodeEnv };
}

/**
 * Switch to a specific environment
 */
function switchEnvironment(envName) {
  if (!ENVIRONMENTS[envName]) {
    console.error(`❌ Unknown environment: ${envName}`);
    console.log('Available environments:', Object.keys(ENVIRONMENTS).join(', '));
    process.exit(1);
  }
  
  const envConfig = ENVIRONMENTS[envName];
  const content = readEnvFile();
  const updatedContent = updateEnvContent(content, envConfig);
  
  writeEnvFile(updatedContent);
  
  console.log(`✅ Switched to ${envName} environment`);
  console.log(`📍 API Base URL: ${envConfig.NEXT_PUBLIC_API_BASE}`);
  console.log(`🔧 Node Environment: ${envConfig.NODE_ENV}`);
  console.log(`📝 Description: ${envConfig.description}`);
  console.log('');
  console.log('⚠️  Please restart your development server for changes to take effect.');
}

/**
 * Show current environment status
 */
function showStatus() {
  const current = getCurrentEnvironment();
  
  console.log('🔍 Current Environment Status');
  console.log('================================');
  console.log(`Environment: ${current.name}`);
  console.log(`API Base URL: ${current.apiBase}`);
  console.log(`Node Environment: ${current.nodeEnv}`);
  
  if (current.config) {
    console.log(`Description: ${current.config.description}`);
  }
  
  console.log('');
  console.log('Available environments:');
  Object.entries(ENVIRONMENTS).forEach(([name, config]) => {
    const indicator = current.name === name ? '👉' : '  ';
    console.log(`${indicator} ${name}: ${config.NEXT_PUBLIC_API_BASE} (${config.NODE_ENV})`);
  });
}

/**
 * Show help information
 */
function showHelp() {
  console.log('🚀 Agent Report UI - Environment Switching Utility');
  console.log('==================================================');
  console.log('');
  console.log('Usage:');
  console.log('  npm run env:local        Switch to local development');
  console.log('  npm run env:production   Switch to production');
  console.log('  npm run env:staging      Switch to staging');
  console.log('  npm run env:status       Show current environment');
  console.log('  npm run env:help         Show this help');
  console.log('');
  console.log('Available Environments:');
  Object.entries(ENVIRONMENTS).forEach(([name, config]) => {
    console.log(`  ${name.padEnd(12)} ${config.NEXT_PUBLIC_API_BASE}`);
    console.log(`  ${' '.repeat(12)} ${config.description}`);
    console.log('');
  });
  console.log('Notes:');
  console.log('  • Changes require restarting the development server');
  console.log('  • The .env file will be automatically updated');
  console.log('  • Original comments and formatting are preserved');
}

// Main execution
const command = process.argv[2];

switch (command) {
  case 'local':
  case 'production':
  case 'staging':
    switchEnvironment(command);
    break;
  case 'status':
    showStatus();
    break;
  case 'help':
  case undefined:
    showHelp();
    break;
  default:
    console.error(`❌ Unknown command: ${command}`);
    console.log('Run "npm run env:help" for usage information.');
    process.exit(1);
}
