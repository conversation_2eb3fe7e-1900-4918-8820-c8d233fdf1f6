"""Supabase Service

This module provides direct Supabase database operations for enhanced user state detection
and database management. It complements the existing SQLAlchemy approach with Supabase-specific
optimizations and direct API access.
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import json

from app.config import settings

logger = logging.getLogger(__name__)

class SupabaseService:
    """Service for direct Supabase database operations.
    
    This service provides Supabase-specific optimizations for user state detection
    and database operations while maintaining compatibility with the existing
    SQLAlchemy-based authentication system.
    """
    
    def __init__(self):
        """Initialize the Supabase service."""
        self.project_id = "fnwrnsojpsmleatmvmjw"  # From environment analysis
        self.database_url = settings.AUTH_DATABASE_URL
        logger.info("Supabase service initialized for enhanced user state detection")
    
    def validate_supabase_connection(self) -> bool:
        """Validate that we have a proper Supabase connection.
        
        Returns:
            bool: True if connection is valid, False otherwise
        """
        try:
            if not self.database_url:
                logger.error("AUTH_DATABASE_URL not configured")
                return False
                
            if "supabase.com" not in self.database_url:
                logger.warning("Database URL does not appear to be Supabase")
                return False
                
            logger.info("Supabase connection validation successful")
            return True
            
        except Exception as e:
            logger.error(f"Supabase connection validation failed: {str(e)}")
            return False
    
    def get_user_state_analytics(self, user_id: str) -> Dict[str, Any]:
        """Get comprehensive user state analytics for enhanced detection.
        
        This method provides additional insights beyond basic user state detection
        by analyzing user behavior patterns and engagement metrics.
        
        Args:
            user_id: The user ID to analyze
            
        Returns:
            Dict[str, Any]: Analytics data for user state determination
        """
        try:
            analytics = {
                "user_id": user_id,
                "analysis_timestamp": datetime.utcnow().isoformat(),
                "state_indicators": {
                    "profile_completion_score": 0,
                    "engagement_level": "new",
                    "onboarding_progress": 0,
                    "feature_usage": [],
                    "login_frequency": "first_time"
                },
                "recommendations": []
            }
            
            # This would be enhanced with actual database queries in a full implementation
            # For now, we provide the structure for future enhancement
            
            logger.debug(f"Generated user state analytics for user {user_id[:8]}...")
            return analytics
            
        except Exception as e:
            logger.error(f"Error generating user state analytics for user {user_id}: {str(e)}")
            return {
                "user_id": user_id,
                "error": str(e),
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
    
    def optimize_user_lookup_query(self, email: str, auth_provider: str = None) -> Dict[str, Any]:
        """Optimize user lookup queries for Supabase performance.
        
        This method provides query optimization hints and caching strategies
        specifically for Supabase PostgreSQL.
        
        Args:
            email: User email to lookup
            auth_provider: Optional auth provider filter
            
        Returns:
            Dict[str, Any]: Query optimization metadata
        """
        try:
            optimization_hints = {
                "query_type": "user_lookup",
                "email_domain": email.split('@')[1] if '@' in email else 'unknown',
                "auth_provider": auth_provider,
                "suggested_indexes": [
                    "users_email_idx",
                    "users_auth_provider_idx",
                    "users_email_auth_provider_idx"
                ],
                "cache_strategy": "short_term",
                "performance_notes": [
                    "Email lookups are optimized with B-tree index",
                    "Auth provider filtering uses composite index",
                    "Consider connection pooling for high frequency lookups"
                ]
            }
            
            logger.debug(f"Generated query optimization hints for email domain: {optimization_hints['email_domain']}")
            return optimization_hints
            
        except Exception as e:
            logger.error(f"Error generating query optimization hints: {str(e)}")
            return {"error": str(e)}
    
    def enhance_user_state_detection(self, user_data: Dict[str, Any], auth_method: str) -> Dict[str, Any]:
        """Enhance user state detection with Supabase-specific insights.
        
        This method provides additional context and recommendations for user state
        determination beyond the basic boolean result.
        
        Args:
            user_data: User data dictionary
            auth_method: Authentication method used
            
        Returns:
            Dict[str, Any]: Enhanced state detection results
        """
        try:
            enhanced_result = {
                "is_new_user": True,  # Default to new user for safety
                "confidence_score": 0.0,
                "state_factors": [],
                "recommendations": [],
                "supabase_optimizations": []
            }
            
            if not user_data:
                enhanced_result.update({
                    "is_new_user": True,
                    "confidence_score": 1.0,
                    "state_factors": ["no_user_record"],
                    "recommendations": ["create_user_profile", "start_onboarding"]
                })
                return enhanced_result
            
            # Analyze various factors
            factors = []
            confidence = 0.0
            
            # Check last login
            if not user_data.get('last_login'):
                factors.append("first_login")
                confidence += 0.3
            
            # Check profile completion
            if auth_method == 'oauth' and not user_data.get('full_name'):
                factors.append("incomplete_oauth_profile")
                confidence += 0.2
            
            # Check email verification for local accounts
            if not user_data.get('is_email_verified') and user_data.get('auth_provider') == 'local':
                factors.append("unverified_local_account")
                confidence += 0.3
            
            # Check onboarding status
            settings_data = user_data.get('settings', {})
            if settings_data is None:
                settings_data = {}
            elif isinstance(settings_data, str):
                try:
                    settings_data = json.loads(settings_data)
                except (ValueError, TypeError):
                    settings_data = {}

            if not settings_data.get('onboarding_completed', False):
                factors.append("onboarding_incomplete")
                confidence += 0.2
            
            # Determine final state
            is_new = confidence > 0.1  # If any significant factors indicate new user
            
            enhanced_result.update({
                "is_new_user": is_new,
                "confidence_score": min(confidence, 1.0),
                "state_factors": factors,
                "recommendations": self._generate_recommendations(factors, auth_method),
                "supabase_optimizations": [
                    "Use prepared statements for repeated queries",
                    "Leverage Supabase real-time subscriptions for state changes",
                    "Implement row-level security for user data protection"
                ]
            })
            
            logger.debug(f"Enhanced user state detection completed with confidence: {confidence:.2f}")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in enhanced user state detection: {str(e)}")
            return {
                "is_new_user": True,  # Safe default
                "confidence_score": 0.0,
                "error": str(e)
            }
    
    def _generate_recommendations(self, factors: List[str], auth_method: str) -> List[str]:
        """Generate recommendations based on user state factors.
        
        Args:
            factors: List of factors indicating user state
            auth_method: Authentication method used
            
        Returns:
            List[str]: List of recommendations
        """
        recommendations = []
        
        if "first_login" in factors:
            recommendations.append("Show welcome tutorial")
            recommendations.append("Collect user preferences")
        
        if "incomplete_oauth_profile" in factors:
            recommendations.append("Request additional profile information")
            recommendations.append("Sync with OAuth provider data")
        
        if "unverified_local_account" in factors:
            recommendations.append("Send email verification")
            recommendations.append("Limit account functionality until verified")
        
        if "onboarding_incomplete" in factors:
            recommendations.append("Resume onboarding flow")
            recommendations.append("Highlight key features")
        
        return recommendations
    
    def log_user_state_event(self, user_id: str, event_type: str, details: Dict[str, Any]) -> bool:
        """Log user state events for analytics and debugging.
        
        Args:
            user_id: User ID
            event_type: Type of state event
            details: Event details
            
        Returns:
            bool: True if logged successfully
        """
        try:
            log_entry = {
                "timestamp": datetime.utcnow().isoformat(),
                "user_id": user_id[:8] + "..." if len(user_id) > 8 else user_id,  # Truncate for privacy
                "event_type": event_type,
                "details": details,
                "service": "supabase_service"
            }
            
            logger.info(f"User state event: {event_type} for user {log_entry['user_id']}")
            logger.debug(f"Event details: {json.dumps(log_entry, indent=2)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error logging user state event: {str(e)}")
            return False
