"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, CheckCircle, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OnboardingLoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  submessage?: string;
  type?: 'loading' | 'success' | 'completing';
}

export default function OnboardingLoadingOverlay({
  isVisible,
  message = 'Setting up your account...',
  submessage = 'This will only take a moment',
  type = 'loading',
}: OnboardingLoadingOverlayProps) {
  if (!isVisible) return null;

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-600 dark:text-green-400" />;
      case 'completing':
        return <Sparkles className="h-12 w-12 text-blue-600 dark:text-blue-400 animate-pulse" />;
      default:
        return <Loader2 className="h-12 w-12 text-blue-600 dark:text-blue-400 animate-spin" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-50 dark:bg-green-900/20',
          border: 'border-green-200 dark:border-green-800',
          text: 'text-green-900 dark:text-green-100',
          subtext: 'text-green-700 dark:text-green-300',
        };
      case 'completing':
        return {
          bg: 'bg-blue-50 dark:bg-blue-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          text: 'text-blue-900 dark:text-blue-100',
          subtext: 'text-blue-700 dark:text-blue-300',
        };
      default:
        return {
          bg: 'bg-white dark:bg-slate-900',
          border: 'border-slate-200 dark:border-slate-700',
          text: 'text-slate-900 dark:text-slate-100',
          subtext: 'text-slate-600 dark:text-slate-400',
        };
    }
  };

  const colors = getColors();

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <Card className={cn(
        "w-full max-w-md mx-4 shadow-2xl",
        colors.bg,
        colors.border
      )}>
        <CardContent className="p-8 text-center">
          <div className="mb-6 flex justify-center">
            {getIcon()}
          </div>
          
          <h3 className={cn(
            "text-xl font-semibold mb-2",
            colors.text
          )}>
            {message}
          </h3>
          
          <p className={cn(
            "text-sm",
            colors.subtext
          )}>
            {submessage}
          </p>

          {type === 'loading' && (
            <div className="mt-6">
              <div className="flex justify-center space-x-1">
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                <div className="w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
              </div>
            </div>
          )}

          {type === 'completing' && (
            <div className="mt-6">
              <div className="flex justify-center">
                <div className="w-8 h-1 bg-blue-200 dark:bg-blue-800 rounded-full overflow-hidden">
                  <div className="w-full h-full bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
