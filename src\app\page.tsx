"use client";
import React from 'react';
import { Hero } from '@/components/ui/animated-hero';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/providers/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ThemeToggle } from '@/components/ui/theme-toggle';

export default function LandingPage() {
  const { isAuthenticated, signIn } = useAuth();
  const router = useRouter();

  // Handle sign in button click
  const handleSignIn = async () => {
    if (isAuthenticated) {
      // If already authenticated, go to dashboard
      router.push('/dashboard');
    } else {
      // Try to sign in with stored tokens, or redirect to login (with redirect enabled)
      await signIn(undefined, true);
    }
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--sidebar-bg)' }}>
      {/* Navigation Header */}
      <header 
        className="backdrop-blur supports-[backdrop-filter]:bg-background/60"
        style={{ 
          borderBottom: '1px solid var(--sidebar-border)',
          backgroundColor: 'var(--sidebar-bg)'
        }}
      >
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div 
              className="w-8 h-8 rounded-lg flex items-center justify-center"
              style={{ backgroundColor: 'var(--surface-selected)' }}
            >
              <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" style={{ color: 'var(--sidebar-icon)' }}>
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h1 className="text-xl font-semibold" style={{ color: 'var(--sidebar-text-primary)' }}>Agent Platform</h1>
          </div>

          <div className="flex items-center gap-4">
            <ThemeToggle />
            <div className="flex items-center gap-2">
              {isAuthenticated ? (
                <Button 
                  onClick={() => router.push('/dashboard')}
                  className="border-0"
                  style={{
                    backgroundColor: 'var(--surface-selected)',
                    color: 'var(--sidebar-text-primary)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--surface-selected)';
                  }}
                >
                  Go to Dashboard
                </Button>
              ) : (
                <>
                  <Button 
                    variant="ghost" 
                    onClick={handleSignIn}
                    className="border-0"
                    style={{
                      color: 'var(--sidebar-text-secondary)',
                      backgroundColor: 'transparent'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }}
                  >
                    Sign In
                  </Button>
                  <Button 
                    asChild
                    className="border-0"
                    style={{
                      backgroundColor: 'var(--surface-selected)',
                      color: 'var(--sidebar-text-primary)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--surface-selected)';
                    }}
                  >
                    <Link href="/register">Get Started</Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main>
        <Hero />
      </main>

      {/* Footer */}
      <footer 
        style={{ 
          borderTop: '1px solid var(--sidebar-border)',
          backgroundColor: 'var(--sidebar-bg)'
        }}
      >
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm" style={{ color: 'var(--sidebar-text-tertiary)' }}>
            <p>&copy; 2024 Agent Platform. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}