"""
Table Exploration Service
========================

Advanced table discussion features for exploring table structures, relationships,
metadata, and performing progressive data exploration with comparative analysis.
"""

import logging
import json
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass, asdict
from enum import Enum
from datetime import datetime

from app.models.database import DatabaseTable, DatabaseColumn, ForeignKeyRelationship
from app.services.database_service import DatabaseService
from app.services.conversational_context_service import conversational_context_service
from app.utils.bedrock_client import BedrockClient

logger = logging.getLogger(__name__)


class ExplorationDepth(Enum):
    """Levels of table exploration depth."""
    BASIC = "basic"           # Table name, column count
    DETAILED = "detailed"     # Columns, types, constraints
    COMPREHENSIVE = "comprehensive"  # Relationships, metadata, sample data
    ANALYTICAL = "analytical"  # Data patterns, quality, insights


class RelationshipType(Enum):
    """Types of table relationships."""
    FOREIGN_KEY = "foreign_key"
    POTENTIAL_FK = "potential_foreign_key"
    NAMING_PATTERN = "naming_pattern"
    DATA_PATTERN = "data_pattern"


@dataclass
class TableInsight:
    """Insights about a table."""
    table_name: str
    insight_type: str
    description: str
    confidence: float
    supporting_data: Dict[str, Any]
    recommendations: List[str]


@dataclass
class RelationshipInsight:
    """Insights about table relationships."""
    from_table: str
    to_table: str
    relationship_type: RelationshipType
    strength: float
    description: str
    columns_involved: List[Tuple[str, str]]  # (from_column, to_column)


@dataclass
class TableExplorationResult:
    """Complete table exploration result."""
    table_name: str
    database_id: str
    exploration_depth: ExplorationDepth
    basic_info: Dict[str, Any]
    detailed_info: Optional[Dict[str, Any]] = None
    relationships: List[RelationshipInsight] = None
    insights: List[TableInsight] = None
    sample_data: Optional[List[Dict[str, Any]]] = None
    data_quality: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.relationships is None:
            self.relationships = []
        if self.insights is None:
            self.insights = []


class TableExplorationService:
    """Service for advanced table exploration and discussion."""
    
    def __init__(self):
        self.database_service = DatabaseService()
        self.bedrock_client = BedrockClient()
        self._exploration_cache: Dict[str, TableExplorationResult] = {}
        
    async def explore_table(
        self,
        database_id: str,
        table_name: str,
        depth: ExplorationDepth = ExplorationDepth.DETAILED,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> TableExplorationResult:
        """Explore a table with specified depth."""
        
        cache_key = f"{database_id}:{table_name}:{depth.value}"
        
        # Check cache first
        if cache_key in self._exploration_cache:
            return self._exploration_cache[cache_key]
        
        try:
            # Get basic table metadata
            table_metadata = await self.database_service.get_table_metadata(
                database_id, table_name
            )
            
            # Build exploration result
            result = TableExplorationResult(
                table_name=table_name,
                database_id=database_id,
                exploration_depth=depth,
                basic_info=self._extract_basic_info(table_metadata)
            )
            
            # Add detailed information if requested
            if depth in [ExplorationDepth.DETAILED, ExplorationDepth.COMPREHENSIVE, ExplorationDepth.ANALYTICAL]:
                result.detailed_info = self._extract_detailed_info(table_metadata)
            
            # Add relationships if requested
            if depth in [ExplorationDepth.COMPREHENSIVE, ExplorationDepth.ANALYTICAL]:
                result.relationships = await self._analyze_table_relationships(
                    database_id, table_metadata
                )
            
            # Add insights and analysis if requested
            if depth == ExplorationDepth.ANALYTICAL:
                result.insights = await self._generate_table_insights(
                    database_id, table_metadata
                )
                result.sample_data = await self._get_sample_data(
                    database_id, table_name
                )
                result.data_quality = await self._analyze_data_quality(
                    database_id, table_metadata
                )
            
            # Update conversational context if session info provided
            if session_id and user_id:
                await self._update_exploration_context(
                    session_id, user_id, result
                )
            
            # Cache result
            self._exploration_cache[cache_key] = result
            
            return result
            
        except Exception as e:
            logger.error(f"Error exploring table {table_name}: {e}")
            raise
    
    async def compare_tables(
        self,
        database_id: str,
        table_names: List[str],
        comparison_aspects: List[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Compare multiple tables across various aspects."""
        
        if comparison_aspects is None:
            comparison_aspects = ["structure", "size", "relationships", "data_types"]
        
        try:
            # Explore all tables
            table_results = {}
            for table_name in table_names:
                table_results[table_name] = await self.explore_table(
                    database_id, table_name, ExplorationDepth.COMPREHENSIVE, session_id
                )
            
            # Perform comparisons
            comparison_result = {
                "tables_compared": table_names,
                "comparison_aspects": comparison_aspects,
                "comparisons": {},
                "insights": [],
                "recommendations": []
            }
            
            # Structure comparison
            if "structure" in comparison_aspects:
                comparison_result["comparisons"]["structure"] = self._compare_table_structures(table_results)
            
            # Size comparison
            if "size" in comparison_aspects:
                comparison_result["comparisons"]["size"] = self._compare_table_sizes(table_results)
            
            # Relationship comparison
            if "relationships" in comparison_aspects:
                comparison_result["comparisons"]["relationships"] = self._compare_table_relationships(table_results)
            
            # Data type comparison
            if "data_types" in comparison_aspects:
                comparison_result["comparisons"]["data_types"] = self._compare_data_types(table_results)
            
            # Generate comparative insights
            comparison_result["insights"] = await self._generate_comparative_insights(
                table_results, comparison_result["comparisons"]
            )
            
            return comparison_result
            
        except Exception as e:
            logger.error(f"Error comparing tables: {e}")
            raise
    
    async def discover_table_patterns(
        self,
        database_id: str,
        pattern_types: List[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Discover patterns across all tables in a database."""
        
        if pattern_types is None:
            pattern_types = ["naming_patterns", "relationship_patterns", "data_patterns"]
        
        try:
            # Get all tables in database
            table_names = await self.database_service.list_tables(database_id)
            
            # Analyze patterns
            pattern_result = {
                "database_id": database_id,
                "total_tables": len(table_names),
                "pattern_types": pattern_types,
                "patterns": {},
                "insights": []
            }
            
            # Naming patterns
            if "naming_patterns" in pattern_types:
                pattern_result["patterns"]["naming"] = await self._analyze_naming_patterns(
                    database_id, table_names
                )
            
            # Relationship patterns
            if "relationship_patterns" in pattern_types:
                pattern_result["patterns"]["relationships"] = await self._analyze_relationship_patterns(
                    database_id, table_names
                )
            
            # Data patterns
            if "data_patterns" in pattern_types:
                pattern_result["patterns"]["data"] = await self._analyze_data_patterns(
                    database_id, table_names
                )
            
            # Generate pattern insights
            pattern_result["insights"] = await self._generate_pattern_insights(
                pattern_result["patterns"]
            )
            
            return pattern_result
            
        except Exception as e:
            logger.error(f"Error discovering table patterns: {e}")
            raise
    
    async def generate_table_discussion_response(
        self,
        query: str,
        exploration_results: List[TableExplorationResult],
        context: Dict[str, Any] = None
    ) -> str:
        """Generate a conversational response about table exploration."""
        
        system_prompt = """You are a database expert who excels at explaining table structures and relationships in conversational language.

When discussing tables, you should:
- Use clear, non-technical language when possible
- Explain relationships and connections between tables
- Point out interesting patterns or insights
- Suggest practical implications of the table structure
- Provide actionable recommendations for data exploration
- Use analogies when helpful to explain complex relationships

Make your explanations engaging and educational."""
        
        # Prepare context for the prompt
        tables_info = []
        for result in exploration_results:
            table_info = {
                "name": result.table_name,
                "basic_info": result.basic_info,
                "detailed_info": result.detailed_info,
                "relationships": [asdict(rel) for rel in result.relationships],
                "insights": [asdict(insight) for insight in result.insights]
            }
            tables_info.append(table_info)
        
        user_prompt = f"""
User asked: "{query}"

Table information:
{json.dumps(tables_info, indent=2)}

Context: {json.dumps(context or {}, indent=2)}

Generate a conversational, informative response that addresses the user's question about these tables.
Focus on making the technical information accessible and interesting.
"""
        
        response = await self.bedrock_client.generate_response(
            prompt=user_prompt,
            system_prompt=system_prompt,
            temperature=0.7
        )
        
        return response.strip()
    
    def _extract_basic_info(self, table_metadata: DatabaseTable) -> Dict[str, Any]:
        """Extract basic information from table metadata."""
        return {
            "table_name": table_metadata.name,
            "schema": table_metadata.db_schema,
            "column_count": len(table_metadata.columns),
            "row_count": table_metadata.row_count,
            "has_primary_key": bool(table_metadata.primary_keys),
            "has_foreign_keys": bool(table_metadata.foreign_keys),
            "primary_key_columns": table_metadata.primary_keys,
            "foreign_key_count": len(table_metadata.foreign_keys)
        }
    
    def _extract_detailed_info(self, table_metadata: DatabaseTable) -> Dict[str, Any]:
        """Extract detailed information from table metadata."""
        columns_info = []
        data_types = {}
        nullable_columns = []
        unique_columns = []
        
        for column in table_metadata.columns:
            col_info = {
                "name": column.name,
                "data_type": column.data_type,
                "is_nullable": column.is_nullable,
                "is_primary_key": column.is_primary_key,
                "is_foreign_key": column.is_foreign_key,
                "is_unique": column.is_unique,
                "default_value": column.default_value
            }
            columns_info.append(col_info)
            
            # Categorize data types
            data_type_category = self._categorize_data_type(column.data_type)
            if data_type_category not in data_types:
                data_types[data_type_category] = []
            data_types[data_type_category].append(column.name)
            
            if column.is_nullable:
                nullable_columns.append(column.name)
            if column.is_unique:
                unique_columns.append(column.name)
        
        return {
            "columns": columns_info,
            "data_type_distribution": data_types,
            "nullable_columns": nullable_columns,
            "unique_columns": unique_columns,
            "foreign_key_details": [
                {
                    "column": fk.column_name,
                    "references_table": fk.referenced_table_name,
                    "references_column": fk.referenced_column_name
                }
                for fk in table_metadata.foreign_keys
            ]
        }
    
    def _categorize_data_type(self, data_type: str) -> str:
        """Categorize a data type into a general category."""
        data_type_lower = data_type.lower()
        
        if any(t in data_type_lower for t in ['int', 'integer', 'bigint', 'smallint']):
            return "integer"
        elif any(t in data_type_lower for t in ['float', 'double', 'decimal', 'numeric']):
            return "numeric"
        elif any(t in data_type_lower for t in ['varchar', 'char', 'text', 'string']):
            return "text"
        elif any(t in data_type_lower for t in ['date', 'time', 'timestamp']):
            return "datetime"
        elif any(t in data_type_lower for t in ['bool', 'boolean']):
            return "boolean"
        elif any(t in data_type_lower for t in ['json', 'jsonb']):
            return "json"
        else:
            return "other"


    async def _analyze_table_relationships(
        self,
        database_id: str,
        table_metadata: DatabaseTable
    ) -> List[RelationshipInsight]:
        """Analyze relationships for a specific table."""
        relationships = []

        # Analyze explicit foreign key relationships
        for fk in table_metadata.foreign_keys:
            relationships.append(RelationshipInsight(
                from_table=table_metadata.name,
                to_table=fk.referenced_table_name,
                relationship_type=RelationshipType.FOREIGN_KEY,
                strength=1.0,
                description=f"Foreign key relationship via {fk.column_name}",
                columns_involved=[(fk.column_name, fk.referenced_column_name)]
            ))

        # Analyze potential relationships based on naming patterns
        all_tables = await self.database_service.list_tables(database_id)
        for column in table_metadata.columns:
            if column.name.endswith('_id') or column.name.endswith('Id'):
                potential_table = column.name[:-3]  # Remove '_id'

                for table_name in all_tables:
                    if table_name.lower() == potential_table.lower():
                        # Check if this isn't already a foreign key
                        is_existing_fk = any(
                            fk.column_name == column.name and fk.referenced_table_name == table_name
                            for fk in table_metadata.foreign_keys
                        )

                        if not is_existing_fk:
                            relationships.append(RelationshipInsight(
                                from_table=table_metadata.name,
                                to_table=table_name,
                                relationship_type=RelationshipType.POTENTIAL_FK,
                                strength=0.7,
                                description=f"Potential relationship based on column naming pattern",
                                columns_involved=[(column.name, "id")]
                            ))

        return relationships

    async def _generate_table_insights(
        self,
        database_id: str,
        table_metadata: DatabaseTable
    ) -> List[TableInsight]:
        """Generate insights about a table."""
        insights = []

        # Column count insight
        column_count = len(table_metadata.columns)
        if column_count > 20:
            insights.append(TableInsight(
                table_name=table_metadata.name,
                insight_type="structure",
                description=f"This table has {column_count} columns, which suggests it might be a wide table with lots of attributes",
                confidence=0.8,
                supporting_data={"column_count": column_count},
                recommendations=["Consider if this table could be normalized", "Review if all columns are necessary"]
            ))

        # Primary key insight
        if not table_metadata.primary_keys:
            insights.append(TableInsight(
                table_name=table_metadata.name,
                insight_type="data_quality",
                description="This table doesn't have a primary key, which could impact data integrity",
                confidence=0.9,
                supporting_data={"has_primary_key": False},
                recommendations=["Consider adding a primary key", "Review data uniqueness requirements"]
            ))

        # Foreign key insights
        fk_count = len(table_metadata.foreign_keys)
        if fk_count > 5:
            insights.append(TableInsight(
                table_name=table_metadata.name,
                insight_type="relationships",
                description=f"This table has {fk_count} foreign key relationships, indicating it's highly connected",
                confidence=0.8,
                supporting_data={"foreign_key_count": fk_count},
                recommendations=["This table might be central to your data model", "Consider it for join operations"]
            ))

        # Data type insights
        data_types = {}
        for column in table_metadata.columns:
            data_type_category = self._categorize_data_type(column.data_type)
            data_types[data_type_category] = data_types.get(data_type_category, 0) + 1

        if data_types.get("text", 0) > data_types.get("numeric", 0) * 2:
            insights.append(TableInsight(
                table_name=table_metadata.name,
                insight_type="data_types",
                description="This table is text-heavy, which might indicate it stores descriptive or categorical data",
                confidence=0.7,
                supporting_data={"data_type_distribution": data_types},
                recommendations=["Good for text analysis and categorization", "Consider indexing text columns for search"]
            ))

        return insights

    async def _get_sample_data(
        self,
        database_id: str,
        table_name: str,
        limit: int = 5
    ) -> Optional[List[Dict[str, Any]]]:
        """Get sample data from a table."""
        try:
            # This would need to be implemented based on your database service
            # For now, return None as placeholder
            return None
        except Exception as e:
            logger.warning(f"Could not get sample data for {table_name}: {e}")
            return None

    async def _analyze_data_quality(
        self,
        database_id: str,
        table_metadata: DatabaseTable
    ) -> Dict[str, Any]:
        """Analyze data quality aspects of a table."""
        quality_analysis = {
            "completeness": {},
            "consistency": {},
            "validity": {},
            "overall_score": 0.0
        }

        # Analyze nullable columns
        nullable_count = sum(1 for col in table_metadata.columns if col.is_nullable)
        total_columns = len(table_metadata.columns)

        if total_columns > 0:
            nullable_ratio = nullable_count / total_columns
            quality_analysis["completeness"]["nullable_columns_ratio"] = nullable_ratio

            if nullable_ratio < 0.3:
                quality_analysis["completeness"]["assessment"] = "Good - Most columns are required"
            elif nullable_ratio < 0.7:
                quality_analysis["completeness"]["assessment"] = "Moderate - Some optional data"
            else:
                quality_analysis["completeness"]["assessment"] = "Concerning - Many optional columns"

        # Analyze primary key presence
        has_pk = bool(table_metadata.primary_keys)
        quality_analysis["validity"]["has_primary_key"] = has_pk
        quality_analysis["validity"]["primary_key_assessment"] = "Good" if has_pk else "Needs attention"

        # Calculate overall score
        pk_score = 1.0 if has_pk else 0.0
        completeness_score = 1.0 - min(nullable_ratio, 1.0) if total_columns > 0 else 0.0
        quality_analysis["overall_score"] = (pk_score + completeness_score) / 2

        return quality_analysis

    async def _update_exploration_context(
        self,
        session_id: str,
        user_id: str,
        exploration_result: TableExplorationResult
    ) -> None:
        """Update conversational context with table exploration results."""
        try:
            # Update table context with exploration results
            await conversational_context_service.update_table_context(
                session_id=session_id,
                table_name=exploration_result.table_name,
                database_id=exploration_result.database_id,
                columns_mentioned=[col["name"] for col in exploration_result.detailed_info.get("columns", [])],
                sample_data=exploration_result.sample_data[0] if exploration_result.sample_data else None,
                row_count=exploration_result.basic_info.get("row_count")
            )
        except Exception as e:
            logger.warning(f"Failed to update exploration context: {e}")

    def _compare_table_structures(self, table_results: Dict[str, TableExplorationResult]) -> Dict[str, Any]:
        """Compare table structures."""
        comparison = {
            "column_counts": {},
            "data_type_distributions": {},
            "constraint_patterns": {},
            "similarities": []
        }

        for table_name, result in table_results.items():
            comparison["column_counts"][table_name] = result.basic_info["column_count"]

            if result.detailed_info:
                comparison["data_type_distributions"][table_name] = result.detailed_info["data_type_distribution"]

                # Analyze constraint patterns
                constraints = {
                    "has_primary_key": result.basic_info["has_primary_key"],
                    "foreign_key_count": result.basic_info["foreign_key_count"],
                    "nullable_columns": len(result.detailed_info["nullable_columns"]),
                    "unique_columns": len(result.detailed_info["unique_columns"])
                }
                comparison["constraint_patterns"][table_name] = constraints

        # Find structural similarities
        table_names = list(table_results.keys())
        for i, table1 in enumerate(table_names):
            for table2 in table_names[i+1:]:
                similarity_score = self._calculate_structural_similarity(
                    table_results[table1], table_results[table2]
                )
                if similarity_score > 0.5:
                    comparison["similarities"].append({
                        "table1": table1,
                        "table2": table2,
                        "similarity_score": similarity_score,
                        "similar_aspects": self._identify_similar_aspects(
                            table_results[table1], table_results[table2]
                        )
                    })

        return comparison

    def _compare_table_sizes(self, table_results: Dict[str, TableExplorationResult]) -> Dict[str, Any]:
        """Compare table sizes."""
        comparison = {
            "row_counts": {},
            "size_categories": {},
            "largest_table": None,
            "smallest_table": None
        }

        row_counts = {}
        for table_name, result in table_results.items():
            row_count = result.basic_info.get("row_count", 0)
            row_counts[table_name] = row_count
            comparison["row_counts"][table_name] = row_count

            # Categorize by size
            if row_count == 0:
                category = "empty"
            elif row_count < 1000:
                category = "small"
            elif row_count < 100000:
                category = "medium"
            elif row_count < 1000000:
                category = "large"
            else:
                category = "very_large"

            comparison["size_categories"][table_name] = category

        # Find largest and smallest
        if row_counts:
            comparison["largest_table"] = max(row_counts, key=row_counts.get)
            comparison["smallest_table"] = min(row_counts, key=row_counts.get)

        return comparison

    def _compare_table_relationships(self, table_results: Dict[str, TableExplorationResult]) -> Dict[str, Any]:
        """Compare table relationships."""
        comparison = {
            "relationship_counts": {},
            "most_connected": None,
            "least_connected": None,
            "relationship_network": {}
        }

        relationship_counts = {}
        for table_name, result in table_results.items():
            rel_count = len(result.relationships)
            relationship_counts[table_name] = rel_count
            comparison["relationship_counts"][table_name] = rel_count

            # Build relationship network
            connections = []
            for rel in result.relationships:
                connections.append({
                    "to_table": rel.to_table,
                    "type": rel.relationship_type.value,
                    "strength": rel.strength
                })
            comparison["relationship_network"][table_name] = connections

        # Find most and least connected
        if relationship_counts:
            comparison["most_connected"] = max(relationship_counts, key=relationship_counts.get)
            comparison["least_connected"] = min(relationship_counts, key=relationship_counts.get)

        return comparison

    def _compare_data_types(self, table_results: Dict[str, TableExplorationResult]) -> Dict[str, Any]:
        """Compare data type distributions."""
        comparison = {
            "type_distributions": {},
            "common_patterns": [],
            "unique_patterns": {}
        }

        all_type_patterns = {}
        for table_name, result in table_results.items():
            if result.detailed_info:
                type_dist = result.detailed_info["data_type_distribution"]
                comparison["type_distributions"][table_name] = type_dist

                # Track patterns across tables
                for data_type, columns in type_dist.items():
                    if data_type not in all_type_patterns:
                        all_type_patterns[data_type] = []
                    all_type_patterns[data_type].append((table_name, len(columns)))

        # Identify common patterns
        for data_type, occurrences in all_type_patterns.items():
            if len(occurrences) >= len(table_results) * 0.7:  # Present in 70% of tables
                comparison["common_patterns"].append({
                    "data_type": data_type,
                    "present_in": len(occurrences),
                    "total_tables": len(table_results)
                })

        return comparison

    def _calculate_structural_similarity(
        self,
        table1: TableExplorationResult,
        table2: TableExplorationResult
    ) -> float:
        """Calculate structural similarity between two tables."""
        similarity_score = 0.0
        factors = 0

        # Column count similarity
        if table1.basic_info["column_count"] > 0 and table2.basic_info["column_count"] > 0:
            col_ratio = min(
                table1.basic_info["column_count"],
                table2.basic_info["column_count"]
            ) / max(
                table1.basic_info["column_count"],
                table2.basic_info["column_count"]
            )
            similarity_score += col_ratio
            factors += 1

        # Data type distribution similarity
        if table1.detailed_info and table2.detailed_info:
            dist1 = table1.detailed_info["data_type_distribution"]
            dist2 = table2.detailed_info["data_type_distribution"]

            common_types = set(dist1.keys()) & set(dist2.keys())
            all_types = set(dist1.keys()) | set(dist2.keys())

            if all_types:
                type_similarity = len(common_types) / len(all_types)
                similarity_score += type_similarity
                factors += 1

        return similarity_score / factors if factors > 0 else 0.0

    def _identify_similar_aspects(
        self,
        table1: TableExplorationResult,
        table2: TableExplorationResult
    ) -> List[str]:
        """Identify specific aspects where tables are similar."""
        similar_aspects = []

        # Similar column counts
        col_diff = abs(table1.basic_info["column_count"] - table2.basic_info["column_count"])
        if col_diff <= 2:
            similar_aspects.append("similar_column_counts")

        # Similar constraint patterns
        if (table1.basic_info["has_primary_key"] == table2.basic_info["has_primary_key"] and
            table1.basic_info["has_foreign_keys"] == table2.basic_info["has_foreign_keys"]):
            similar_aspects.append("similar_constraint_patterns")

        # Similar data type distributions
        if table1.detailed_info and table2.detailed_info:
            dist1 = table1.detailed_info["data_type_distribution"]
            dist2 = table2.detailed_info["data_type_distribution"]

            common_types = set(dist1.keys()) & set(dist2.keys())
            if len(common_types) >= 3:
                similar_aspects.append("similar_data_types")

        return similar_aspects


# Global instance
table_exploration_service = TableExplorationService()
