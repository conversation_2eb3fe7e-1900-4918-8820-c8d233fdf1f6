"""
Data Insights Agent - Phase 3: Intelligent Data Analysis & Insights
===================================================================

This agent provides sophisticated statistical analysis and insight generation capabilities
for database query results. It goes beyond basic data presentation to deliver meaningful
business insights through advanced statistical analysis.

Key Features:
- Statistical Analysis Engine: Trend detection, correlation analysis, anomaly identification
- Pattern Recognition: Automatic discovery of significant patterns in data
- Insight Generation: Intelligent interpretation of statistical findings
- Anomaly Detection: Identification of outliers and unusual patterns
- Trend Analysis: Time-series analysis and forecasting capabilities
"""

import logging
import json
import asyncio
import statistics
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN

from app.agents.base import Agent, AgentResponse
from app.utils.bedrock_client import BedrockClient
from app.config.llm_config import ModelPurpose

logger = logging.getLogger(__name__)


class AnalysisType(Enum):
    """Types of statistical analysis that can be performed."""
    DESCRIPTIVE = "descriptive"
    CORRELATION = "correlation"
    TREND = "trend"
    ANOMALY = "anomaly"
    DISTRIBUTION = "distribution"
    COMPARATIVE = "comparative"


@dataclass
class StatisticalInsight:
    """Represents a statistical insight discovered in the data."""
    insight_type: str
    significance: float  # 0.0 to 1.0
    description: str
    statistical_evidence: Dict[str, Any]
    business_relevance: str
    confidence_level: float


@dataclass
class DataPattern:
    """Represents a pattern discovered in the data."""
    pattern_type: str
    strength: float  # 0.0 to 1.0
    description: str
    affected_columns: List[str]
    statistical_measures: Dict[str, Any]


class DataInsightsAgent(Agent):
    """
    Agent responsible for generating statistical insights and analysis from query results.
    
    This agent takes raw query results and applies sophisticated statistical analysis
    to generate meaningful business insights, detect patterns, and identify anomalies.
    """
    
    def __init__(self, agent_id: Optional[str] = None):
        """Initialize the Data Insights Agent."""
        self.agent_id = agent_id or "data_insights_agent"
        self.bedrock_client = BedrockClient(purpose=ModelPurpose.ANALYSIS)
        self.initialized = False
        
        # Statistical analysis thresholds
        self.correlation_threshold = 0.7
        self.anomaly_threshold = 2.0  # Standard deviations
        self.trend_significance = 0.05  # p-value threshold
        
    async def initialize(self) -> None:
        """Initialize the agent."""
        self.initialized = True
        logger.info(f"✅ {self.agent_id} initialized successfully")
        
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process query results and generate statistical insights.
        
        Expected message format:
        {
            "query_results": [...],  # Raw query results from SQL agent
            "query_context": {...},  # Original query and context
            "analysis_preferences": {...},  # User preferences for analysis
            "enable_advanced_analysis": bool,  # Whether to perform advanced analysis
        }
        
        Returns:
        {
            "statistical_summary": {...},
            "insights": [...],
            "patterns": [...],
            "anomalies": [...],
            "trends": [...],
            "recommendations": [...]
        }
        """
        if not self.initialized:
            await self.initialize()
            
        try:
            # Extract input data
            query_results = message.get("query_results", [])
            query_context = message.get("query_context", {})
            analysis_preferences = message.get("analysis_preferences", {})
            enable_advanced = message.get("enable_advanced_analysis", True)
            
            if not query_results:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    error="No query results provided for analysis"
                ).to_dict()
            
            # Convert results to analyzable format
            analysis_data = await self._prepare_analysis_data(query_results)
            
            if analysis_data is None:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    error="Unable to prepare data for analysis"
                ).to_dict()
            
            # Perform statistical analysis
            statistical_summary = await self._generate_statistical_summary(analysis_data)
            insights = await self._generate_insights(analysis_data, query_context)
            patterns = await self._detect_patterns(analysis_data) if enable_advanced else []
            anomalies = await self._detect_anomalies(analysis_data) if enable_advanced else []
            trends = await self._analyze_trends(analysis_data) if enable_advanced else []
            
            # Generate recommendations based on findings
            recommendations = await self._generate_recommendations(
                statistical_summary, insights, patterns, anomalies, trends, query_context
            )
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={
                    "statistical_summary": statistical_summary,
                    "insights": [insight.__dict__ for insight in insights],
                    "patterns": [pattern.__dict__ for pattern in patterns],
                    "anomalies": anomalies,
                    "trends": trends,
                    "recommendations": recommendations,
                    "analysis_metadata": {
                        "total_records": len(analysis_data),
                        "analysis_timestamp": datetime.utcnow().isoformat(),
                        "analysis_types_performed": self._get_performed_analysis_types(
                            enable_advanced, analysis_data
                        )
                    }
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error in data insights analysis: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Data insights analysis failed: {str(e)}"
            ).to_dict()
    
    async def _prepare_analysis_data(self, query_results: List[Dict[str, Any]]) -> Optional[pd.DataFrame]:
        """Convert query results to pandas DataFrame for analysis."""
        try:
            if not query_results:
                return None
                
            # Handle different result formats
            if isinstance(query_results[0], dict) and "data" in query_results[0]:
                # SQL agent format with columns and data
                result = query_results[0]
                columns = result.get("columns", [])
                data = result.get("data", [])
                
                if not columns or not data:
                    return None
                    
                df = pd.DataFrame(data, columns=columns)
            else:
                # Direct data format
                df = pd.DataFrame(query_results)
            
            # Basic data cleaning
            df = self._clean_data(df)
            
            return df if len(df) > 0 else None
            
        except Exception as e:
            logger.error(f"Error preparing analysis data: {str(e)}")
            return None
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Perform basic data cleaning for analysis."""
        try:
            # Remove completely empty rows
            df = df.dropna(how='all')
            
            # Convert numeric strings to numbers where possible
            for col in df.columns:
                if df[col].dtype == 'object':
                    # Try to convert to numeric
                    numeric_series = pd.to_numeric(df[col], errors='coerce')
                    if not numeric_series.isna().all():
                        df[col] = numeric_series
            
            return df
            
        except Exception as e:
            logger.warning(f"Error in data cleaning: {str(e)}")
            return df
    
    async def _generate_statistical_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Generate comprehensive statistical summary of the data."""
        try:
            summary = {
                "record_count": len(df),
                "column_count": len(df.columns),
                "numeric_columns": [],
                "categorical_columns": [],
                "missing_data_summary": {},
                "data_types": {}
            }
            
            for col in df.columns:
                col_info = {
                    "name": col,
                    "type": str(df[col].dtype),
                    "non_null_count": df[col].count(),
                    "null_count": df[col].isnull().sum(),
                    "null_percentage": (df[col].isnull().sum() / len(df)) * 100
                }
                
                if pd.api.types.is_numeric_dtype(df[col]):
                    # Numeric column analysis
                    col_info.update({
                        "mean": float(df[col].mean()) if len(df[col].dropna()) > 0 else None,
                        "median": float(df[col].median()) if len(df[col].dropna()) > 0 else None,
                        "std": float(df[col].std()) if len(df[col].dropna()) > 0 else None,
                        "min": float(df[col].min()) if len(df[col].dropna()) > 0 else None,
                        "max": float(df[col].max()) if len(df[col].dropna()) > 0 else None,
                        "quartiles": {
                            "q1": float(df[col].quantile(0.25)) if len(df[col].dropna()) > 0 else None,
                            "q3": float(df[col].quantile(0.75)) if len(df[col].dropna()) > 0 else None
                        }
                    })
                    summary["numeric_columns"].append(col_info)
                else:
                    # Categorical column analysis
                    col_info.update({
                        "unique_count": df[col].nunique(),
                        "most_frequent": df[col].mode().iloc[0] if len(df[col].dropna()) > 0 else None,
                        "frequency_distribution": df[col].value_counts().head(10).to_dict()
                    })
                    summary["categorical_columns"].append(col_info)
                
                summary["data_types"][col] = str(df[col].dtype)
                summary["missing_data_summary"][col] = col_info["null_percentage"]
            
            return summary

        except Exception as e:
            logger.error(f"Error generating statistical summary: {str(e)}")
            return {"error": f"Statistical summary generation failed: {str(e)}"}

    async def _generate_insights(self, df: pd.DataFrame, query_context: Dict[str, Any]) -> List[StatisticalInsight]:
        """Generate meaningful insights from the statistical analysis."""
        insights = []

        try:
            # Data quality insights
            missing_data_insight = self._analyze_missing_data(df)
            if missing_data_insight:
                insights.append(missing_data_insight)

            # Distribution insights for numeric columns
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            for col in numeric_cols:
                if len(df[col].dropna()) > 0:
                    distribution_insight = self._analyze_distribution(df[col], col)
                    if distribution_insight:
                        insights.append(distribution_insight)

            # Correlation insights
            if len(numeric_cols) > 1:
                correlation_insights = self._analyze_correlations(df[numeric_cols])
                insights.extend(correlation_insights)

            # Value concentration insights
            concentration_insights = self._analyze_value_concentration(df)
            insights.extend(concentration_insights)

            return insights

        except Exception as e:
            logger.error(f"Error generating insights: {str(e)}")
            return []

    def _analyze_missing_data(self, df: pd.DataFrame) -> Optional[StatisticalInsight]:
        """Analyze missing data patterns."""
        try:
            total_cells = df.size
            missing_cells = df.isnull().sum().sum()
            missing_percentage = (missing_cells / total_cells) * 100

            if missing_percentage > 5:  # Significant missing data
                columns_with_missing = df.columns[df.isnull().any()].tolist()

                return StatisticalInsight(
                    insight_type="data_quality",
                    significance=min(missing_percentage / 100, 1.0),
                    description=f"Dataset has {missing_percentage:.1f}% missing values across {len(columns_with_missing)} columns",
                    statistical_evidence={
                        "missing_percentage": missing_percentage,
                        "affected_columns": columns_with_missing,
                        "total_missing_cells": missing_cells
                    },
                    business_relevance="Missing data may impact analysis reliability and should be addressed",
                    confidence_level=0.95
                )

            return None

        except Exception as e:
            logger.error(f"Error analyzing missing data: {str(e)}")
            return None

    def _analyze_distribution(self, series: pd.Series, column_name: str) -> Optional[StatisticalInsight]:
        """Analyze the distribution of a numeric column."""
        try:
            if len(series) == 0 or series.isnull().all():
                return None

            # Remove null values for analysis
            clean_series = series.dropna()

            if len(clean_series) < 3:
                return None

            # Calculate distribution statistics
            skewness = stats.skew(clean_series)
            kurtosis = stats.kurtosis(clean_series)

            # Determine distribution characteristics
            if abs(skewness) > 1:
                skew_direction = "right" if skewness > 0 else "left"
                return StatisticalInsight(
                    insight_type="distribution",
                    significance=min(abs(skewness) / 3, 1.0),
                    description=f"Column '{column_name}' shows significant {skew_direction}-skewed distribution",
                    statistical_evidence={
                        "skewness": skewness,
                        "kurtosis": kurtosis,
                        "mean": float(clean_series.mean()),
                        "median": float(clean_series.median())
                    },
                    business_relevance=f"Skewed distribution may indicate outliers or specific business patterns in {column_name}",
                    confidence_level=0.85
                )

            return None

        except Exception as e:
            logger.error(f"Error analyzing distribution for {column_name}: {str(e)}")
            return None

    def _analyze_correlations(self, numeric_df: pd.DataFrame) -> List[StatisticalInsight]:
        """Analyze correlations between numeric columns."""
        insights = []

        try:
            if len(numeric_df) == 0 or len(numeric_df.columns) < 2:
                return insights

            # Calculate correlation matrix
            corr_matrix = numeric_df.corr()

            # Find strong correlations
            for i in range(len(corr_matrix.columns)):
                for j in range(i + 1, len(corr_matrix.columns)):
                    col1 = corr_matrix.columns[i]
                    col2 = corr_matrix.columns[j]
                    correlation = corr_matrix.iloc[i, j]

                    if not pd.isna(correlation) and abs(correlation) >= self.correlation_threshold:
                        correlation_type = "positive" if correlation > 0 else "negative"

                        insight = StatisticalInsight(
                            insight_type="correlation",
                            significance=abs(correlation),
                            description=f"Strong {correlation_type} correlation ({correlation:.3f}) between '{col1}' and '{col2}'",
                            statistical_evidence={
                                "correlation_coefficient": correlation,
                                "column_1": col1,
                                "column_2": col2,
                                "correlation_type": correlation_type
                            },
                            business_relevance=f"Strong relationship between {col1} and {col2} may indicate business dependencies",
                            confidence_level=0.90
                        )
                        insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Error analyzing correlations: {str(e)}")
            return insights

    def _analyze_value_concentration(self, df: pd.DataFrame) -> List[StatisticalInsight]:
        """Analyze concentration of values in categorical columns."""
        insights = []

        try:
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns

            for col in categorical_cols:
                if len(df[col].dropna()) == 0:
                    continue

                value_counts = df[col].value_counts()
                total_count = len(df[col].dropna())

                if len(value_counts) > 0:
                    # Check for high concentration in top value
                    top_value_percentage = (value_counts.iloc[0] / total_count) * 100

                    if top_value_percentage > 80:
                        insight = StatisticalInsight(
                            insight_type="concentration",
                            significance=top_value_percentage / 100,
                            description=f"Column '{col}' has high value concentration: {top_value_percentage:.1f}% are '{value_counts.index[0]}'",
                            statistical_evidence={
                                "top_value": value_counts.index[0],
                                "concentration_percentage": top_value_percentage,
                                "unique_values": len(value_counts),
                                "total_records": total_count
                            },
                            business_relevance=f"High concentration in {col} may indicate data quality issues or business patterns",
                            confidence_level=0.85
                        )
                        insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Error analyzing value concentration: {str(e)}")
            return insights

    async def _detect_patterns(self, df: pd.DataFrame) -> List[DataPattern]:
        """Detect patterns in the data using various statistical methods."""
        patterns = []

        try:
            # Detect clustering patterns in numeric data
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) >= 2:
                clustering_patterns = self._detect_clustering_patterns(df[numeric_cols])
                patterns.extend(clustering_patterns)

            # Detect sequential patterns
            sequential_patterns = self._detect_sequential_patterns(df)
            patterns.extend(sequential_patterns)

            # Detect frequency patterns
            frequency_patterns = self._detect_frequency_patterns(df)
            patterns.extend(frequency_patterns)

            return patterns

        except Exception as e:
            logger.error(f"Error detecting patterns: {str(e)}")
            return patterns

    def _detect_clustering_patterns(self, numeric_df: pd.DataFrame) -> List[DataPattern]:
        """Detect clustering patterns in numeric data."""
        patterns = []

        try:
            if len(numeric_df) == 0 or len(numeric_df.columns) < 2:
                return patterns

            # Prepare data for clustering
            clean_df = numeric_df.dropna()
            if len(clean_df) < 10:  # Need sufficient data for clustering
                return patterns

            # Standardize the data
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(clean_df)

            # Apply DBSCAN clustering
            dbscan = DBSCAN(eps=0.5, min_samples=5)
            clusters = dbscan.fit_predict(scaled_data)

            # Analyze clustering results
            unique_clusters = set(clusters)
            if len(unique_clusters) > 1 and -1 in unique_clusters:
                # Found meaningful clusters with outliers
                n_clusters = len(unique_clusters) - 1  # Exclude noise cluster (-1)
                n_outliers = sum(1 for c in clusters if c == -1)

                pattern = DataPattern(
                    pattern_type="clustering",
                    strength=min(n_clusters / len(clean_df), 1.0),
                    description=f"Data shows {n_clusters} distinct clusters with {n_outliers} outliers",
                    affected_columns=list(numeric_df.columns),
                    statistical_measures={
                        "n_clusters": n_clusters,
                        "n_outliers": n_outliers,
                        "cluster_distribution": {str(c): sum(1 for x in clusters if x == c) for c in unique_clusters}
                    }
                )
                patterns.append(pattern)

            return patterns

        except Exception as e:
            logger.error(f"Error detecting clustering patterns: {str(e)}")
            return patterns

    def _detect_sequential_patterns(self, df: pd.DataFrame) -> List[DataPattern]:
        """Detect sequential patterns in the data."""
        patterns = []

        try:
            # Look for potential time-based or sequential columns
            for col in df.columns:
                if df[col].dtype in ['datetime64[ns]', 'object']:
                    # Try to detect if this could be a time series
                    if self._is_potential_time_column(df[col]):
                        pattern = DataPattern(
                            pattern_type="temporal_sequence",
                            strength=0.8,
                            description=f"Column '{col}' appears to contain temporal/sequential data",
                            affected_columns=[col],
                            statistical_measures={
                                "column_type": str(df[col].dtype),
                                "unique_values": df[col].nunique(),
                                "sample_values": df[col].dropna().head(5).tolist()
                            }
                        )
                        patterns.append(pattern)

            return patterns

        except Exception as e:
            logger.error(f"Error detecting sequential patterns: {str(e)}")
            return patterns

    def _is_potential_time_column(self, series: pd.Series) -> bool:
        """Check if a column might contain time-based data."""
        try:
            if len(series) == 0:
                return False

            # Check if it's already datetime
            if pd.api.types.is_datetime64_any_dtype(series):
                return True

            # Try to parse as datetime
            sample = series.dropna().head(10)
            parsed_count = 0

            for value in sample:
                try:
                    pd.to_datetime(str(value))
                    parsed_count += 1
                except:
                    pass

            return parsed_count / len(sample) > 0.7  # 70% parseable as dates

        except Exception:
            return False

    def _detect_frequency_patterns(self, df: pd.DataFrame) -> List[DataPattern]:
        """Detect frequency patterns in categorical data."""
        patterns = []

        try:
            categorical_cols = df.select_dtypes(include=['object', 'category']).columns

            for col in categorical_cols:
                if len(df[col].dropna()) == 0:
                    continue

                value_counts = df[col].value_counts()

                # Check for power law distribution (Pareto principle)
                if len(value_counts) > 5:
                    top_20_percent = int(len(value_counts) * 0.2)
                    top_20_percent = max(1, top_20_percent)

                    top_values_sum = value_counts.head(top_20_percent).sum()
                    total_sum = value_counts.sum()

                    if top_values_sum / total_sum > 0.8:  # 80/20 rule
                        pattern = DataPattern(
                            pattern_type="pareto_distribution",
                            strength=top_values_sum / total_sum,
                            description=f"Column '{col}' follows Pareto distribution (80/20 rule)",
                            affected_columns=[col],
                            statistical_measures={
                                "top_20_percent_contribution": (top_values_sum / total_sum) * 100,
                                "unique_values": len(value_counts),
                                "concentration_ratio": top_values_sum / total_sum
                            }
                        )
                        patterns.append(pattern)

            return patterns

        except Exception as e:
            logger.error(f"Error detecting frequency patterns: {str(e)}")
            return patterns

    async def _detect_anomalies(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Detect anomalies and outliers in the data."""
        anomalies = []

        try:
            numeric_cols = df.select_dtypes(include=[np.number]).columns

            for col in numeric_cols:
                if len(df[col].dropna()) == 0:
                    continue

                clean_series = df[col].dropna()
                if len(clean_series) < 10:
                    continue

                # Statistical outlier detection using Z-score
                z_scores = np.abs(stats.zscore(clean_series))
                outlier_indices = np.where(z_scores > self.anomaly_threshold)[0]

                if len(outlier_indices) > 0:
                    outlier_values = clean_series.iloc[outlier_indices].tolist()

                    anomaly = {
                        "type": "statistical_outlier",
                        "column": col,
                        "count": len(outlier_indices),
                        "percentage": (len(outlier_indices) / len(clean_series)) * 100,
                        "outlier_values": outlier_values[:10],  # Limit to first 10
                        "threshold_used": self.anomaly_threshold,
                        "description": f"Found {len(outlier_indices)} statistical outliers in '{col}'"
                    }
                    anomalies.append(anomaly)

            return anomalies

        except Exception as e:
            logger.error(f"Error detecting anomalies: {str(e)}")
            return anomalies

    async def _analyze_trends(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """Analyze trends in time-series or sequential data."""
        trends = []

        try:
            # Look for potential time columns
            time_cols = []
            for col in df.columns:
                if self._is_potential_time_column(df[col]):
                    time_cols.append(col)

            if not time_cols:
                return trends

            numeric_cols = df.select_dtypes(include=[np.number]).columns

            for time_col in time_cols:
                for numeric_col in numeric_cols:
                    trend_analysis = self._analyze_single_trend(df, time_col, numeric_col)
                    if trend_analysis:
                        trends.append(trend_analysis)

            return trends

        except Exception as e:
            logger.error(f"Error analyzing trends: {str(e)}")
            return trends

    def _analyze_single_trend(self, df: pd.DataFrame, time_col: str, value_col: str) -> Optional[Dict[str, Any]]:
        """Analyze trend for a single time-value pair."""
        try:
            # Prepare data
            subset = df[[time_col, value_col]].dropna()
            if len(subset) < 5:
                return None

            # Try to convert time column to datetime
            try:
                subset[time_col] = pd.to_datetime(subset[time_col])
                subset = subset.sort_values(time_col)
            except:
                # If conversion fails, treat as ordinal
                pass

            # Simple linear trend analysis
            x = np.arange(len(subset))
            y = subset[value_col].values

            # Calculate linear regression
            slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)

            if p_value < self.trend_significance:
                trend_direction = "increasing" if slope > 0 else "decreasing"
                trend_strength = abs(r_value)

                return {
                    "type": "linear_trend",
                    "time_column": time_col,
                    "value_column": value_col,
                    "direction": trend_direction,
                    "strength": trend_strength,
                    "slope": slope,
                    "r_squared": r_value ** 2,
                    "p_value": p_value,
                    "significance": "significant" if p_value < 0.01 else "moderate",
                    "description": f"{trend_direction.capitalize()} trend in '{value_col}' over '{time_col}' (R² = {r_value**2:.3f})"
                }

            return None

        except Exception as e:
            logger.error(f"Error analyzing trend for {time_col} vs {value_col}: {str(e)}")
            return None

    async def _generate_recommendations(
        self,
        statistical_summary: Dict[str, Any],
        insights: List[StatisticalInsight],
        patterns: List[DataPattern],
        anomalies: List[Dict[str, Any]],
        trends: List[Dict[str, Any]],
        query_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate actionable recommendations based on the analysis."""
        recommendations = []

        try:
            # Data quality recommendations
            if statistical_summary.get("missing_data_summary"):
                high_missing_cols = [
                    col for col, pct in statistical_summary["missing_data_summary"].items()
                    if pct > 20
                ]
                if high_missing_cols:
                    recommendations.append({
                        "type": "data_quality",
                        "priority": "high",
                        "title": "Address Missing Data",
                        "description": f"Columns {high_missing_cols} have significant missing data (>20%)",
                        "action": "Consider data imputation strategies or investigate data collection processes"
                    })

            # Correlation-based recommendations
            correlation_insights = [i for i in insights if i.insight_type == "correlation"]
            if correlation_insights:
                recommendations.append({
                    "type": "analysis_opportunity",
                    "priority": "medium",
                    "title": "Explore Relationships",
                    "description": f"Found {len(correlation_insights)} strong correlations in the data",
                    "action": "Consider deeper analysis of these relationships for business insights"
                })

            # Anomaly-based recommendations
            if anomalies:
                total_anomalies = sum(a.get("count", 0) for a in anomalies)
                recommendations.append({
                    "type": "data_investigation",
                    "priority": "medium",
                    "title": "Investigate Outliers",
                    "description": f"Found {total_anomalies} potential outliers across {len(anomalies)} columns",
                    "action": "Review outliers for data quality issues or exceptional business cases"
                })

            # Trend-based recommendations
            if trends:
                significant_trends = [t for t in trends if t.get("significance") == "significant"]
                if significant_trends:
                    recommendations.append({
                        "type": "business_insight",
                        "priority": "high",
                        "title": "Monitor Trends",
                        "description": f"Identified {len(significant_trends)} significant trends in the data",
                        "action": "Set up monitoring and forecasting for these trending metrics"
                    })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating recommendations: {str(e)}")
            return recommendations

    def _get_performed_analysis_types(self, enable_advanced: bool, df: pd.DataFrame) -> List[str]:
        """Get list of analysis types that were performed."""
        analysis_types = ["statistical_summary", "insights"]

        if enable_advanced:
            analysis_types.extend(["patterns", "anomalies", "trends"])

        return analysis_types
