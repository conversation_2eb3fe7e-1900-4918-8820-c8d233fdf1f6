"""Database Migration: Add Token Security Fields

This migration adds enhanced security fields to the refresh_tokens table
and creates a new token_blacklist table for improved token security.
"""

import logging
from datetime import datetime
from sqlalchemy import text, inspect
from sqlalchemy.exc import SQLAlchemyError

from app.utils.db import get_db

logger = logging.getLogger(__name__)


def check_column_exists(db, table_name: str, column_name: str) -> bool:
    """Check if a column exists in a table.
    
    Args:
        db: Database session
        table_name: Name of the table
        column_name: Name of the column
        
    Returns:
        bool: True if column exists, False otherwise
    """
    try:
        inspector = inspect(db.bind)
        columns = [col['name'] for col in inspector.get_columns(table_name)]
        return column_name in columns
    except Exception as e:
        logger.warning(f"Could not check column existence: {e}")
        return False


def check_table_exists(db, table_name: str) -> bool:
    """Check if a table exists.
    
    Args:
        db: Database session
        table_name: Name of the table
        
    Returns:
        bool: True if table exists, False otherwise
    """
    try:
        inspector = inspect(db.bind)
        return table_name in inspector.get_table_names()
    except Exception as e:
        logger.warning(f"Could not check table existence: {e}")
        return False


def add_refresh_token_security_fields(db):
    """Add security fields to refresh_tokens table."""
    logger.info("Adding security fields to refresh_tokens table...")
    
    # List of new columns to add
    new_columns = [
        ("revoked_at", "TIMESTAMP NULL"),
        ("revoked_reason", "VARCHAR(100) NULL"),
        ("last_used_at", "TIMESTAMP NULL"),
        ("use_count", "VARCHAR(10) NOT NULL DEFAULT '0'"),
        ("ip_address", "VARCHAR(45) NULL"),
        ("user_agent", "VARCHAR(500) NULL")
    ]
    
    for column_name, column_def in new_columns:
        if not check_column_exists(db, "refresh_tokens", column_name):
            try:
                # Use appropriate SQL syntax based on database type
                db_url = str(db.bind.url)
                if "postgresql" in db_url:
                    sql = f"ALTER TABLE refresh_tokens ADD COLUMN {column_name} {column_def}"
                elif "sqlite" in db_url:
                    sql = f"ALTER TABLE refresh_tokens ADD COLUMN {column_name} {column_def}"
                else:
                    # MySQL and others
                    sql = f"ALTER TABLE refresh_tokens ADD COLUMN {column_name} {column_def}"
                
                db.execute(text(sql))
                logger.info(f"Added column {column_name} to refresh_tokens table")
            except SQLAlchemyError as e:
                logger.error(f"Failed to add column {column_name}: {e}")
                raise
        else:
            logger.info(f"Column {column_name} already exists in refresh_tokens table")


def create_token_blacklist_table(db):
    """Create token_blacklist table."""
    logger.info("Creating token_blacklist table...")
    
    if check_table_exists(db, "token_blacklist"):
        logger.info("token_blacklist table already exists")
        return
    
    try:
        # Use appropriate SQL syntax based on database type
        db_url = str(db.bind.url)
        
        if "postgresql" in db_url:
            sql = """
            CREATE TABLE token_blacklist (
                id VARCHAR(36) PRIMARY KEY,
                token_hash VARCHAR(64) UNIQUE NOT NULL,
                token_type VARCHAR(20) NOT NULL,
                user_id VARCHAR(36) NOT NULL,
                blacklisted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                reason VARCHAR(100),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
            """
        elif "sqlite" in db_url:
            sql = """
            CREATE TABLE token_blacklist (
                id TEXT PRIMARY KEY,
                token_hash TEXT UNIQUE NOT NULL,
                token_type TEXT NOT NULL,
                user_id TEXT NOT NULL,
                blacklisted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                reason TEXT,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
            """
        else:
            # MySQL and others
            sql = """
            CREATE TABLE token_blacklist (
                id VARCHAR(36) PRIMARY KEY,
                token_hash VARCHAR(64) UNIQUE NOT NULL,
                token_type VARCHAR(20) NOT NULL,
                user_id VARCHAR(36) NOT NULL,
                blacklisted_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                reason VARCHAR(100),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
            )
            """
        
        db.execute(text(sql))
        
        # Create indexes for better performance
        index_sqls = [
            "CREATE INDEX idx_token_blacklist_token_hash ON token_blacklist(token_hash)",
            "CREATE INDEX idx_token_blacklist_user_id ON token_blacklist(user_id)",
            "CREATE INDEX idx_token_blacklist_expires_at ON token_blacklist(expires_at)"
        ]
        
        for index_sql in index_sqls:
            try:
                db.execute(text(index_sql))
            except SQLAlchemyError as e:
                logger.warning(f"Failed to create index: {e}")
        
        logger.info("Created token_blacklist table with indexes")
        
    except SQLAlchemyError as e:
        logger.error(f"Failed to create token_blacklist table: {e}")
        raise


def apply_migration_if_needed():
    """Apply the token security migration if needed."""
    logger.info("Checking if token security migration is needed...")
    
    try:
        db = next(get_db())
        
        # Check if migration is needed
        needs_migration = False
        
        # Check if new columns exist in refresh_tokens
        if not check_column_exists(db, "refresh_tokens", "revoked_at"):
            needs_migration = True
        
        # Check if token_blacklist table exists
        if not check_table_exists(db, "token_blacklist"):
            needs_migration = True
        
        if not needs_migration:
            logger.info("Token security migration not needed - all fields already exist")
            return
        
        logger.info("Applying token security migration...")
        
        # Apply migrations
        add_refresh_token_security_fields(db)
        create_token_blacklist_table(db)
        
        # Commit changes
        db.commit()
        
        logger.info("Token security migration completed successfully")
        
    except Exception as e:
        logger.error(f"Token security migration failed: {e}")
        if 'db' in locals():
            db.rollback()
        raise
    finally:
        if 'db' in locals():
            db.close()


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    apply_migration_if_needed()
