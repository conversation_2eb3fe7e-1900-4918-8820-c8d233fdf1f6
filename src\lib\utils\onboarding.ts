// Onboarding utility functions

import { ROUTES } from '@/lib/constants';

/**
 * Determines the appropriate redirect path based on user authentication and onboarding status
 */
export function getRedirectPath(
  isAuthenticated: boolean,
  isNewUser: boolean,
  currentPath: string
): string | null {
  // Not authenticated - redirect to login (except for public routes)
  if (!isAuthenticated) {
    const publicRoutes = [
      ROUTES.HOME,
      ROUTES.LOGIN,
      ROUTES.REGISTER,
      ROUTES.AUTH.CALLBACK,
      ROUTES.OAUTH.CALLBACK,
      ROUTES.ONBOARDING,
    ];

    if (!publicRoutes.includes(currentPath as any)) {
      return ROUTES.LOGIN;
    }
    return null;
  }

  // Authenticated user scenarios
  if (isAuthenticated) {
    // New user not on onboarding page - redirect to onboarding
    if (isNewUser && currentPath !== ROUTES.ONBOARDING) {
      const publicRoutes = [
        ROUTES.HOME,
        ROUTES.LOGIN,
        ROUTES.REGISTER,
        ROUTES.AUTH.CALLBACK,
        ROUTES.OAUTH.CALLBACK,
      ];

      // Don't redirect if on public routes (except login)
      if (!publicRoutes.includes(currentPath as any) || currentPath === ROUTES.LOGIN) {
        return ROUTES.ONBOARDING;
      }
    }
    
    // Existing user on onboarding page - redirect to dashboard
    if (!isNewUser && currentPath === ROUTES.ONBOARDING) {
      return ROUTES.DASHBOARD;
    }
    
    // Authenticated user on login or register page - redirect based on onboarding status
    if (currentPath === ROUTES.LOGIN || currentPath === ROUTES.REGISTER) {
      return isNewUser ? ROUTES.ONBOARDING : ROUTES.DASHBOARD;
    }
  }

  return null;
}

/**
 * Checks if a route requires authentication
 */
export function isProtectedRoute(path: string): boolean {
  const publicRoutes = [
    ROUTES.HOME,
    ROUTES.LOGIN,
    ROUTES.REGISTER,
    ROUTES.AUTH.CALLBACK,
    ROUTES.OAUTH.CALLBACK,
    ROUTES.ONBOARDING,
  ];

  return !publicRoutes.includes(path as any);
}

/**
 * Checks if a route is accessible to new users
 */
export function isNewUserAccessibleRoute(path: string): boolean {
  const newUserRoutes = [
    ROUTES.HOME,
    ROUTES.LOGIN,
    ROUTES.REGISTER,
    ROUTES.AUTH.CALLBACK,
    ROUTES.OAUTH.CALLBACK,
    ROUTES.ONBOARDING,
  ];

  return newUserRoutes.includes(path as any);
}

/**
 * Gets the next step in the onboarding flow after completion
 */
export function getPostOnboardingRedirect(): string {
  return ROUTES.DASHBOARD;
}

/**
 * Validates if onboarding completion is allowed from the current state
 */
export function canCompleteOnboarding(
  isAuthenticated: boolean,
  isNewUser: boolean,
  currentPath: string
): boolean {
  return isAuthenticated && isNewUser && currentPath === ROUTES.ONBOARDING;
}
