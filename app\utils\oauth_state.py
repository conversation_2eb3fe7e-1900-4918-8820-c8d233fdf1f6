"""OAuth State Management

This module provides secure OAuth state parameter generation and validation
for CSRF protection in OAuth flows.
"""

import hashlib
import hmac
import json
import secrets
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple

from app.config import settings


class OAuthStateManager:
    """Manages OAuth state parameters for CSRF protection."""
    
    def __init__(self, secret_key: str = None):
        """Initialize the OAuth state manager.
        
        Args:
            secret_key: Secret key for signing state parameters
        """
        if secret_key is None:
            secret_key = settings.JWT_SECRET_KEY
        self.secret_key = secret_key.encode() if isinstance(secret_key, str) else secret_key
    
    def generate_state(
        self,
        client_type: str = "browser",
        correlation_id: Optional[str] = None,
        redirect_uri: Optional[str] = None,
        expires_in: int = 600  # 10 minutes
    ) -> str:
        """Generate a secure OAuth state parameter.
        
        Args:
            client_type: Type of client ('spa', 'mobile', 'api', 'browser')
            correlation_id: Request correlation ID
            redirect_uri: Custom redirect URI
            expires_in: State expiration time in seconds
            
        Returns:
            str: Secure state parameter
        """
        # Create state payload
        payload = {
            "client_type": client_type,
            "correlation_id": correlation_id,
            "redirect_uri": redirect_uri,
            "timestamp": int(time.time()),
            "expires_at": int(time.time()) + expires_in,
            "nonce": secrets.token_urlsafe(16)
        }
        
        # Encode payload as JSON
        payload_json = json.dumps(payload, separators=(',', ':'))
        payload_bytes = payload_json.encode('utf-8')
        
        # Create HMAC signature
        signature = hmac.new(
            self.secret_key,
            payload_bytes,
            hashlib.sha256
        ).hexdigest()
        
        # Combine payload and signature
        state_data = {
            "payload": payload_json,
            "signature": signature
        }
        
        # Encode as base64-like string (URL safe)
        import base64
        state_json = json.dumps(state_data, separators=(',', ':'))
        state_encoded = base64.urlsafe_b64encode(state_json.encode()).decode().rstrip('=')
        
        return state_encoded
    
    def validate_state(self, state: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """Validate an OAuth state parameter.
        
        Args:
            state: The state parameter to validate
            
        Returns:
            Tuple[bool, Optional[Dict]]: (is_valid, payload_data)
        """
        try:
            # Decode state
            import base64
            
            # Add padding if needed
            padding = 4 - (len(state) % 4)
            if padding != 4:
                state += '=' * padding
            
            state_json = base64.urlsafe_b64decode(state).decode()
            state_data = json.loads(state_json)
            
            payload_json = state_data.get("payload")
            received_signature = state_data.get("signature")
            
            if not payload_json or not received_signature:
                return False, None
            
            # Verify signature
            payload_bytes = payload_json.encode('utf-8')
            expected_signature = hmac.new(
                self.secret_key,
                payload_bytes,
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(received_signature, expected_signature):
                return False, None
            
            # Parse payload
            payload = json.loads(payload_json)
            
            # Check expiration
            current_time = int(time.time())
            if current_time > payload.get("expires_at", 0):
                return False, None
            
            return True, payload
            
        except Exception:
            return False, None
    
    def extract_client_type(self, state: str) -> str:
        """Extract client type from state parameter.
        
        Args:
            state: The state parameter
            
        Returns:
            str: Client type or 'browser' as default
        """
        is_valid, payload = self.validate_state(state)
        if is_valid and payload:
            return payload.get("client_type", "browser")
        return "browser"
    
    def extract_correlation_id(self, state: str) -> Optional[str]:
        """Extract correlation ID from state parameter.
        
        Args:
            state: The state parameter
            
        Returns:
            Optional[str]: Correlation ID if present
        """
        is_valid, payload = self.validate_state(state)
        if is_valid and payload:
            return payload.get("correlation_id")
        return None


# Global OAuth state manager instance
oauth_state_manager = OAuthStateManager()


def generate_oauth_state(
    client_type: str = "browser",
    correlation_id: Optional[str] = None,
    redirect_uri: Optional[str] = None
) -> str:
    """Generate OAuth state parameter.
    
    Args:
        client_type: Type of client
        correlation_id: Request correlation ID
        redirect_uri: Custom redirect URI
        
    Returns:
        str: OAuth state parameter
    """
    return oauth_state_manager.generate_state(
        client_type=client_type,
        correlation_id=correlation_id,
        redirect_uri=redirect_uri
    )


def validate_oauth_state(state: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
    """Validate OAuth state parameter.
    
    Args:
        state: State parameter to validate
        
    Returns:
        Tuple[bool, Optional[Dict]]: Validation result and payload
    """
    return oauth_state_manager.validate_state(state)


def get_client_type_from_state(state: str) -> str:
    """Get client type from OAuth state.
    
    Args:
        state: OAuth state parameter
        
    Returns:
        str: Client type
    """
    return oauth_state_manager.extract_client_type(state)


def get_correlation_id_from_state(state: str) -> Optional[str]:
    """Get correlation ID from OAuth state.
    
    Args:
        state: OAuth state parameter
        
    Returns:
        Optional[str]: Correlation ID
    """
    return oauth_state_manager.extract_correlation_id(state)
