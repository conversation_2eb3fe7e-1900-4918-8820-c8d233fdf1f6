"""
Comparative Analysis Agent - Phase 3: Intelligent Data Analysis & Insights
==========================================================================

This agent provides sophisticated comparative analysis capabilities including
period-over-period analysis, benchmarking, and performance evaluation.

Key Features:
- Period-over-Period Analysis: Compare metrics across different time periods
- Benchmarking: Compare performance against industry standards or historical baselines
- Cohort Analysis: Analyze behavior patterns across different groups
- Performance Evaluation: Assess performance against targets and goals
- Variance Analysis: Identify and explain significant variances
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import pandas as pd
import numpy as np
from scipy import stats

from app.agents.base import Agent, AgentResponse
from app.utils.bedrock_client import BedrockClient
from app.config.llm_config import ModelPurpose

logger = logging.getLogger(__name__)


class ComparisonType(Enum):
    """Types of comparative analysis that can be performed."""
    PERIOD_OVER_PERIOD = "period_over_period"
    YEAR_OVER_YEAR = "year_over_year"
    MONTH_OVER_MONTH = "month_over_month"
    WEEK_OVER_WEEK = "week_over_week"
    BENCHMARK = "benchmark"
    COHORT = "cohort"
    PERFORMANCE_TARGET = "performance_target"


@dataclass
class ComparativeInsight:
    """Represents a comparative analysis insight."""
    comparison_id: str
    comparison_type: str
    metric_name: str
    current_value: float
    comparison_value: float
    change_amount: float
    change_percentage: float
    significance_level: float
    interpretation: str
    business_impact: str
    trend_direction: str  # improving, declining, stable


@dataclass
class BenchmarkResult:
    """Represents a benchmarking analysis result."""
    metric_name: str
    current_value: float
    benchmark_value: float
    performance_rating: str  # excellent, good, average, below_average, poor
    percentile_rank: Optional[float]
    gap_analysis: str
    improvement_potential: float


class ComparativeAnalysisAgent(Agent):
    """
    Agent responsible for performing comparative analysis on query results.
    
    This agent specializes in comparing data across different dimensions such as
    time periods, cohorts, benchmarks, and performance targets to provide
    meaningful comparative insights.
    """
    
    def __init__(self, agent_id: Optional[str] = None):
        """Initialize the Comparative Analysis Agent."""
        self.agent_id = agent_id or "comparative_analysis_agent"
        self.bedrock_client = BedrockClient(purpose=ModelPurpose.ANALYSIS)
        self.initialized = False
        
        # Analysis thresholds
        self.significance_threshold = 0.05
        self.material_change_threshold = 0.05  # 5% change considered material
        
    async def initialize(self) -> None:
        """Initialize the agent."""
        self.initialized = True
        logger.info(f"✅ {self.agent_id} initialized successfully")
        
    async def process(self, message: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process data for comparative analysis.
        
        Expected message format:
        {
            "current_data": [...],           # Current period data
            "comparison_data": [...],        # Historical/comparison data (optional)
            "comparison_type": "period_over_period",
            "time_column": "date",           # Column containing time information
            "metric_columns": [...],         # Columns to analyze
            "benchmark_data": {...},         # Optional benchmark data
            "targets": {...},                # Optional performance targets
        }
        
        Returns:
        {
            "comparative_insights": [...],
            "benchmark_results": [...],
            "trend_analysis": {...},
            "performance_summary": {...},
            "recommendations": [...]
        }
        """
        if not self.initialized:
            await self.initialize()
            
        try:
            # Extract input data
            current_data = message.get("current_data", [])
            comparison_data = message.get("comparison_data", [])
            comparison_type = message.get("comparison_type", "period_over_period")
            time_column = message.get("time_column")
            metric_columns = message.get("metric_columns", [])
            benchmark_data = message.get("benchmark_data", {})
            targets = message.get("targets", {})
            
            if not current_data:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    error="No current data provided for comparative analysis"
                ).to_dict()
            
            # Convert data to analyzable format
            current_df = await self._prepare_data(current_data)
            comparison_df = await self._prepare_data(comparison_data) if comparison_data else None
            
            if current_df is None:
                return AgentResponse(
                    agent_id=self.agent_id,
                    has_relevant_info=False,
                    error="Unable to prepare data for comparative analysis"
                ).to_dict()
            
            # Auto-detect time and metric columns if not provided
            if not time_column:
                time_column = self._detect_time_column(current_df)
            
            if not metric_columns:
                metric_columns = self._detect_metric_columns(current_df, time_column)
            
            # Perform comparative analysis
            comparative_insights = await self._perform_comparative_analysis(
                current_df, comparison_df, comparison_type, time_column, metric_columns
            )
            
            # Perform benchmarking if benchmark data provided
            benchmark_results = await self._perform_benchmarking(
                current_df, benchmark_data, metric_columns
            ) if benchmark_data else []
            
            # Analyze trends
            trend_analysis = await self._analyze_trends(
                current_df, time_column, metric_columns
            )
            
            # Evaluate performance against targets
            performance_summary = await self._evaluate_performance(
                current_df, targets, metric_columns
            ) if targets else {}
            
            # Generate recommendations
            recommendations = await self._generate_comparative_recommendations(
                comparative_insights, benchmark_results, trend_analysis, performance_summary
            )
            
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=True,
                data={
                    "comparative_insights": [insight.__dict__ for insight in comparative_insights],
                    "benchmark_results": [result.__dict__ for result in benchmark_results],
                    "trend_analysis": trend_analysis,
                    "performance_summary": performance_summary,
                    "recommendations": recommendations,
                    "analysis_metadata": {
                        "comparison_type": comparison_type,
                        "metrics_analyzed": len(metric_columns),
                        "time_column": time_column,
                        "analysis_timestamp": datetime.utcnow().isoformat()
                    }
                }
            ).to_dict()
            
        except Exception as e:
            logger.error(f"Error in comparative analysis: {str(e)}")
            return AgentResponse(
                agent_id=self.agent_id,
                has_relevant_info=False,
                error=f"Comparative analysis failed: {str(e)}"
            ).to_dict()
    
    async def _prepare_data(self, data: List[Dict[str, Any]]) -> Optional[pd.DataFrame]:
        """Convert data to pandas DataFrame for analysis."""
        try:
            if not data:
                return None
                
            # Handle different data formats
            if isinstance(data[0], dict) and "data" in data[0]:
                # SQL agent format
                result = data[0]
                columns = result.get("columns", [])
                rows = result.get("data", [])
                
                if not columns or not rows:
                    return None
                    
                df = pd.DataFrame(rows, columns=columns)
            else:
                # Direct data format
                df = pd.DataFrame(data)
            
            # Basic data cleaning and type conversion
            df = self._clean_and_convert_data(df)
            
            return df if not df.empty else None
            
        except Exception as e:
            logger.error(f"Error preparing data: {str(e)}")
            return None
    
    def _clean_and_convert_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and convert data types for analysis."""
        try:
            # Remove completely empty rows
            df = df.dropna(how='all')
            
            # Convert numeric strings to numbers
            for col in df.columns:
                if df[col].dtype == 'object':
                    # Try to convert to numeric
                    numeric_series = pd.to_numeric(df[col], errors='coerce')
                    if not numeric_series.isna().all():
                        df[col] = numeric_series
                    else:
                        # Try to convert to datetime
                        try:
                            datetime_series = pd.to_datetime(df[col], errors='coerce')
                            if not datetime_series.isna().all():
                                df[col] = datetime_series
                        except:
                            pass
            
            return df
            
        except Exception as e:
            logger.warning(f"Error in data cleaning: {str(e)}")
            return df
    
    def _detect_time_column(self, df: pd.DataFrame) -> Optional[str]:
        """Automatically detect the time column in the dataset."""
        try:
            # Look for datetime columns
            datetime_cols = df.select_dtypes(include=['datetime64']).columns
            if len(datetime_cols) > 0:
                return datetime_cols[0]
            
            # Look for columns with time-related names
            time_keywords = ['date', 'time', 'timestamp', 'created', 'updated', 'period']
            for col in df.columns:
                if any(keyword in col.lower() for keyword in time_keywords):
                    return col
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting time column: {str(e)}")
            return None
    
    def _detect_metric_columns(self, df: pd.DataFrame, time_column: Optional[str]) -> List[str]:
        """Automatically detect metric columns for analysis."""
        try:
            # Get numeric columns excluding the time column
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            
            if time_column and time_column in numeric_cols:
                numeric_cols.remove(time_column)
            
            # Filter out ID-like columns (high cardinality, likely identifiers)
            metric_cols = []
            for col in numeric_cols:
                unique_ratio = df[col].nunique() / len(df)
                if unique_ratio < 0.95:  # Not likely an ID column
                    metric_cols.append(col)
            
            return metric_cols
            
        except Exception as e:
            logger.error(f"Error detecting metric columns: {str(e)}")
            return []
    
    async def _perform_comparative_analysis(
        self,
        current_df: pd.DataFrame,
        comparison_df: Optional[pd.DataFrame],
        comparison_type: str,
        time_column: Optional[str],
        metric_columns: List[str]
    ) -> List[ComparativeInsight]:
        """Perform comparative analysis between current and comparison data."""
        insights = []
        
        try:
            if comparison_df is None:
                # Perform internal comparison (e.g., period-over-period within same dataset)
                insights = await self._internal_comparison(
                    current_df, comparison_type, time_column, metric_columns
                )
            else:
                # Perform external comparison between datasets
                insights = await self._external_comparison(
                    current_df, comparison_df, comparison_type, metric_columns
                )
            
            return insights
            
        except Exception as e:
            logger.error(f"Error performing comparative analysis: {str(e)}")
            return insights
    
    async def _internal_comparison(
        self,
        df: pd.DataFrame,
        comparison_type: str,
        time_column: Optional[str],
        metric_columns: List[str]
    ) -> List[ComparativeInsight]:
        """Perform comparison within the same dataset."""
        insights = []
        
        try:
            if not time_column or time_column not in df.columns:
                return insights
            
            # Sort by time column
            df_sorted = df.sort_values(time_column)
            
            # Split data into periods for comparison
            if comparison_type == "period_over_period":
                # Compare first half vs second half
                midpoint = len(df_sorted) // 2
                period1 = df_sorted.iloc[:midpoint]
                period2 = df_sorted.iloc[midpoint:]
                
                for metric in metric_columns:
                    if metric in df.columns:
                        insight = self._calculate_period_comparison(
                            period1[metric], period2[metric], metric, "first_half", "second_half"
                        )
                        if insight:
                            insights.append(insight)
            
            return insights

        except Exception as e:
            logger.error(f"Error in internal comparison: {str(e)}")
            return insights

    async def _external_comparison(
        self,
        current_df: pd.DataFrame,
        comparison_df: pd.DataFrame,
        comparison_type: str,
        metric_columns: List[str]
    ) -> List[ComparativeInsight]:
        """Perform comparison between two different datasets."""
        insights = []

        try:
            for metric in metric_columns:
                if metric in current_df.columns and metric in comparison_df.columns:
                    insight = self._calculate_period_comparison(
                        comparison_df[metric], current_df[metric], metric, "previous_period", "current_period"
                    )
                    if insight:
                        insights.append(insight)

            return insights

        except Exception as e:
            logger.error(f"Error in external comparison: {str(e)}")
            return insights

    def _calculate_period_comparison(
        self,
        period1_data: pd.Series,
        period2_data: pd.Series,
        metric_name: str,
        period1_name: str,
        period2_name: str
    ) -> Optional[ComparativeInsight]:
        """Calculate comparison metrics between two periods."""
        try:
            # Calculate summary statistics
            period1_mean = period1_data.mean()
            period2_mean = period2_data.mean()

            if pd.isna(period1_mean) or pd.isna(period2_mean) or period1_mean == 0:
                return None

            # Calculate change metrics
            change_amount = period2_mean - period1_mean
            change_percentage = (change_amount / period1_mean) * 100

            # Perform statistical significance test
            try:
                t_stat, p_value = stats.ttest_ind(period1_data.dropna(), period2_data.dropna())
                significance_level = p_value
            except:
                significance_level = 1.0  # No significance if test fails

            # Determine trend direction
            if abs(change_percentage) < self.material_change_threshold * 100:
                trend_direction = "stable"
            elif change_percentage > 0:
                trend_direction = "improving"
            else:
                trend_direction = "declining"

            # Assess business impact
            if abs(change_percentage) > 20:
                business_impact = "high"
            elif abs(change_percentage) > 10:
                business_impact = "medium"
            else:
                business_impact = "low"

            # Generate interpretation
            interpretation = self._generate_comparison_interpretation(
                metric_name, change_percentage, significance_level, trend_direction
            )

            return ComparativeInsight(
                comparison_id=f"comp_{metric_name}_{period1_name}_{period2_name}",
                comparison_type="period_comparison",
                metric_name=metric_name,
                current_value=float(period2_mean),
                comparison_value=float(period1_mean),
                change_amount=float(change_amount),
                change_percentage=float(change_percentage),
                significance_level=float(significance_level),
                interpretation=interpretation,
                business_impact=business_impact,
                trend_direction=trend_direction
            )

        except Exception as e:
            logger.error(f"Error calculating period comparison for {metric_name}: {str(e)}")
            return None

    def _generate_comparison_interpretation(
        self,
        metric_name: str,
        change_percentage: float,
        significance_level: float,
        trend_direction: str
    ) -> str:
        """Generate human-readable interpretation of the comparison."""

        # Determine significance
        if significance_level < 0.01:
            significance_text = "highly significant"
        elif significance_level < 0.05:
            significance_text = "significant"
        else:
            significance_text = "not statistically significant"

        # Generate interpretation
        if trend_direction == "stable":
            return f"{metric_name} remained relatively stable with minimal change ({significance_text})"
        elif trend_direction == "improving":
            return f"{metric_name} increased by {abs(change_percentage):.1f}% ({significance_text})"
        else:
            return f"{metric_name} decreased by {abs(change_percentage):.1f}% ({significance_text})"

    async def _perform_benchmarking(
        self,
        current_df: pd.DataFrame,
        benchmark_data: Dict[str, Any],
        metric_columns: List[str]
    ) -> List[BenchmarkResult]:
        """Perform benchmarking analysis against provided benchmarks."""
        benchmark_results = []

        try:
            for metric in metric_columns:
                if metric in current_df.columns and metric in benchmark_data:
                    current_value = current_df[metric].mean()
                    benchmark_value = benchmark_data[metric]

                    if not pd.isna(current_value) and benchmark_value is not None:
                        result = self._calculate_benchmark_result(
                            metric, current_value, benchmark_value
                        )
                        if result:
                            benchmark_results.append(result)

            return benchmark_results

        except Exception as e:
            logger.error(f"Error performing benchmarking: {str(e)}")
            return benchmark_results

    def _calculate_benchmark_result(
        self,
        metric_name: str,
        current_value: float,
        benchmark_value: float
    ) -> Optional[BenchmarkResult]:
        """Calculate benchmarking result for a single metric."""
        try:
            # Calculate performance ratio
            performance_ratio = current_value / benchmark_value if benchmark_value != 0 else 0

            # Determine performance rating
            if performance_ratio >= 1.2:
                performance_rating = "excellent"
            elif performance_ratio >= 1.1:
                performance_rating = "good"
            elif performance_ratio >= 0.9:
                performance_rating = "average"
            elif performance_ratio >= 0.8:
                performance_rating = "below_average"
            else:
                performance_rating = "poor"

            # Calculate improvement potential
            improvement_potential = max(0, benchmark_value - current_value)

            # Generate gap analysis
            gap_percentage = ((benchmark_value - current_value) / benchmark_value) * 100
            if gap_percentage > 0:
                gap_analysis = f"Performance is {gap_percentage:.1f}% below benchmark"
            else:
                gap_analysis = f"Performance exceeds benchmark by {abs(gap_percentage):.1f}%"

            return BenchmarkResult(
                metric_name=metric_name,
                current_value=current_value,
                benchmark_value=benchmark_value,
                performance_rating=performance_rating,
                percentile_rank=None,  # Could be calculated if distribution data available
                gap_analysis=gap_analysis,
                improvement_potential=improvement_potential
            )

        except Exception as e:
            logger.error(f"Error calculating benchmark result for {metric_name}: {str(e)}")
            return None

    async def _analyze_trends(
        self,
        df: pd.DataFrame,
        time_column: Optional[str],
        metric_columns: List[str]
    ) -> Dict[str, Any]:
        """Analyze trends in the data."""
        trend_analysis = {
            "overall_trend": "stable",
            "metric_trends": {},
            "trend_strength": 0.0,
            "trend_consistency": 0.0
        }

        try:
            if not time_column or time_column not in df.columns:
                return trend_analysis

            # Sort by time
            df_sorted = df.sort_values(time_column)

            trend_directions = []
            trend_strengths = []

            for metric in metric_columns:
                if metric in df.columns:
                    # Simple linear trend analysis
                    x = np.arange(len(df_sorted))
                    y = df_sorted[metric].values

                    # Remove NaN values
                    valid_indices = ~pd.isna(y)
                    if valid_indices.sum() < 3:
                        continue

                    x_clean = x[valid_indices]
                    y_clean = y[valid_indices]

                    # Calculate linear regression
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x_clean, y_clean)

                    # Determine trend direction
                    if p_value < 0.05:  # Significant trend
                        if slope > 0:
                            direction = "increasing"
                        else:
                            direction = "decreasing"
                        trend_directions.append(direction)
                        trend_strengths.append(abs(r_value))
                    else:
                        direction = "stable"
                        trend_directions.append(direction)
                        trend_strengths.append(0)

                    trend_analysis["metric_trends"][metric] = {
                        "direction": direction,
                        "strength": abs(r_value),
                        "slope": slope,
                        "p_value": p_value,
                        "r_squared": r_value ** 2
                    }

            # Calculate overall trend
            if trend_directions:
                # Most common trend direction
                from collections import Counter
                trend_counts = Counter(trend_directions)
                trend_analysis["overall_trend"] = trend_counts.most_common(1)[0][0]

                # Average trend strength
                trend_analysis["trend_strength"] = np.mean(trend_strengths)

                # Trend consistency (how many metrics follow the same trend)
                max_count = trend_counts.most_common(1)[0][1]
                trend_analysis["trend_consistency"] = max_count / len(trend_directions)

            return trend_analysis

        except Exception as e:
            logger.error(f"Error analyzing trends: {str(e)}")
            return trend_analysis

    async def _evaluate_performance(
        self,
        df: pd.DataFrame,
        targets: Dict[str, float],
        metric_columns: List[str]
    ) -> Dict[str, Any]:
        """Evaluate performance against targets."""
        performance_summary = {
            "targets_met": 0,
            "total_targets": 0,
            "performance_score": 0.0,
            "metric_performance": {}
        }

        try:
            for metric in metric_columns:
                if metric in df.columns and metric in targets:
                    current_value = df[metric].mean()
                    target_value = targets[metric]

                    if not pd.isna(current_value) and target_value is not None:
                        # Calculate performance ratio
                        performance_ratio = current_value / target_value if target_value != 0 else 0

                        # Determine if target is met (within 5% tolerance)
                        target_met = performance_ratio >= 0.95

                        performance_summary["metric_performance"][metric] = {
                            "current_value": current_value,
                            "target_value": target_value,
                            "performance_ratio": performance_ratio,
                            "target_met": target_met,
                            "gap": target_value - current_value
                        }

                        performance_summary["total_targets"] += 1
                        if target_met:
                            performance_summary["targets_met"] += 1

            # Calculate overall performance score
            if performance_summary["total_targets"] > 0:
                performance_summary["performance_score"] = (
                    performance_summary["targets_met"] / performance_summary["total_targets"]
                )

            return performance_summary

        except Exception as e:
            logger.error(f"Error evaluating performance: {str(e)}")
            return performance_summary

    async def _generate_comparative_recommendations(
        self,
        comparative_insights: List[ComparativeInsight],
        benchmark_results: List[BenchmarkResult],
        trend_analysis: Dict[str, Any],
        performance_summary: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate recommendations based on comparative analysis."""
        recommendations = []

        try:
            # Recommendations based on comparative insights
            high_impact_changes = [
                insight for insight in comparative_insights
                if insight.business_impact == "high"
            ]

            for insight in high_impact_changes:
                if insight.trend_direction == "declining":
                    recommendations.append({
                        "type": "performance_alert",
                        "priority": "high",
                        "title": f"Address Declining {insight.metric_name}",
                        "description": f"{insight.metric_name} has declined by {abs(insight.change_percentage):.1f}%",
                        "action": f"Investigate causes of decline in {insight.metric_name} and implement corrective measures"
                    })
                elif insight.trend_direction == "improving":
                    recommendations.append({
                        "type": "opportunity",
                        "priority": "medium",
                        "title": f"Leverage Improving {insight.metric_name}",
                        "description": f"{insight.metric_name} has improved by {insight.change_percentage:.1f}%",
                        "action": f"Analyze success factors for {insight.metric_name} improvement and replicate across other areas"
                    })

            # Recommendations based on benchmarking
            poor_performers = [
                result for result in benchmark_results
                if result.performance_rating in ["poor", "below_average"]
            ]

            for result in poor_performers:
                recommendations.append({
                    "type": "benchmark_improvement",
                    "priority": "high",
                    "title": f"Improve {result.metric_name} Performance",
                    "description": result.gap_analysis,
                    "action": f"Develop improvement plan for {result.metric_name} to reach benchmark levels"
                })

            # Recommendations based on trend analysis
            if trend_analysis.get("overall_trend") == "decreasing":
                recommendations.append({
                    "type": "trend_alert",
                    "priority": "high",
                    "title": "Address Overall Declining Trend",
                    "description": "Multiple metrics show declining trends",
                    "action": "Conduct comprehensive analysis to identify root causes of declining performance"
                })

            return recommendations

        except Exception as e:
            logger.error(f"Error generating comparative recommendations: {str(e)}")
            return recommendations
