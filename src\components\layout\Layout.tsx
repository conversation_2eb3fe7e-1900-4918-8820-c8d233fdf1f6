"use client";
import React, { ReactNode, useState, useRef, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useChatHistory } from "@/providers/ChatHistoryContext";
import { PageHeaderProvider } from '@/providers/PageHeaderContext';
import Header from './Header';
import Sidebar from './Sidebar';

interface LayoutProps {
  children: ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { setActiveChat } = useChatHistory();
  const router = useRouter();
  const pathname = usePathname();
  const [sidebarCollapsed, setSidebarCollapsed] = useState<boolean>(false);
  const [isCreatingNewChat, setIsCreatingNewChat] = useState<boolean>(false);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleNewChat = useCallback(async () => {
    // Prevent multiple simultaneous new chat creations
    if (isCreatingNewChat) {
      console.log('New chat creation already in progress, ignoring click');
      return;
    }

    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set debounce timeout to prevent rapid clicking
    debounceTimeoutRef.current = setTimeout(async () => {
      setIsCreatingNewChat(true);
      
      try {
        // Clear the active chat to show the welcome message
        await setActiveChat(null);
        
        // Navigate to the base chat route without a specific chat ID
        router.push('/chat');
      } catch (error) {
        console.error('Error creating new chat:', error);
      } finally {
        setIsCreatingNewChat(false);
      }
    }, 300); // 300ms debounce to prevent rapid clicking
  }, [isCreatingNewChat, setActiveChat, router]);

  const handleToggleCollapse = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed);
  };

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <PageHeaderProvider>
      <div className="flex h-screen bg-sidebar-bg" style={{ fontFamily: 'var(--font-inter), var(--font-noto-sans), sans-serif' }}>
      <Sidebar 
        onNewChat={handleNewChat} 
        onToggleCollapse={handleToggleCollapse}
        isCreatingNewChat={isCreatingNewChat}
      />
      <div className="flex flex-col flex-1 min-w-0">
        <Header />
          <main className="flex-1 overflow-y-auto bg-sidebar-bg">
          {children}
        </main>
      </div>
    </div>
    </PageHeaderProvider>
  );
};

export default Layout;