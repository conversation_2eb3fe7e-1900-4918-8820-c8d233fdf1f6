"""
Performance Monitoring Utility

This module provides performance monitoring and metrics collection
for the database query agent pipeline to track optimization improvements.
"""

import time
import logging
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric data."""
    name: str
    value: float
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceStats:
    """Aggregated performance statistics."""
    avg: float
    min: float
    max: float
    count: int
    p95: float
    p99: float


@dataclass
class BottleneckAnalysis:
    """Analysis of performance bottlenecks."""
    bottleneck_type: str
    severity: str  # "low", "medium", "high", "critical"
    metric_name: str
    current_value: float
    threshold: float
    impact_description: str
    recommendations: List[str]


@dataclass
class PipelineStageMetrics:
    """Metrics for a specific pipeline stage."""
    stage_name: str
    avg_duration: float
    percentage_of_total: float
    bottleneck_score: float  # 0-100, higher = more bottleneck
    parallel_efficiency: Optional[float] = None


class PerformanceMonitor:
    """Performance monitoring and metrics collection with advanced bottleneck detection."""

    def __init__(self, max_metrics_per_category: int = 1000):
        """Initialize the performance monitor.

        Args:
            max_metrics_per_category: Maximum metrics to keep per category
        """
        self.metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_metrics_per_category))
        self.active_timers: Dict[str, float] = {}
        self.counters: Dict[str, int] = defaultdict(int)

        # Performance thresholds for bottleneck detection
        self.performance_thresholds = {
            "sql_agent_total": 30.0,  # SQL agent should complete within 30s
            "sql_execution": 15.0,    # SQL execution should complete within 15s
            "sql_generation": 5.0,    # SQL generation should complete within 5s
            "orchestrator_total": 60.0,  # Total orchestrator time should be under 60s
            "database_connection": 2.0,   # Database connections should be under 2s
            "query_understanding": 10.0,  # Query understanding should be under 10s
            "schema_retrieval": 5.0,      # Schema retrieval should be under 5s
            "output_generation": 15.0,    # Output generation should be under 15s
        }

        # Pipeline stage mapping for bottleneck analysis
        self.pipeline_stages = [
            "query_understanding",
            "schema_retrieval",
            "sql_generation",
            "sql_execution",
            "data_analysis",
            "output_generation"
        ]
        
    @asynccontextmanager
    async def timer(self, metric_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Context manager for timing operations.
        
        Args:
            metric_name: Name of the metric to track
            metadata: Additional metadata to store with the metric
        """
        start_time = time.perf_counter()
        timer_id = f"{metric_name}_{id(asyncio.current_task())}"
        self.active_timers[timer_id] = start_time
        
        try:
            yield
        finally:
            end_time = time.perf_counter()
            duration = end_time - start_time
            
            self.record_metric(
                metric_name, 
                duration, 
                metadata or {}
            )
            
            self.active_timers.pop(timer_id, None)
    
    def record_metric(self, name: str, value: float, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Record a performance metric.
        
        Args:
            name: Metric name
            value: Metric value
            metadata: Additional metadata
        """
        metric = PerformanceMetric(
            name=name,
            value=value,
            timestamp=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        self.metrics[name].append(metric)
        logger.debug(f"Recorded metric {name}: {value:.4f}s")
    
    def increment_counter(self, name: str, value: int = 1) -> None:
        """Increment a counter metric.
        
        Args:
            name: Counter name
            value: Value to increment by
        """
        self.counters[name] += value
    
    def get_stats(self, metric_name: str, time_window: Optional[timedelta] = None) -> Optional[PerformanceStats]:
        """Get aggregated statistics for a metric.
        
        Args:
            metric_name: Name of the metric
            time_window: Optional time window to filter metrics
            
        Returns:
            Performance statistics or None if no data
        """
        if metric_name not in self.metrics:
            return None
        
        metrics = list(self.metrics[metric_name])
        
        # Filter by time window if specified
        if time_window:
            cutoff_time = datetime.utcnow() - time_window
            metrics = [m for m in metrics if m.timestamp >= cutoff_time]
        
        if not metrics:
            return None
        
        values = [m.value for m in metrics]
        values.sort()
        
        count = len(values)
        avg = sum(values) / count
        min_val = min(values)
        max_val = max(values)
        
        # Calculate percentiles
        p95_idx = int(0.95 * count)
        p99_idx = int(0.99 * count)
        p95 = values[min(p95_idx, count - 1)]
        p99 = values[min(p99_idx, count - 1)]
        
        return PerformanceStats(
            avg=avg,
            min=min_val,
            max=max_val,
            count=count,
            p95=p95,
            p99=p99
        )
    
    def get_counter(self, name: str) -> int:
        """Get counter value.
        
        Args:
            name: Counter name
            
        Returns:
            Counter value
        """
        return self.counters.get(name, 0)
    
    def get_all_metrics(self) -> Dict[str, List[PerformanceMetric]]:
        """Get all recorded metrics.
        
        Returns:
            Dictionary of all metrics
        """
        return {name: list(metrics) for name, metrics in self.metrics.items()}
    
    def get_performance_report(self, time_window: Optional[timedelta] = None) -> Dict[str, Any]:
        """Generate a comprehensive performance report.
        
        Args:
            time_window: Optional time window to filter metrics
            
        Returns:
            Performance report dictionary
        """
        report = {
            "timestamp": datetime.utcnow().isoformat(),
            "time_window": str(time_window) if time_window else "all_time",
            "metrics": {},
            "counters": dict(self.counters),
            "summary": {}
        }
        
        # Collect metric statistics
        for metric_name in self.metrics.keys():
            stats = self.get_stats(metric_name, time_window)
            if stats:
                report["metrics"][metric_name] = {
                    "avg": round(stats.avg, 4),
                    "min": round(stats.min, 4),
                    "max": round(stats.max, 4),
                    "count": stats.count,
                    "p95": round(stats.p95, 4),
                    "p99": round(stats.p99, 4)
                }
        
        # Generate summary insights
        if "database_query_total" in report["metrics"]:
            total_stats = report["metrics"]["database_query_total"]
            report["summary"]["avg_query_time"] = f"{total_stats['avg']:.2f}s"
            report["summary"]["p95_query_time"] = f"{total_stats['p95']:.2f}s"
            report["summary"]["total_queries"] = total_stats["count"]
        
        if "database_connection" in report["metrics"]:
            conn_stats = report["metrics"]["database_connection"]
            report["summary"]["avg_connection_time"] = f"{conn_stats['avg']:.3f}s"
        
        return report
    
    def clear_metrics(self, metric_name: Optional[str] = None) -> None:
        """Clear metrics.
        
        Args:
            metric_name: Specific metric to clear, or None to clear all
        """
        if metric_name:
            self.metrics.pop(metric_name, None)
        else:
            self.metrics.clear()
            self.counters.clear()
    
    def detect_bottlenecks(self, time_window: Optional[timedelta] = None) -> List[BottleneckAnalysis]:
        """Detect performance bottlenecks in the system.

        Args:
            time_window: Optional time window to analyze

        Returns:
            List of detected bottlenecks
        """
        bottlenecks = []

        for metric_name, threshold in self.performance_thresholds.items():
            stats = self.get_stats(metric_name, time_window)
            if not stats:
                continue

            # Check if average exceeds threshold
            if stats.avg > threshold:
                severity = self._calculate_severity(stats.avg, threshold)
                bottleneck = BottleneckAnalysis(
                    bottleneck_type="slow_operation",
                    severity=severity,
                    metric_name=metric_name,
                    current_value=stats.avg,
                    threshold=threshold,
                    impact_description=f"{metric_name} averaging {stats.avg:.2f}s (threshold: {threshold}s)",
                    recommendations=self._get_recommendations(metric_name, stats)
                )
                bottlenecks.append(bottleneck)

        # Check for high variance (inconsistent performance)
        for metric_name in self.metrics.keys():
            stats = self.get_stats(metric_name, time_window)
            if not stats or stats.count < 10:  # Need sufficient data
                continue

            variance_ratio = (stats.max - stats.min) / stats.avg if stats.avg > 0 else 0
            if variance_ratio > 3.0:  # High variance
                bottleneck = BottleneckAnalysis(
                    bottleneck_type="inconsistent_performance",
                    severity="medium",
                    metric_name=metric_name,
                    current_value=variance_ratio,
                    threshold=3.0,
                    impact_description=f"{metric_name} has inconsistent performance (variance ratio: {variance_ratio:.2f})",
                    recommendations=[
                        "Investigate resource contention",
                        "Check for memory leaks or garbage collection issues",
                        "Consider connection pooling optimization"
                    ]
                )
                bottlenecks.append(bottleneck)

        return sorted(bottlenecks, key=lambda x: self._severity_score(x.severity), reverse=True)

    def analyze_pipeline_stages(self, time_window: Optional[timedelta] = None) -> List[PipelineStageMetrics]:
        """Analyze performance of different pipeline stages.

        Args:
            time_window: Optional time window to analyze

        Returns:
            List of pipeline stage metrics
        """
        stage_metrics = []
        total_time = 0

        # Calculate total pipeline time and individual stage times
        stage_times = {}
        for stage in self.pipeline_stages:
            stats = self.get_stats(stage, time_window)
            if stats:
                stage_times[stage] = stats.avg
                total_time += stats.avg

        # Calculate metrics for each stage
        for stage, avg_time in stage_times.items():
            percentage = (avg_time / total_time * 100) if total_time > 0 else 0
            bottleneck_score = min(percentage * 2, 100)  # Higher percentage = higher bottleneck score

            stage_metric = PipelineStageMetrics(
                stage_name=stage,
                avg_duration=avg_time,
                percentage_of_total=percentage,
                bottleneck_score=bottleneck_score
            )
            stage_metrics.append(stage_metric)

        return sorted(stage_metrics, key=lambda x: x.bottleneck_score, reverse=True)

    def _calculate_severity(self, current_value: float, threshold: float) -> str:
        """Calculate severity based on how much the value exceeds the threshold."""
        ratio = current_value / threshold
        if ratio >= 3.0:
            return "critical"
        elif ratio >= 2.0:
            return "high"
        elif ratio >= 1.5:
            return "medium"
        else:
            return "low"

    def _severity_score(self, severity: str) -> int:
        """Convert severity to numeric score for sorting."""
        scores = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        return scores.get(severity, 0)

    def _get_recommendations(self, metric_name: str, stats: PerformanceStats) -> List[str]:
        """Get specific recommendations based on the metric and its performance."""
        recommendations = []

        if "sql_execution" in metric_name:
            recommendations.extend([
                "Review SQL query complexity and add appropriate indexes",
                "Consider query result caching for frequently accessed data",
                "Optimize database connection pooling settings",
                "Check for table locks or blocking queries"
            ])
        elif "sql_generation" in metric_name:
            recommendations.extend([
                "Implement SQL query template caching",
                "Optimize LLM prompt engineering for faster generation",
                "Consider pre-generating common query patterns"
            ])
        elif "database_connection" in metric_name:
            recommendations.extend([
                "Increase connection pool size",
                "Implement connection pre-warming",
                "Check network latency to database",
                "Consider connection multiplexing"
            ])
        elif "query_understanding" in metric_name:
            recommendations.extend([
                "Cache query analysis results for similar queries",
                "Optimize NLP model inference",
                "Implement query pattern recognition"
            ])
        elif "output_generation" in metric_name:
            recommendations.extend([
                "Implement response caching for similar queries",
                "Optimize streaming response generation",
                "Consider parallel processing for large result sets"
            ])
        else:
            recommendations.extend([
                "Profile the operation to identify specific bottlenecks",
                "Consider implementing caching mechanisms",
                "Review resource allocation and scaling options"
            ])

        return recommendations

    def log_performance_summary(self, time_window: Optional[timedelta] = None) -> None:
        """Log a comprehensive performance summary with bottleneck analysis.

        Args:
            time_window: Optional time window to filter metrics
        """
        report = self.get_performance_report(time_window)
        bottlenecks = self.detect_bottlenecks(time_window)
        pipeline_analysis = self.analyze_pipeline_stages(time_window)

        logger.info("=== Performance Summary ===")

        if report["summary"]:
            for key, value in report["summary"].items():
                logger.info(f"{key}: {value}")

        logger.info("=== Pipeline Stage Analysis ===")
        for stage in pipeline_analysis:
            logger.info(f"{stage.stage_name}: {stage.avg_duration:.2f}s ({stage.percentage_of_total:.1f}% of total, bottleneck score: {stage.bottleneck_score:.1f})")

        logger.info("=== Bottleneck Detection ===")
        if bottlenecks:
            for bottleneck in bottlenecks:
                logger.warning(f"[{bottleneck.severity.upper()}] {bottleneck.impact_description}")
                for rec in bottleneck.recommendations[:2]:  # Show top 2 recommendations
                    logger.info(f"  → {rec}")
        else:
            logger.info("No significant bottlenecks detected")

        logger.info("=== Detailed Metrics ===")
        for metric_name, stats in report["metrics"].items():
            logger.info(f"{metric_name}: avg={stats['avg']:.4f}s, p95={stats['p95']:.4f}s, count={stats['count']}")

        if report["counters"]:
            logger.info("=== Counters ===")
            for counter_name, value in report["counters"].items():
                logger.info(f"{counter_name}: {value}")


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


def monitor_performance(metric_name: str, metadata: Optional[Dict[str, Any]] = None):
    """Decorator for monitoring function performance.
    
    Args:
        metric_name: Name of the metric to track
        metadata: Additional metadata to store
    """
    def decorator(func: Callable):
        if asyncio.iscoroutinefunction(func):
            async def async_wrapper(*args, **kwargs):
                async with performance_monitor.timer(metric_name, metadata):
                    return await func(*args, **kwargs)
            return async_wrapper
        else:
            def sync_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    duration = time.perf_counter() - start_time
                    performance_monitor.record_metric(metric_name, duration, metadata or {})
            return sync_wrapper
    return decorator
