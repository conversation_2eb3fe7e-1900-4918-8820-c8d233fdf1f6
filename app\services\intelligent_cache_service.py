"""
Intelligent Cache Service

This service provides advanced caching mechanisms for the database query pipeline,
including semantic query caching, result set caching, and intelligent cache invalidation.
"""

import asyncio
import hashlib
import json
import logging
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, OrderedDict
import threading

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry with metadata."""
    key: str
    value: Any
    created_at: datetime
    last_accessed: datetime
    access_count: int
    ttl_seconds: int
    size_bytes: int
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """Check if the cache entry is expired."""
        return datetime.utcnow() > self.created_at + timedelta(seconds=self.ttl_seconds)
    
    def update_access(self) -> None:
        """Update access statistics."""
        self.last_accessed = datetime.utcnow()
        self.access_count += 1


@dataclass
class CacheStats:
    """Cache performance statistics."""
    total_entries: int
    total_size_bytes: int
    hit_rate: float
    miss_rate: float
    eviction_count: int
    avg_access_time_ms: float


class LRUCache:
    """Thread-safe LRU cache with TTL support."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        """Initialize LRU cache.
        
        Args:
            max_size: Maximum number of entries
            default_ttl: Default TTL in seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.lock = threading.RLock()
        self.stats = {
            "hits": 0,
            "misses": 0,
            "evictions": 0,
            "total_access_time": 0.0,
            "access_count": 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        start_time = time.perf_counter()
        
        with self.lock:
            if key not in self.cache:
                self.stats["misses"] += 1
                return None
            
            entry = self.cache[key]
            
            # Check if expired
            if entry.is_expired():
                del self.cache[key]
                self.stats["misses"] += 1
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            entry.update_access()
            
            self.stats["hits"] += 1
            self.stats["total_access_time"] += (time.perf_counter() - start_time) * 1000
            self.stats["access_count"] += 1
            
            return entry.value
    
    def put(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Put value in cache."""
        with self.lock:
            ttl = ttl or self.default_ttl
            size_bytes = self._estimate_size(value)
            
            entry = CacheEntry(
                key=key,
                value=value,
                created_at=datetime.utcnow(),
                last_accessed=datetime.utcnow(),
                access_count=0,
                ttl_seconds=ttl,
                size_bytes=size_bytes
            )
            
            # Remove existing entry if present
            if key in self.cache:
                del self.cache[key]
            
            # Add new entry
            self.cache[key] = entry
            
            # Evict if necessary
            while len(self.cache) > self.max_size:
                oldest_key = next(iter(self.cache))
                del self.cache[oldest_key]
                self.stats["evictions"] += 1
    
    def _estimate_size(self, value: Any) -> int:
        """Estimate size of value in bytes."""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (dict, list)):
                return len(json.dumps(value, default=str).encode('utf-8'))
            else:
                return len(str(value).encode('utf-8'))
        except:
            return 1024  # Default estimate
    
    def get_stats(self) -> CacheStats:
        """Get cache statistics."""
        with self.lock:
            total_hits = self.stats["hits"]
            total_misses = self.stats["misses"]
            total_requests = total_hits + total_misses
            
            hit_rate = total_hits / total_requests if total_requests > 0 else 0.0
            miss_rate = total_misses / total_requests if total_requests > 0 else 0.0
            
            avg_access_time = (
                self.stats["total_access_time"] / self.stats["access_count"]
                if self.stats["access_count"] > 0 else 0.0
            )
            
            total_size = sum(entry.size_bytes for entry in self.cache.values())
            
            return CacheStats(
                total_entries=len(self.cache),
                total_size_bytes=total_size,
                hit_rate=hit_rate,
                miss_rate=miss_rate,
                eviction_count=self.stats["evictions"],
                avg_access_time_ms=avg_access_time
            )
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()


class IntelligentCacheService:
    """Intelligent caching service with multiple cache layers."""
    
    def __init__(self):
        """Initialize the intelligent cache service."""
        # Multiple cache layers for different types of data
        self.query_cache = LRUCache(max_size=500, default_ttl=600)  # 10 minutes
        self.sql_cache = LRUCache(max_size=1000, default_ttl=1800)  # 30 minutes
        self.schema_cache = LRUCache(max_size=200, default_ttl=3600)  # 1 hour
        self.result_cache = LRUCache(max_size=300, default_ttl=300)  # 5 minutes
        
        # Semantic similarity tracking for intelligent caching
        self.query_patterns: Dict[str, List[str]] = defaultdict(list)
        self.pattern_lock = threading.Lock()
    
    def generate_semantic_key(self, query: str, context: Dict[str, Any]) -> str:
        """Generate a semantic cache key that captures query intent."""
        # Normalize query for semantic similarity
        normalized_query = self._normalize_query(query)
        
        # Include relevant context
        context_str = json.dumps({
            "databases": sorted(context.get("target_databases", [])),
            "tables": dict(sorted(context.get("target_tables", {}).items())),
            "columns": dict(sorted(context.get("target_columns", {}).items()))
        }, sort_keys=True)
        
        # Create semantic key
        semantic_data = f"{normalized_query}|{context_str}"
        return hashlib.sha256(semantic_data.encode()).hexdigest()
    
    def _normalize_query(self, query: str) -> str:
        """Normalize query for semantic comparison."""
        # Convert to lowercase and remove extra whitespace
        normalized = " ".join(query.lower().split())
        
        # Remove common variations that don't change semantic meaning
        replacements = {
            "show me": "show",
            "give me": "show",
            "find": "show",
            "get": "show",
            "list": "show",
            "display": "show"
        }
        
        for old, new in replacements.items():
            normalized = normalized.replace(old, new)
        
        return normalized
    
    async def get_query_result(self, query: str, context: Dict[str, Any]) -> Optional[Any]:
        """Get cached query result with semantic matching."""
        # Try exact semantic match first
        semantic_key = self.generate_semantic_key(query, context)
        result = self.query_cache.get(semantic_key)
        
        if result is not None:
            logger.debug(f"Cache hit for query: {query[:50]}...")
            return result
        
        # Try pattern-based matching for similar queries
        normalized_query = self._normalize_query(query)
        similar_result = await self._find_similar_cached_query(normalized_query, context)
        
        if similar_result is not None:
            logger.debug(f"Similar query cache hit for: {query[:50]}...")
            # Cache this query with the similar result
            self.cache_query_result(query, context, similar_result, ttl=300)  # Shorter TTL for similar matches
            return similar_result
        
        return None
    
    async def _find_similar_cached_query(self, normalized_query: str, context: Dict[str, Any]) -> Optional[Any]:
        """Find similar cached queries using pattern matching."""
        with self.pattern_lock:
            # Simple similarity check based on common words
            query_words = set(normalized_query.split())
            
            for pattern, cached_keys in self.query_patterns.items():
                pattern_words = set(pattern.split())
                
                # Calculate Jaccard similarity
                intersection = len(query_words.intersection(pattern_words))
                union = len(query_words.union(pattern_words))
                similarity = intersection / union if union > 0 else 0
                
                # If similarity is high enough, try to use cached result
                if similarity > 0.7:  # 70% similarity threshold
                    for cached_key in cached_keys:
                        result = self.query_cache.get(cached_key)
                        if result is not None:
                            return result
        
        return None
    
    def cache_query_result(self, query: str, context: Dict[str, Any], result: Any, ttl: Optional[int] = None) -> None:
        """Cache query result with semantic indexing."""
        semantic_key = self.generate_semantic_key(query, context)
        self.query_cache.put(semantic_key, result, ttl)
        
        # Update pattern tracking
        normalized_query = self._normalize_query(query)
        with self.pattern_lock:
            self.query_patterns[normalized_query].append(semantic_key)
            
            # Limit pattern tracking to prevent memory growth
            if len(self.query_patterns[normalized_query]) > 10:
                self.query_patterns[normalized_query] = self.query_patterns[normalized_query][-5:]
    
    def get_sql_query(self, query_signature: str) -> Optional[str]:
        """Get cached SQL query."""
        return self.sql_cache.get(query_signature)
    
    def cache_sql_query(self, query_signature: str, sql: str, ttl: Optional[int] = None) -> None:
        """Cache generated SQL query."""
        self.sql_cache.put(query_signature, sql, ttl)
    
    def get_schema_info(self, database_id: str, table_name: Optional[str] = None) -> Optional[Any]:
        """Get cached schema information."""
        cache_key = f"{database_id}:{table_name or 'all_tables'}"
        return self.schema_cache.get(cache_key)
    
    def cache_schema_info(self, database_id: str, schema_info: Any, table_name: Optional[str] = None, ttl: Optional[int] = None) -> None:
        """Cache schema information."""
        cache_key = f"{database_id}:{table_name or 'all_tables'}"
        self.schema_cache.put(cache_key, schema_info, ttl)
    
    def get_result_data(self, result_signature: str) -> Optional[Any]:
        """Get cached result data."""
        return self.result_cache.get(result_signature)
    
    def cache_result_data(self, result_signature: str, data: Any, ttl: Optional[int] = None) -> None:
        """Cache result data."""
        self.result_cache.put(result_signature, data, ttl)
    
    def invalidate_cache(self, cache_type: str, pattern: Optional[str] = None) -> None:
        """Invalidate cache entries."""
        cache_map = {
            "query": self.query_cache,
            "sql": self.sql_cache,
            "schema": self.schema_cache,
            "result": self.result_cache
        }
        
        if cache_type in cache_map:
            if pattern is None:
                cache_map[cache_type].clear()
            else:
                # Pattern-based invalidation would require more complex implementation
                logger.warning(f"Pattern-based invalidation not implemented for {cache_type}")
    
    def get_cache_statistics(self) -> Dict[str, CacheStats]:
        """Get statistics for all cache layers."""
        return {
            "query_cache": self.query_cache.get_stats(),
            "sql_cache": self.sql_cache.get_stats(),
            "schema_cache": self.schema_cache.get_stats(),
            "result_cache": self.result_cache.get_stats()
        }
    
    def log_cache_performance(self) -> None:
        """Log cache performance statistics."""
        stats = self.get_cache_statistics()
        
        logger.info("=== Cache Performance Statistics ===")
        for cache_name, cache_stats in stats.items():
            logger.info(f"{cache_name}:")
            logger.info(f"  Entries: {cache_stats.total_entries}")
            logger.info(f"  Size: {cache_stats.total_size_bytes / 1024:.1f} KB")
            logger.info(f"  Hit Rate: {cache_stats.hit_rate:.2%}")
            logger.info(f"  Avg Access Time: {cache_stats.avg_access_time_ms:.2f}ms")
            logger.info(f"  Evictions: {cache_stats.eviction_count}")


# Global cache service instance
intelligent_cache_service = IntelligentCacheService()
