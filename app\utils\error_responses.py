"""Error Response Utilities

This module provides standardized error response handling for the authentication system.
"""

import uuid
from enum import Enum
from typing import Any, Dict, Optional

from fastapi import Request
from fastapi.responses import JSONResponse


class ErrorCode(str, Enum):
    """Standardized error codes for the authentication system."""
    
    # Authentication errors
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    USER_NOT_FOUND = "USER_NOT_FOUND"
    USER_INACTIVE = "USER_INACTIVE"
    EMAIL_NOT_VERIFIED = "EMAIL_NOT_VERIFIED"
    
    # Token errors
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    TOKEN_INVALID = "TOKEN_INVALID"
    TOKEN_REVOKED = "TOKEN_REVOKED"
    TOKEN_NOT_FOUND = "TOKEN_NOT_FOUND"
    TOKEN_BLACKLISTED = "TOKEN_BLACKLISTED"
    TOKEN_ROTATION_FAILED = "TOKEN_ROTATION_FAILED"
    CONCURRENT_TOKEN_USE = "CONCURRENT_TOKEN_USE"
    
    # OAuth errors
    OAUTH_PROVIDER_ERROR = "OAUTH_PROVIDER_ERROR"
    OAUTH_STATE_MISMATCH = "OAUTH_STATE_MISMATCH"
    OAUTH_CODE_INVALID = "OAUTH_CODE_INVALID"
    
    # Validation errors
    VALIDATION_ERROR = "VALIDATION_ERROR"
    MISSING_REQUIRED_FIELD = "MISSING_REQUIRED_FIELD"
    INVALID_INPUT_FORMAT = "INVALID_INPUT_FORMAT"
    EMAIL_ALREADY_EXISTS = "EMAIL_ALREADY_EXISTS"
    
    # Rate limiting
    RATE_LIMIT_EXCEEDED = "RATE_LIMIT_EXCEEDED"
    
    # Server errors
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    DATABASE_ERROR = "DATABASE_ERROR"


def get_correlation_id(request: Request) -> str:
    """Extract or generate a correlation ID from the request.
    
    Args:
        request: FastAPI request object
        
    Returns:
        str: Correlation ID for request tracking
    """
    # Try to get correlation ID from headers
    correlation_id = (
        request.headers.get("X-Correlation-ID") or
        request.headers.get("X-Request-ID") or
        request.headers.get("X-Trace-ID") or
        str(uuid.uuid4())
    )
    
    return correlation_id


class AuthErrorResponse:
    """Utility class for creating standardized authentication error responses."""
    
    @staticmethod
    def create_error_response(
        error_code: ErrorCode,
        message: str,
        status_code: int,
        correlation_id: str,
        request: Request,
        details: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """Create a standardized error response.
        
        Args:
            error_code: The error code
            message: Human-readable error message
            status_code: HTTP status code
            correlation_id: Request correlation ID
            request: FastAPI request object
            details: Additional error details
            
        Returns:
            JSONResponse: Standardized error response
        """
        error_response = {
            "error": {
                "code": error_code.value,
                "message": message,
                "correlation_id": correlation_id,
                "timestamp": str(uuid.uuid4()),  # Using UUID as timestamp placeholder
                "path": request.url.path
            }
        }
        
        if details:
            error_response["error"]["details"] = details
        
        return JSONResponse(
            status_code=status_code,
            content=error_response,
            headers={"X-Correlation-ID": correlation_id}
        )
    
    @staticmethod
    def from_exception(
        exception: Exception,
        request: Request,
        correlation_id: str,
        status_code: int = 500
    ) -> JSONResponse:
        """Create an error response from an exception.
        
        Args:
            exception: The exception that occurred
            request: FastAPI request object
            correlation_id: Request correlation ID
            status_code: HTTP status code (default: 500)
            
        Returns:
            JSONResponse: Standardized error response
        """
        from app.utils.exceptions import (
            TokenExpiredError,
            TokenRevokedError,
            TokenBlacklistedError,
            TokenNotFoundError,
            UserNotFoundError,
            UserInactiveError,
            TokenRotationError,
            ConcurrentTokenUseError,
            InvalidTokenError,
            EmailAlreadyExistsError
        )
        
        # Map exceptions to error codes and status codes
        exception_mapping = {
            TokenExpiredError: (ErrorCode.TOKEN_EXPIRED, 401),
            TokenRevokedError: (ErrorCode.TOKEN_REVOKED, 401),
            TokenBlacklistedError: (ErrorCode.TOKEN_BLACKLISTED, 401),
            TokenNotFoundError: (ErrorCode.TOKEN_NOT_FOUND, 401),
            UserNotFoundError: (ErrorCode.USER_NOT_FOUND, 401),
            UserInactiveError: (ErrorCode.USER_INACTIVE, 401),
            TokenRotationError: (ErrorCode.TOKEN_ROTATION_FAILED, 500),
            ConcurrentTokenUseError: (ErrorCode.CONCURRENT_TOKEN_USE, 401),
            InvalidTokenError: (ErrorCode.TOKEN_INVALID, 401),
            EmailAlreadyExistsError: (ErrorCode.EMAIL_ALREADY_EXISTS, 409),
        }
        
        # Get error code and status from mapping
        error_code, mapped_status = exception_mapping.get(
            type(exception),
            (ErrorCode.INTERNAL_SERVER_ERROR, status_code)
        )
        
        return AuthErrorResponse.create_error_response(
            error_code=error_code,
            message=str(exception),
            status_code=mapped_status,
            correlation_id=correlation_id,
            request=request,
            details={"exception_type": type(exception).__name__}
        )


class ClientResponseHandler:
    """Handler for client-specific response formatting."""
    
    @staticmethod
    def format_response(
        data: Dict[str, Any],
        client_type: str,
        correlation_id: str
    ) -> Dict[str, Any]:
        """Format response based on client type.
        
        Args:
            data: Response data
            client_type: Type of client (spa, mobile, api, browser)
            correlation_id: Request correlation ID
            
        Returns:
            Dict[str, Any]: Formatted response
        """
        response = {
            "data": data,
            "meta": {
                "client_type": client_type,
                "correlation_id": correlation_id,
                "timestamp": str(uuid.uuid4())  # Using UUID as timestamp placeholder
            }
        }
        
        # Add client-specific metadata
        if client_type == "spa":
            response["meta"]["spa_optimized"] = True
        elif client_type == "mobile":
            response["meta"]["mobile_optimized"] = True
        elif client_type == "api":
            response["meta"]["api_version"] = "v1"
        
        return response
    
    @staticmethod
    def format_token_response(
        token_data: Dict[str, Any],
        client_type: str,
        correlation_id: str
    ) -> Dict[str, Any]:
        """Format token response for different client types.
        
        Args:
            token_data: Token response data
            client_type: Type of client
            correlation_id: Request correlation ID
            
        Returns:
            Dict[str, Any]: Formatted token response
        """
        if client_type in ["spa", "mobile", "api"]:
            # JSON response for programmatic clients
            return {
                "access_token": token_data.get("access_token"),
                "refresh_token": token_data.get("refresh_token"),
                "token_type": token_data.get("token_type", "bearer"),
                "expires_in": token_data.get("expires_in", 3600),
                "user_id": token_data.get("user_id"),
                "client_type": client_type,
                "correlation_id": correlation_id
            }
        else:
            # Standard response for browsers
            return token_data


def create_success_response(
    data: Any,
    message: str = "Success",
    correlation_id: Optional[str] = None
) -> Dict[str, Any]:
    """Create a standardized success response.
    
    Args:
        data: Response data
        message: Success message
        correlation_id: Request correlation ID
        
    Returns:
        Dict[str, Any]: Standardized success response
    """
    response = {
        "success": True,
        "message": message,
        "data": data
    }
    
    if correlation_id:
        response["correlation_id"] = correlation_id
    
    return response


def create_validation_error_response(
    field_errors: Dict[str, str],
    correlation_id: str,
    request: Request
) -> JSONResponse:
    """Create a validation error response.
    
    Args:
        field_errors: Dictionary of field validation errors
        correlation_id: Request correlation ID
        request: FastAPI request object
        
    Returns:
        JSONResponse: Validation error response
    """
    return AuthErrorResponse.create_error_response(
        error_code=ErrorCode.VALIDATION_ERROR,
        message="Validation failed",
        status_code=422,
        correlation_id=correlation_id,
        request=request,
        details={"field_errors": field_errors}
    )
