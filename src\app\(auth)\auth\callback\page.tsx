'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/providers/AuthContext';
import { STORAGE_KEYS } from '@/lib/constants';

// Separate component that uses useSearchParams
function OAuthCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { setUser } = useAuth();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const handleCallback = async () => {
      try {
        console.log('OAuth callback page loaded');
        console.log('Current URL:', window.location.href);
        console.log('Search params:', Object.fromEntries(searchParams.entries()));

        // Extract tokens from URL parameters (sent by your backend after OAuth)
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const userId = searchParams.get('user_id');
        const expiresAt = searchParams.get('expires_at');
        const tokenType = searchParams.get('token_type') || 'bearer';

        if (accessToken && userId) {
          console.log('✅ Found tokens in URL params - OAuth successful!');

          // Store tokens in localStorage (same as your existing login)
          localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, accessToken);
          localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken || '');
          localStorage.setItem(STORAGE_KEYS.USER_ID, userId);
          localStorage.setItem(STORAGE_KEYS.EXPIRES_AT, expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString());
          localStorage.setItem(STORAGE_KEYS.TOKEN_TYPE, tokenType);

          console.log('✅ Tokens stored in localStorage');

          // Create token data for auth context
          const tokenData = {
            access_token: accessToken,
            token_type: tokenType,
            user_id: userId,
            expires_at: expiresAt || new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            refresh_token: refreshToken || ''
          };

          // Update auth context
          setUser(tokenData);
          setStatus('success');

          console.log('✅ User authenticated, redirecting to dashboard...');

          // Redirect to dashboard
          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
          return;
        }

        // If no tokens found, there was an error
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        if (error) {
          throw new Error(`OAuth Error: ${error} - ${errorDescription || 'Unknown error'}`);
        }

        // If we get here, something went wrong
        throw new Error('No tokens received from OAuth callback. Please check your backend configuration.');
        
      } catch (err: any) {
        console.error('OAuth callback error:', err);
        setError(err.message || 'Authentication failed');
        setStatus('error');
        
        // Redirect to login after error
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      }
    };

    handleCallback();
  }, [searchParams, router, setUser]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          {status === 'loading' && (
            <>
              <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900">Completing sign in...</h2>
              <p className="text-gray-600 mt-2">Please wait while we authenticate you with Google.</p>
            </>
          )}
          
          {status === 'success' && (
            <>
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Sign in successful!</h2>
              <p className="text-gray-600 mt-2">Redirecting to dashboard...</p>
            </>
          )}
          
          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-gray-900">Authentication failed</h2>
              <p className="text-gray-600 mt-2">{error}</p>
              <p className="text-sm text-gray-500 mt-2">Redirecting to login page...</p>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function CallbackLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900">Loading...</h2>
          <p className="text-gray-600 mt-2">Preparing authentication...</p>
        </div>
      </div>
    </div>
  );
}

// Main component with Suspense boundary
export default function OAuthCallback() {
  return (
    <Suspense fallback={<CallbackLoading />}>
      <OAuthCallbackContent />
    </Suspense>
  );
}
